/**
 * 폼 검증 유틸리티
 * 
 * 여러 페이지에서 사용되는 공통 검증 로직을 모아둔 유틸리티입니다.
 */

/**
 * 이메일 형식 검증
 * @param email 검증할 이메일 주소
 * @returns 유효한 이메일인지 여부
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * 비밀번호 복잡성 검증
 * @param password 검증할 비밀번호
 * @returns 검증 결과 객체
 */
export const validatePassword = (password: string) => {
  const minLength = 6;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  const checks = {
    minLength: password.length >= minLength,
    hasUpperCase,
    hasLowerCase,
    hasNumbers,
    hasSpecialChar,
  };

  const isValid = Object.values(checks).every(Boolean);

  return {
    isValid,
    checks,
    message: isValid ? "" : getPasswordErrorMessage(checks),
  };
};

/**
 * 비밀번호 오류 메시지 생성
 */
const getPasswordErrorMessage = (checks: Record<string, boolean>): string => {
  if (!checks.minLength) {
    return "비밀번호는 최소 6자 이상이어야 합니다.";
  }
  if (!checks.hasUpperCase || !checks.hasLowerCase) {
    return "비밀번호는 대문자와 소문자를 포함해야 합니다.";
  }
  if (!checks.hasNumbers) {
    return "비밀번호는 숫자를 포함해야 합니다.";
  }
  if (!checks.hasSpecialChar) {
    return "비밀번호는 특수문자를 포함해야 합니다.";
  }
  return "";
};

/**
 * 한국 휴대폰 번호 검증
 * @param phone 검증할 전화번호
 * @returns 유효한 전화번호인지 여부
 */
export const validatePhone = (phone: string): boolean => {
  // 010-XXXX-XXXX 형식
  const phoneRegex = /^010-\d{4}-\d{4}$/;
  return phoneRegex.test(phone);
};

/**
 * 휴대폰 번호 형식 자동 변환
 * @param phone 입력된 전화번호
 * @returns 형식이 맞춰진 전화번호
 */
export const formatPhone = (phone: string): string => {
  // 숫자만 추출
  const numbers = phone.replace(/\D/g, "");
  
  // 010으로 시작하는 11자리 숫자인 경우 형식 적용
  if (numbers.length === 11 && numbers.startsWith("010")) {
    return `${numbers.slice(0, 3)}-${numbers.slice(3, 7)}-${numbers.slice(7)}`;
  }
  
  return phone;
};

/**
 * 이름 검증
 * @param name 검증할 이름
 * @param minLength 최소 길이 (기본값: 2)
 * @param maxLength 최대 길이 (기본값: 50)
 * @returns 검증 결과
 */
export const validateName = (
  name: string, 
  minLength: number = 2, 
  maxLength: number = 50
) => {
  const trimmedName = name.trim();
  
  if (trimmedName.length < minLength) {
    return {
      isValid: false,
      message: `이름은 최소 ${minLength}자 이상이어야 합니다.`,
    };
  }
  
  if (trimmedName.length > maxLength) {
    return {
      isValid: false,
      message: `이름은 최대 ${maxLength}자까지 가능합니다.`,
    };
  }
  
  return {
    isValid: true,
    message: "",
  };
};

/**
 * 주소 검증
 * @param address 검증할 주소
 * @param minLength 최소 길이 (기본값: 5)
 * @param maxLength 최대 길이 (기본값: 200)
 * @returns 검증 결과
 */
export const validateAddress = (
  address: string,
  minLength: number = 5,
  maxLength: number = 200
) => {
  const trimmedAddress = address.trim();
  
  if (trimmedAddress.length < minLength) {
    return {
      isValid: false,
      message: "주소를 입력해주세요.",
    };
  }
  
  if (trimmedAddress.length > maxLength) {
    return {
      isValid: false,
      message: "주소가 너무 깁니다.",
    };
  }
  
  return {
    isValid: true,
    message: "",
  };
};

/**
 * 비밀번호 확인 검증
 * @param password 원본 비밀번호
 * @param confirmPassword 확인 비밀번호
 * @returns 검증 결과
 */
export const validatePasswordConfirm = (
  password: string,
  confirmPassword: string
) => {
  if (password !== confirmPassword) {
    return {
      isValid: false,
      message: "비밀번호가 일치하지 않습니다.",
    };
  }
  
  return {
    isValid: true,
    message: "",
  };
};

/**
 * 필수 필드 검증
 * @param value 검증할 값
 * @param fieldName 필드명
 * @returns 검증 결과
 */
export const validateRequired = (value: string, fieldName: string) => {
  if (!value || value.trim().length === 0) {
    return {
      isValid: false,
      message: `${fieldName}을(를) 입력해주세요.`,
    };
  }
  
  return {
    isValid: true,
    message: "",
  };
};

/**
 * 차량번호 검증 (한국 차량번호 형식)
 * @param vehicleNumber 검증할 차량번호
 * @returns 검증 결과
 */
export const validateVehicleNumber = (vehicleNumber: string) => {
  if (!vehicleNumber || vehicleNumber.trim().length === 0) {
    return {
      isValid: true, // 차량번호는 선택사항
      message: "",
    };
  }
  
  // 한국 차량번호 패턴 (예: 12가1234, 123가1234)
  const vehicleRegex = /^\d{2,3}[가-힣]\d{4}$/;
  
  if (!vehicleRegex.test(vehicleNumber.trim())) {
    return {
      isValid: false,
      message: "올바른 차량번호 형식을 입력해주세요. (예: 12가1234)",
    };
  }
  
  return {
    isValid: true,
    message: "",
  };
};

/**
 * 날짜 범위 검증
 * @param startDate 시작 날짜
 * @param endDate 종료 날짜
 * @returns 검증 결과
 */
export const validateDateRange = (startDate: string, endDate: string) => {
  if (!startDate || !endDate) {
    return {
      isValid: false,
      message: "시작 날짜와 종료 날짜를 모두 선택해주세요.",
    };
  }
  
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  if (start > end) {
    return {
      isValid: false,
      message: "시작 날짜는 종료 날짜보다 이전이어야 합니다.",
    };
  }
  
  // 최대 5년 범위 제한
  const maxYears = 5;
  const maxDays = maxYears * 365;
  const diffDays = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffDays > maxDays) {
    return {
      isValid: false,
      message: `날짜 범위는 최대 ${maxYears}년까지 선택 가능합니다.`,
    };
  }
  
  return {
    isValid: true,
    message: "",
  };
};

/**
 * 폼 전체 검증 헬퍼
 * @param validations 검증 함수들의 배열
 * @returns 전체 검증 결과
 */
export const validateForm = (validations: Array<() => { isValid: boolean; message: string; field?: string }>) => {
  const errors: Record<string, string> = {};
  let isValid = true;
  
  validations.forEach((validation) => {
    const result = validation();
    if (!result.isValid) {
      isValid = false;
      if (result.field) {
        errors[result.field] = result.message;
      }
    }
  });
  
  return {
    isValid,
    errors,
  };
};
