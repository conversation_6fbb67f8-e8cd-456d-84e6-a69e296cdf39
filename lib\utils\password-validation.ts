// 비밀번호 검증 결과 타입
export interface PasswordValidationResult {
  isValid: boolean;
  errors: string[];
}

// 비밀번호 검증 규칙 타입
export interface PasswordValidationRules {
  minLength?: number; // 최소 길이
  maxLength?: number; // 최대 길이
  requireNumbers?: boolean; // 숫자 포함
  requireUppercase?: boolean; // 대문자 포함
  requireLowercase?: boolean; // 소문자 포함
  requireSpecial?: boolean; // 특수문자 포함
  disallowSpaces?: boolean; // 공백 금지
}

// 기본 비밀번호 검증 규칙
export const DEFAULT_PASSWORD_RULES: PasswordValidationRules = {
  minLength: 8,
  maxLength: 50,
  requireNumbers: true,
  requireUppercase: true,
  requireLowercase: true,
  requireSpecial: true,
  disallowSpaces: true,
};

/**
 * 비밀번호 검증 함수
 * @param password 검증할 비밀번호
 * @param rules 검증 규칙 (선택적)
 * @returns PasswordValidationResult 검증 결과
 */
export function validatePassword(
  password: string,
  rules: PasswordValidationRules = DEFAULT_PASSWORD_RULES
): PasswordValidationResult {
  const errors: string[] = [];

  // 최소 길이 검증
  if (rules.minLength && password.length < rules.minLength) {
    errors.push(`비밀번호는 최소 ${rules.minLength}자 이상이어야 합니다.`);
  }

  // 최대 길이 검증
  if (rules.maxLength && password.length > rules.maxLength) {
    errors.push(`비밀번호는 최대 ${rules.maxLength}자 이하여야 합니다.`);
  }

  // 숫자 포함 검증
  if (rules.requireNumbers && !/\d/.test(password)) {
    errors.push("비밀번호는 숫자를 포함해야 합니다.");
  }

  // 대문자 포함 검증
  if (rules.requireUppercase && !/[A-Z]/.test(password)) {
    errors.push("비밀번호는 대문자를 포함해야 합니다.");
  }

  // 소문자 포함 검증
  if (rules.requireLowercase && !/[a-z]/.test(password)) {
    errors.push("비밀번호는 소문자를 포함해야 합니다.");
  }

  // 특수문자 포함 검증
  if (rules.requireSpecial && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push("비밀번호는 특수문자를 포함해야 합니다.");
  }

  // 공백 금지 검증
  if (rules.disallowSpaces && /\s/.test(password)) {
    errors.push("비밀번호에 공백을 포함할 수 없습니다.");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * 비밀번호 일치 여부 검증
 * @param password 비밀번호
 * @param confirmPassword 확인 비밀번호
 * @returns PasswordValidationResult 검증 결과
 */
export function validatePasswordMatch(
  password: string,
  confirmPassword: string
): PasswordValidationResult {
  return {
    isValid: password === confirmPassword,
    errors:
      password === confirmPassword ? [] : ["비밀번호가 일치하지 않습니다."],
  };
}

/**
 * 비밀번호 강도 측정 (0-4)
 * @param password 비밀번호
 * @returns number 비밀번호 강도 (0: 매우 약함, 1: 약함, 2: 보통, 3: 강함, 4: 매우 강함)
 */
export function getPasswordStrength(password: string): number {
  let strength = 0;

  // 길이 점수
  if (password.length >= 8) strength++;
  if (password.length >= 12) strength++;

  // 문자 종류 점수
  if (/[A-Z]/.test(password)) strength++;
  if (/[a-z]/.test(password)) strength++;
  if (/[0-9]/.test(password)) strength++;
  if (/[^A-Za-z0-9]/.test(password)) strength++;

  // 최대 4점으로 정규화
  return Math.min(4, Math.floor(strength / 2));
}

/**
 * 비밀번호 강도 레이블
 * @param strength 비밀번호 강도 (0-4)
 * @returns string 강도 레이블
 */
export function getPasswordStrengthLabel(strength: number): string {
  switch (strength) {
    case 0:
      return "매우 약함";
    case 1:
      return "약함";
    case 2:
      return "보통";
    case 3:
      return "강함";
    case 4:
      return "매우 강함";
    default:
      return "알 수 없음";
  }
}
