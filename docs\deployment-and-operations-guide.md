# 배포 및 운영 가이드

## 📋 **개요**

**문서 목적**: 농장 방문자 관리 시스템의 배포 및 운영 가이드  
**대상 독자**: DevOps 엔지니어, 시스템 관리자, 개발팀  
**최종 업데이트**: 2025-06-15

## 🚀 **배포 준비**

### **1. 시스템 요구사항**

#### **개발 환경**

```bash
Node.js: >= 18.0.0
npm: >= 9.0.0
Git: >= 2.30.0
```

#### **프로덕션 환경**

```bash
Memory: >= 512MB
Storage: >= 1GB
Network: HTTPS 지원
Domain: SSL 인증서 필요
```

### **2. 환경 변수 설정**

#### **필수 환경 변수**

```env
# Supabase 설정
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Next.js 설정
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your_nextauth_secret  # 최소 32자 랜덤 문자열 권장
NEXTAUTH_URL_INTERNAL=http://localhost:3000  # 내부 URL (로드밸런서용)

# 보안 설정
NODE_ENV=production
PASSWORD_MIN_LENGTH=8
PASSWORD_MAX_ATTEMPTS=5  # 비밀번호 실패 최대 시도 횟수
SESSION_TIMEOUT=1800  # 30분 (초 단위)

# 이메일 설정 (선택사항)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password
SMTP_FROM=<EMAIL>
```

#### **선택적 환경 변수**

```env
# 분석 도구
NEXT_PUBLIC_GA_ID=your_google_analytics_id
SENTRY_DSN=your_sentry_dsn

# 외부 서비스
DAUM_POSTCODE_API_KEY=your_daum_api_key
```

### **3. 데이터베이스 설정**

#### **Supabase 프로젝트 생성**

```bash
1. https://supabase.com 접속
2. 새 프로젝트 생성
3. 데이터베이스 비밀번호 설정
4. 프로젝트 URL 및 API 키 확인
```

#### 데이터베이스 초기화

```sql
-- Supabase SQL 에디터에서 실행
-- scripts/database-reset-and-rebuild.sql 파일 내용을 복사하여 실행
-- 12단계: Storage 버킷 및 정책 설정 포함
-- 13단계: Storage 포함 최종 확인
```

#### Storage 버킷 설정 확인

```sql
-- Storage 버킷 생성 확인
SELECT id, name, public, file_size_limit, allowed_mime_types
FROM storage.buckets
WHERE id IN ('profiles', 'system');

-- Storage 정책 확인
SELECT policyname, cmd
FROM pg_policies
WHERE schemaname = 'storage'
AND tablename = 'objects';
```

#### 관리자 계정 생성

```sql
-- 프로덕션 관리자 계정 생성
SELECT create_admin_account(
    '<EMAIL>',
    '시스템 관리자',
    '010-0000-0000',
    '회사명'
);
```

## 배포 방법

### 1. Vercel 배포 (권장)

#### 자동 배포 설정

```bash
# 1. Vercel CLI 설치
npm i -g vercel

# 2. 프로젝트 연결
vercel

# 3. 환경 변수 설정
vercel env add NEXT_PUBLIC_SUPABASE_URL
vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY
vercel env add SUPABASE_SERVICE_ROLE_KEY

# 4. 배포 실행
vercel --prod
```

#### GitHub 연동 배포

```bash
1. 코드를 GitHub 저장소에 푸시
2. Vercel 대시보드에서 프로젝트 가져오기
3. 환경 변수 설정
4. 자동 배포 활성화
```

### 2. Netlify 배포

#### **빌드 설정**

```toml
# netlify.toml
[build]
  command = "npm run build"
  publish = ".next"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### **3. 자체 서버 배포**

#### **Docker 배포**

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

#### **Docker Compose**

```yaml
# docker-compose.yml
version: "3.8"
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_SUPABASE_URL=${SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
    restart: unless-stopped
```

## 🔧 **운영 관리**

### **1. 모니터링 설정**

#### **헬스 체크**

```typescript
// pages/api/health.ts
export default function handler(req, res) {
  res.status(200).json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version,
  });
}
```

#### **로그 모니터링**

```bash
# PM2 사용 시
pm2 logs farm-visitor-system

# Docker 사용 시
docker logs -f container_name
```

### **2. 백업 및 복구**

#### **데이터베이스 백업**

```sql
-- Supabase 자동 백업 설정
-- 대시보드 > Settings > Database > Backups
-- 일일 백업 활성화
```

#### **수동 백업**

```bash
# PostgreSQL 덤프
pg_dump -h your-host -U postgres -d your-db > backup.sql

# 복구
psql -h your-host -U postgres -d your-db < backup.sql
```

#### **Storage 파일 백업 (2025-06-14 추가)**

```bash
# Supabase Storage 파일 백업
# 1. Supabase 대시보드 > Storage
# 2. 각 버킷별 파일 다운로드
# 3. 정기적 백업 스케줄 설정

# 프로그래밍 방식 백업 예시
const backupStorageFiles = async () => {
  const buckets = ['profiles', 'system'];

  for (const bucket of buckets) {
    const { data: files } = await supabase.storage
      .from(bucket)
      .list('', { limit: 1000 });

    for (const file of files) {
      const { data: fileData } = await supabase.storage
        .from(bucket)
        .download(file.name);

      // 백업 저장소에 파일 저장
      // AWS S3, Google Cloud Storage 등 활용
    }
  }
};
```

### **3. 성능 최적화**

#### **CDN 설정**

```bash
# Vercel의 경우 자동 CDN 적용
# 기타 플랫폼의 경우 CloudFlare 등 사용
```

#### **Storage 성능 최적화 (2025-06-14 추가)**

```typescript
// 파일 업로드 최적화
const optimizeFileUpload = {
  // 파일 크기별 캐시 전략
  profiles: {
    maxSize: 5 * 1024 * 1024, // 5MB
    cacheControl: "3600", // 1시간
    allowedTypes: ["image/jpeg", "image/png", "image/webp"],
  },
  system: {
    maxSize: 2 * 1024 * 1024, // 2MB
    cacheControl: "86400", // 24시간
    allowedTypes: ["image/jpeg", "image/png", "image/webp", "image/svg+xml"],
  },
};

// CDN 캐시 무효화
const invalidateCache = async (fileUrl: string) => {
  // CloudFlare API 또는 기타 CDN 캐시 무효화
};
```

#### **캐싱 전략**

```typescript
// next.config.js
module.exports = {
  async headers() {
    return [
      {
        source: "/api/:path*",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=300, stale-while-revalidate=60",
          },
        ],
      },
    ];
  },
};
```

### **4. 보안 설정**

#### **HTTPS 강제**

```typescript
// middleware.ts
import { NextResponse } from "next/server";

export function middleware(request) {
  if (
    process.env.NODE_ENV === "production" &&
    request.headers.get("x-forwarded-proto") !== "https"
  ) {
    return NextResponse.redirect(
      `https://${request.headers.get("host")}${request.nextUrl.pathname}`
    );
  }
}
```

#### **보안 헤더**

```typescript
// next.config.js
module.exports = {
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "Referrer-Policy",
            value: "strict-origin-when-cross-origin",
          },
        ],
      },
    ];
  },
};
```

## 📊 **모니터링 및 알림**

### **1. 성능 모니터링**

#### **Core Web Vitals**

```typescript
// pages/_app.tsx
import { getCLS, getFID, getFCP, getLCP, getTTFB } from "web-vitals";

function sendToAnalytics(metric) {
  // Google Analytics 또는 기타 분석 도구로 전송
  gtag("event", metric.name, {
    value: Math.round(metric.value),
    event_label: metric.id,
  });
}

getCLS(sendToAnalytics);
getFID(sendToAnalytics);
getFCP(sendToAnalytics);
getLCP(sendToAnalytics);
getTTFB(sendToAnalytics);
```

#### **에러 추적**

```typescript
// Sentry 설정
import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
});
```

### **2. 알림 설정**

#### **Slack 알림**

```typescript
// lib/notifications.ts
export async function sendSlackAlert(message: string) {
  if (process.env.SLACK_WEBHOOK_URL) {
    await fetch(process.env.SLACK_WEBHOOK_URL, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ text: message }),
    });
  }
}
```

#### **이메일 알림**

```typescript
// 중요한 오류 발생 시 이메일 알림
export async function sendEmailAlert(error: Error) {
  // SendGrid, Nodemailer 등 사용
}
```

## 🔄 **업데이트 및 배포**

### **1. CI/CD 파이프라인**

#### **GitHub Actions**

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: "18"

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm test

      - name: Build
        run: npm run build

      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: "--prod"
```

### **2. 롤백 전략**

#### **Vercel 롤백**

```bash
# 이전 배포로 롤백
vercel rollback

# 특정 배포로 롤백
vercel rollback [deployment-url]
```

#### **데이터베이스 롤백**

```sql
-- 백업에서 복구
-- 1. 백업 파일 확인
-- 2. 복구 실행
-- 3. 데이터 무결성 검증
```

## 🚨 **장애 대응**

### **1. 장애 감지**

#### **자동 모니터링**

```typescript
// 헬스 체크 API
export default async function handler(req, res) {
  try {
    // 데이터베이스 연결 확인
    const { data, error } = await supabase
      .from("profiles")
      .select("count")
      .limit(1);

    if (error) throw error;

    res.status(200).json({ status: "healthy" });
  } catch (error) {
    res.status(500).json({ status: "unhealthy", error: error.message });
  }
}
```

### **2. 장애 복구**

#### **일반적인 문제 해결**

```bash
# 1. 서비스 재시작
pm2 restart farm-visitor-system

# 2. 로그 확인
pm2 logs --lines 100

# 3. 메모리 사용량 확인
pm2 monit

# 4. 데이터베이스 연결 확인
psql -h host -U user -d database -c "SELECT 1;"
```

## 📈 **확장성 고려사항**

### **1. 수평 확장**

```bash
# 로드 밸런서 설정
# 여러 인스턴스 운영
# 세션 스토어 외부화
```

### **2. 데이터베이스 최적화**

```sql
-- 인덱스 최적화
-- 쿼리 성능 튜닝
-- 연결 풀 설정
-- 읽기 전용 복제본 활용
```

## 환경 변수 업데이트

### 보안 관련 환경 변수

```env
# 비밀번호 정책
NEXT_PUBLIC_PASSWORD_MIN_LENGTH=8
NEXT_PUBLIC_PASSWORD_MAX_LENGTH=50
NEXT_PUBLIC_PASSWORD_REQUIRE_NUMBERS=true
NEXT_PUBLIC_PASSWORD_REQUIRE_UPPERCASE=true
NEXT_PUBLIC_PASSWORD_REQUIRE_LOWERCASE=true
NEXT_PUBLIC_PASSWORD_REQUIRE_SPECIAL=true
NEXT_PUBLIC_PASSWORD_DISALLOW_SPACES=true

# 보안 설정
NEXT_PUBLIC_MAX_LOGIN_ATTEMPTS=5
NEXT_PUBLIC_LOGIN_LOCKOUT_MINUTES=15
NEXT_PUBLIC_PASSWORD_HISTORY_SIZE=5
```

## 모니터링 및 유지보수

### 보안 모니터링

1. **비밀번호 관련 이벤트 모니터링**

   ```sql
   -- 실패한 로그인 시도 조회
   SELECT user_id, COUNT(*) as failed_attempts, MAX(attempt_time) as last_attempt
   FROM auth.audit_log_entries
   WHERE action = 'login'
   AND success = false
   GROUP BY user_id
   HAVING COUNT(*) >= 3;
   ```

2. **비밀번호 변경 모니터링**
   ```sql
   -- 비정상적인 비밀번호 변경 패턴 감지
   SELECT user_id, COUNT(*) as change_count
   FROM password_change_history
   WHERE changed_at > NOW() - INTERVAL '24 HOURS'
   GROUP BY user_id
   HAVING COUNT(*) > 3;
   ```

### 유지보수 절차

#### 비밀번호 정책 업데이트

1. 정책 변경 시 고려사항:

   - 기존 비밀번호 영향 분석
   - 사용자 공지 계획
   - 단계적 적용 전략

2. 업데이트 절차:

   ```bash
   # 1. 환경 변수 업데이트
   nano .env

   # 2. 서비스 재시작
   pm2 restart farm-dev

   # 3. 로그 확인
   pm2 logs farm-dev
   ```

#### 보안 감사

1. 정기 검사 항목:

   - 비밀번호 해시 알고리즘 검증
   - 실패한 로그인 시도 분석
   - 비밀번호 변경 패턴 검토
   - 의심스러운 IP 확인

2. 보안 업데이트:

   ```bash
   # 의존성 보안 업데이트
   npm audit fix

   # 취약점 스캔
   npm run security-scan
   ```

### 장애 대응

#### 비밀번호 관련 장애

1. 로그인 실패 급증:

   ```bash
   # 로그인 시도 제한 임시 조정
   export MAX_PASSWORD_ATTEMPTS=3
   pm2 restart farm-dev
   ```

2. 비밀번호 재설정 오류:
   ```bash
   # 이메일 서비스 상태 확인
   curl -X POST $EMAIL_SERVICE_HEALTH_CHECK
   ```

---

**문서 작성자**: AI Assistant
**최종 검토일**: 2025-06-14 (Storage 시스템 추가)
**다음 업데이트**: 운영 환경 변경 시
