# 에러 페이지 시스템 구현

## 📅 업데이트 날짜: 2025-06-14

## 🎯 구현 목표
- 사용자 친화적인 에러 처리 시스템 구축
- 하이브리드 접근법으로 상황별 최적화된 에러 페이지 제공
- 일관된 UI/UX와 네비게이션 유지
- 개발자를 위한 상세한 디버깅 정보 제공

---

## 🏗️ 시스템 아키텍처

### **하이브리드 접근법**

#### **1. 전체 페이지 에러 (Global Error Pages)**
- **사용 상황**: 404, 500 등 심각한 오류
- **특징**: 사이드바, 헤더 등 모든 UI 요소 제거
- **장점**: 명확한 에러 표시, 간단한 구현

#### **2. 컴포넌트 레벨 에러 (Component Error Boundaries)**
- **사용 상황**: 관리 영역 내 데이터 오류, 권한 부족
- **특징**: 기존 레이아웃 유지 (사이드바, 헤더 등)
- **장점**: 사용자 경험 향상, 낮은 이탈률

---

## 📁 파일 구조

```
app/
├── not-found.tsx                    # 404 전체 페이지
├── error.tsx                        # 500 전체 페이지
├── admin/
│   ├── error.tsx                    # 관리 영역 컴포넌트 에러
│   └── test-errors/
│       └── page.tsx                 # 에러 테스트 페이지 (개발용)
└── components/
    └── error/
        ├── admin-error.tsx          # 관리 영역 에러 컴포넌트
        ├── access-denied.tsx        # 접근 권한 에러 컴포넌트
        └── error-boundary.tsx       # Error Boundary 컴포넌트
```

---

## 🔧 구현된 컴포넌트

### **1. 전체 페이지 에러**

#### **404 Not Found (`app/not-found.tsx`)**
```typescript
// 자동으로 존재하지 않는 경로에 대해 표시
// 특징: 브랜드 일관성, 명확한 네비게이션
```

**주요 기능:**
- 브랜드 로고 및 일관된 디자인
- 대시보드, 로그인 페이지로 이동 버튼
- 이전 페이지로 돌아가기 기능
- 추가 도움말 링크

#### **500 Server Error (`app/error.tsx`)**
```typescript
// 서버 오류 발생 시 자동으로 표시
// 특징: 에러 로깅, 개발자 정보 제공
```

**주요 기능:**
- 자동 에러 로그 생성
- 다시 시도 및 새로고침 기능
- 개발 모드에서 상세한 에러 정보 표시
- 사용자 친화적인 메시지

### **2. 컴포넌트 레벨 에러**

#### **관리 영역 에러 (`components/error/admin-error.tsx`)**
```typescript
<AdminError 
  error={error}
  reset={reset}
  title="커스텀 제목"
  description="커스텀 설명"
  showNavigation={true}
/>
```

**주요 기능:**
- 사이드바 네비게이션 유지
- 다시 시도 및 새로고침 기능
- 추천 페이지 네비게이션
- 개발 모드 디버깅 정보

#### **접근 권한 에러 (`components/error/access-denied.tsx`)**
```typescript
<AccessDenied
  title="접근 권한이 없습니다"
  description="이 페이지에 접근할 권한이 없습니다."
  requiredRole="관리자"
  currentRole="일반 사용자"
  showNavigation={true}
/>
```

**주요 기능:**
- 권한 정보 명시
- 이용 가능한 페이지 안내
- 계정 설정 링크
- 도움말 정보

#### **Error Boundary (`components/error/error-boundary.tsx`)**
```typescript
<ErrorBoundary
  title="커스텀 제목"
  description="커스텀 설명"
  fallback={(error, reset) => <CustomErrorComponent />}
>
  <YourComponent />
</ErrorBoundary>

// 또는 HOC 방식
const SafeComponent = withErrorBoundary(YourComponent, {
  title: "컴포넌트 에러",
  showNavigation: true
});
```

**주요 기능:**
- React 컴포넌트 에러 포착
- 자동 에러 로그 생성
- 커스텀 fallback 컴포넌트 지원
- HOC 패턴 지원

---

## 🎯 사용 시나리오

### **시나리오 1: 잘못된 URL 접근**
```
사용자 행동: /admin/wrong-page 접근
시스템 응답: 404 전체 페이지 표시
사용자 경험: 명확한 에러 인지, 대시보드로 쉽게 이동
```

### **시나리오 2: 서버 오류**
```
사용자 행동: 페이지 로딩 중 서버 오류 발생
시스템 응답: 500 전체 페이지 표시 + 에러 로그 생성
사용자 경험: 다시 시도 버튼으로 쉬운 복구
```

### **시나리오 3: 관리 영역 데이터 오류**
```
사용자 행동: /admin/farms 접근 후 데이터 로딩 실패
시스템 응답: 사이드바 유지 + 메인 영역에 에러 컴포넌트
사용자 경험: 다른 메뉴로 쉽게 이동, 컨텍스트 유지
```

### **시나리오 4: 권한 부족**
```
사용자 행동: 일반 사용자가 관리자 전용 페이지 접근
시스템 응답: 접근 권한 에러 컴포넌트 표시
사용자 경험: 권한 정보 확인, 이용 가능한 페이지 안내
```

---

## 🔧 통합 및 사용법

### **ProtectedRoute와의 통합**
```typescript
// 권한 부족 시 자동으로 AccessDenied 컴포넌트 표시
<ProtectedRoute requireAdmin={true}>
  <AdminOnlyComponent />
</ProtectedRoute>
```

### **Error Boundary 적용**
```typescript
// 개별 컴포넌트 보호
<ErrorBoundary title="데이터 로딩 에러">
  <DataComponent />
</ErrorBoundary>

// 페이지 전체 보호
export default withErrorBoundary(MyPage, {
  title: "페이지 에러",
  showNavigation: true
});
```

### **수동 에러 처리**
```typescript
// 데이터 로딩 실패 시
if (error) {
  return (
    <AdminError
      error={error}
      reset={() => refetch()}
      title="데이터 로딩 실패"
      description="농장 데이터를 불러오지 못했습니다."
    />
  );
}
```

---

## 🧪 테스트 및 개발

### **에러 테스트 페이지**
- **경로**: `/admin/test-errors` (개발 모드 전용)
- **기능**: 모든 에러 타입 테스트 가능
- **사용법**: 각 버튼 클릭으로 다양한 에러 상황 시뮬레이션

### **테스트 방법**
```typescript
// 1. 404 테스트
window.location.href = "/non-existent-page";

// 2. 컴포넌트 에러 테스트
throw new Error("테스트 에러");

// 3. 권한 테스트
// 일반 사용자로 관리자 페이지 접근

// 4. Error Boundary 테스트
<ErrorBoundary>
  <ComponentThatThrows />
</ErrorBoundary>
```

---

## 📊 에러 로깅

### **자동 로그 생성**
모든 에러 컴포넌트는 자동으로 시스템 로그를 생성합니다:

```typescript
// 전역 에러
await createErrorLog("GLOBAL_ERROR", error, "전역 에러 발생");

// 관리 영역 에러
await createErrorLog("ADMIN_COMPONENT_ERROR", error, "관리 영역 컴포넌트 에러");

// Error Boundary 에러
await createErrorLog("REACT_ERROR_BOUNDARY", error, "React Error Boundary에서 에러 포착");
```

### **로그 데이터 구조**
```json
{
  "action": "GLOBAL_ERROR",
  "message": "페이지 렌더링 중 오류 발생: Cannot read property 'name' of undefined",
  "level": "error",
  "resource_type": "error",
  "metadata": {
    "error_message": "Cannot read property 'name' of undefined",
    "error_stack": "Error: Cannot read property...",
    "user_agent": "Mozilla/5.0...",
    "page_url": "/admin/dashboard"
  }
}
```

---

## 🎨 디자인 가이드라인

### **색상 체계**
- **404/일반 에러**: 빨간색 계열 (`red-600`, `red-100`)
- **서버 에러**: 주황색 계열 (`orange-600`, `orange-100`)
- **권한 에러**: 주황색 계열 (`orange-600`, `orange-100`)
- **정보성**: 파란색 계열 (`blue-600`, `blue-100`)

### **아이콘 사용**
- **404**: `AlertTriangle` - 경고 삼각형
- **500**: `AlertCircle` - 경고 원형
- **권한**: `Shield` - 방패
- **일반 에러**: `AlertTriangle` - 경고 삼각형

### **버튼 우선순위**
1. **주요 액션**: Primary 버튼 (다시 시도, 대시보드)
2. **보조 액션**: Outline 버튼 (새로고침, 이전 페이지)
3. **추가 액션**: Ghost 버튼 (기타 네비게이션)

---

## 🚀 향후 개선 계획

### **단기 (1-2주)**
- [ ] 에러 발생 빈도 모니터링 대시보드
- [ ] 사용자별 에러 패턴 분석
- [ ] 에러 복구 성공률 추적

### **중기 (1개월)**
- [ ] 자동 에러 복구 메커니즘
- [ ] 에러 발생 시 자동 알림 시스템
- [ ] 에러 페이지 A/B 테스트

### **장기 (3개월)**
- [ ] AI 기반 에러 예측 및 방지
- [ ] 사용자 맞춤형 에러 메시지
- [ ] 실시간 에러 모니터링 및 대응

---

## 📚 관련 문서
- [인증 시스템 개선사항](./authentication-improvements.md)
- [시스템 로그 개선사항](./system-log-improvements.md)
- [개발 변경사항 로그](./development-changelog.md)
- [빠른 참조 가이드](./quick-reference.md)
