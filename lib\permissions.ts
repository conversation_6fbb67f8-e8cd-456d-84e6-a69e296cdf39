// 개선된 권한 관리 시스템
// 농장별 권한과 시스템 권한을 명확히 분리

import { supabase } from "./supabase";

// 권한 타입 정의
export type SystemRole = "admin" | "user";
export type FarmRole = "owner" | "manager" | "viewer" | "none";
export type UserCategory =
  | "system_admin"
  | "farm_owner"
  | "farm_member"
  | "general_user";

export interface UserPermissions {
  userId: string;
  systemRole: SystemRole;
  farmPermissions: FarmPermission[];
  userStatus: UserStatus;
}

export interface FarmPermission {
  farmId: string;
  farmName: string;
  role: FarmRole;
  canManage: boolean;
  canView: boolean;
}

export interface UserStatus {
  accountType: SystemRole;
  hasFarms: boolean;
  ownedFarmsCount: number;
  memberFarmsCount: number;
  userCategory: UserCategory;
}

// 권한 확인 클래스
export class PermissionManager {
  private static instance: PermissionManager;
  private userPermissions: Map<string, UserPermissions> = new Map();

  static getInstance(): PermissionManager {
    if (!PermissionManager.instance) {
      PermissionManager.instance = new PermissionManager();
    }
    return PermissionManager.instance;
  }

  // 시스템 관리자 권한 확인
  async isSystemAdmin(userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase.rpc("is_system_admin", {
        user_id: userId,
      });

      if (error) throw error;
      return data || false;
    } catch (error) {
      console.error("Error checking system admin:", error);
      return false;
    }
  }

  // 특정 농장에서의 사용자 권한 확인
  async getFarmRole(userId: string, farmId: string): Promise<FarmRole> {
    try {
      const { data, error } = await supabase.rpc("get_farm_role", {
        user_id: userId,
        farm_id: farmId,
      });

      if (error) throw error;
      return (data as FarmRole) || "none";
    } catch (error) {
      console.error("Error getting farm role:", error);
      return "none";
    }
  }

  // 농장 관리 권한 확인
  async canManageFarm(userId: string, farmId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase.rpc("can_manage_farm", {
        user_id: userId,
        farm_id: farmId,
      });

      if (error) throw error;
      return data || false;
    } catch (error) {
      console.error("Error checking farm management permission:", error);
      return false;
    }
  }

  // 농장 조회 권한 확인
  async canViewFarm(userId: string, farmId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase.rpc("can_view_farm", {
        user_id: userId,
        farm_id: farmId,
      });

      if (error) throw error;
      return data || false;
    } catch (error) {
      console.error("Error checking farm view permission:", error);
      return false;
    }
  }

  // 사용자가 접근 가능한 농장 목록
  async getUserAccessibleFarms(userId: string): Promise<FarmPermission[]> {
    try {
      const { data, error } = await supabase.rpc("get_user_accessible_farms", {
        user_id: userId,
      });

      if (error) throw error;

      return (data || []).map((farm: any) => ({
        farmId: farm.farm_id,
        farmName: farm.farm_name,
        role: farm.farm_role as FarmRole,
        canManage: farm.can_manage,
        canView: farm.can_view,
      }));
    } catch (error) {
      console.error("Error getting accessible farms:", error);
      return [];
    }
  }

  // 사용자 상태 확인
  async getUserStatus(userId: string): Promise<UserStatus> {
    try {
      const { data, error } = await supabase.rpc("get_user_status", {
        user_id: userId,
      });

      if (error) throw error;

      const statusData = data?.[0];
      if (!statusData) {
        return {
          accountType: "user",
          hasFarms: false,
          ownedFarmsCount: 0,
          memberFarmsCount: 0,
          userCategory: "general_user",
        };
      }

      return {
        accountType: statusData.account_type as SystemRole,
        hasFarms: statusData.has_farms,
        ownedFarmsCount: statusData.owned_farms_count,
        memberFarmsCount: statusData.member_farms_count,
        userCategory: statusData.user_category as UserCategory,
      };
    } catch (error) {
      console.error("Error getting user status:", error);
      return {
        accountType: "user",
        hasFarms: false,
        ownedFarmsCount: 0,
        memberFarmsCount: 0,
        userCategory: "general_user",
      };
    }
  }

  // 사용자의 전체 권한 정보 조회
  async getUserPermissions(userId: string): Promise<UserPermissions> {
    try {
      const [isAdmin, accessibleFarms, userStatus] = await Promise.all([
        this.isSystemAdmin(userId),
        this.getUserAccessibleFarms(userId),
        this.getUserStatus(userId),
      ]);

      const permissions: UserPermissions = {
        userId,
        systemRole: isAdmin ? "admin" : "user",
        farmPermissions: accessibleFarms,
        userStatus: userStatus,
      };

      // 캐시에 저장
      this.userPermissions.set(userId, permissions);

      return permissions;
    } catch (error) {
      console.error("Error getting user permissions:", error);
      return {
        userId,
        systemRole: "user",
        farmPermissions: [],
        userStatus: {
          accountType: "user",
          hasFarms: false,
          ownedFarmsCount: 0,
          memberFarmsCount: 0,
          userCategory: "general_user",
        },
      };
    }
  }

  // 권한 캐시 무효화
  invalidateUserPermissions(userId: string): void {
    this.userPermissions.delete(userId);
  }

  // 모든 권한 캐시 무효화
  invalidateAllPermissions(): void {
    this.userPermissions.clear();
  }
}

// 편의 함수들
export const permissionManager = PermissionManager.getInstance();

// React Hook에서 사용할 수 있는 권한 확인 함수들
export const usePermissions = () => {
  return {
    isSystemAdmin: permissionManager.isSystemAdmin.bind(permissionManager),
    getFarmRole: permissionManager.getFarmRole.bind(permissionManager),
    canManageFarm: permissionManager.canManageFarm.bind(permissionManager),
    canViewFarm: permissionManager.canViewFarm.bind(permissionManager),
    getUserAccessibleFarms:
      permissionManager.getUserAccessibleFarms.bind(permissionManager),
    getUserStatus: permissionManager.getUserStatus.bind(permissionManager),
    getUserPermissions:
      permissionManager.getUserPermissions.bind(permissionManager),
  };
};

// 권한 기반 컴포넌트 렌더링을 위한 유틸리티
export const withPermission = (
  component: React.ReactNode,
  condition: boolean,
  fallback?: React.ReactNode
): React.ReactNode => {
  return condition ? component : fallback || null;
};

// 권한 확인을 위한 상수들
export const PERMISSIONS = {
  SYSTEM: {
    ADMIN: "admin" as SystemRole,
    USER: "user" as SystemRole,
  },
  FARM: {
    OWNER: "owner" as FarmRole,
    MANAGER: "manager" as FarmRole,
    VIEWER: "viewer" as FarmRole,
    NONE: "none" as FarmRole,
  },
} as const;

// 권한 레벨 비교 함수
export const isHigherFarmRole = (role1: FarmRole, role2: FarmRole): boolean => {
  const roleHierarchy = {
    owner: 3,
    manager: 2,
    viewer: 1,
    none: 0,
  };

  return roleHierarchy[role1] > roleHierarchy[role2];
};

// 권한 설명 텍스트
export const getRoleDescription = (role: FarmRole): string => {
  switch (role) {
    case "owner":
      return "농장 소유자 - 모든 권한";
    case "manager":
      return "농장 관리자 - 방문자 관리, 농장 정보 수정";
    case "viewer":
      return "조회자 - 방문자 목록 조회만 가능";
    case "none":
      return "권한 없음";
    default:
      return "알 수 없는 권한";
  }
};

// 사용자 카테고리 설명 텍스트
export const getUserCategoryDescription = (category: UserCategory): string => {
  switch (category) {
    case "system_admin":
      return "시스템 관리자 - 모든 농장과 사용자 관리 가능";
    case "farm_owner":
      return "농장 소유자 - 하나 이상의 농장을 소유";
    case "farm_member":
      return "농장 구성원 - 하나 이상의 농장에 소속";
    case "general_user":
      return "일반 사용자 - 농장에 속하지 않음";
    default:
      return "알 수 없는 사용자 유형";
  }
};

// 일반 사용자 관련 유틸리티 함수들
export const isGeneralUser = (userStatus: UserStatus): boolean => {
  return userStatus.userCategory === "general_user";
};

export const canCreateFarm = (userStatus: UserStatus): boolean => {
  // 모든 일반 사용자는 농장을 생성할 수 있음
  return (
    userStatus.accountType === "user" || userStatus.accountType === "admin"
  );
};

export const getAvailableActions = (userStatus: UserStatus): string[] => {
  const actions: string[] = [];

  switch (userStatus.userCategory) {
    case "system_admin":
      actions.push("모든 농장 관리", "사용자 관리", "시스템 설정");
      break;
    case "farm_owner":
      actions.push("농장 관리", "구성원 관리", "방문자 관리", "새 농장 생성");
      break;
    case "farm_member":
      actions.push("소속 농장 접근", "방문자 조회/관리", "새 농장 생성");
      break;
    case "general_user":
      actions.push("농장 생성", "프로필 관리");
      break;
  }

  return actions;
};

// 사용자 상태에 따른 대시보드 표시 여부
export const shouldShowDashboard = (userStatus: UserStatus): boolean => {
  return userStatus.hasFarms || userStatus.accountType === "admin";
};

// 사용자 상태에 따른 농장 목록 표시 여부
export const shouldShowFarmList = (userStatus: UserStatus): boolean => {
  return userStatus.hasFarms || userStatus.accountType === "admin";
};

// 농장 생성 버튼 표시 여부
export const shouldShowCreateFarmButton = (userStatus: UserStatus): boolean => {
  return true; // 모든 사용자가 농장을 생성할 수 있음
};
