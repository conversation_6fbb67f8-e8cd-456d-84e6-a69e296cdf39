"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Leaf, Loader2, Lock } from "lucide-react";
import { motion } from "framer-motion";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import {
  validatePassword,
  validatePasswordMatch,
  getPasswordStrength,
  getPasswordStrengthLabel,
  type PasswordValidationResult,
} from "@/lib/utils/password-validation";
import { cn } from "@/lib/utils";
import { AuthError, AuthSessionMissingError } from "@supabase/supabase-js";

export default function ResetPasswordConfirmPage() {
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [passwordValidation, setPasswordValidation] =
    useState<PasswordValidationResult>({ isValid: true, errors: [] });
  const [passwordMatchValidation, setPasswordMatchValidation] =
    useState<PasswordValidationResult>({ isValid: true, errors: [] });
  const router = useRouter();
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (loading) return;
    setLoading(true);

    try {
      // 세션 유효성 검사
      const {
        data: { session },
        error: sessionError,
      } = await supabase.auth.getSession();

      if (sessionError || !session) {
        throw new AuthSessionMissingError();
      }

      // 비밀번호 유효성 검사
      const currentPasswordValidation = validatePassword(password);
      if (!currentPasswordValidation.isValid) {
        toast({
          title: "비밀번호 오류",
          description: currentPasswordValidation.errors[0],
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      // 비밀번호 일치 검사
      const currentPasswordMatchValidation = validatePasswordMatch(
        password,
        confirmPassword
      );
      if (!currentPasswordMatchValidation.isValid) {
        toast({
          title: "비밀번호 불일치",
          description: currentPasswordMatchValidation.errors[0],
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      // 비밀번호 재설정
      const { error } = await supabase.auth.updateUser({
        password: password,
      });

      if (error) throw error;

      // 성공 메시지 표시
      toast({
        title: "비밀번호 재설정 완료",
        description: "새 비밀번호로 로그인할 수 있습니다.",
      });

      // 세션 정리
      await supabase.auth.signOut();

      // 상태 초기화
      setPassword("");
      setConfirmPassword("");
      setLoading(false);

      // 로그인 페이지로 이동
      router.push("/login");
    } catch (error) {
      console.error("Password reset error:", error);

      if (error instanceof AuthSessionMissingError) {
        toast({
          title: "세션 만료",
          description:
            "비밀번호 재설정 세션이 만료되었습니다. 새로운 재설정 링크를 요청해주세요.",
          variant: "destructive",
        });
        setLoading(false);
        router.push("/reset-password");
      } else if (error instanceof AuthError) {
        if (
          error.message.includes("different") ||
          error.message.includes("old password")
        ) {
          toast({
            title: "비밀번호 재설정 실패",
            description:
              "이전 비밀번호와 동일한 비밀번호는 사용할 수 없습니다.",
            variant: "destructive",
          });
        } else {
          toast({
            title: "비밀번호 재설정 실패",
            description: error.message,
            variant: "destructive",
          });
        }
        setLoading(false);
      } else {
        toast({
          title: "비밀번호 재설정 실패",
          description:
            "비밀번호 재설정 중 오류가 발생했습니다. 다시 시도해주세요.",
          variant: "destructive",
        });
        setLoading(false);
      }
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setPassword(newPassword);
    setPasswordStrength(getPasswordStrength(newPassword));
    setPasswordValidation(validatePassword(newPassword));
    if (confirmPassword) {
      setPasswordMatchValidation(
        validatePasswordMatch(newPassword, confirmPassword)
      );
    }
  };

  const handleConfirmPasswordChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newConfirmPassword = e.target.value;
    setConfirmPassword(newConfirmPassword);
    setPasswordMatchValidation(
      validatePasswordMatch(password, newConfirmPassword)
    );
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-farm p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="border-none shadow-soft-lg">
          <CardHeader className="space-y-1 text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
              <Leaf className="h-6 w-6 text-primary" />
            </div>
            <CardTitle className="text-2xl">비밀번호 재설정</CardTitle>
            <CardDescription>새로운 비밀번호를 입력해주세요</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="password">새 비밀번호</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    value={password}
                    onChange={handlePasswordChange}
                    required
                    className={`h-12 pl-10 input-focus ${
                      !passwordValidation.isValid ? "border-red-500" : ""
                    }`}
                    disabled={loading}
                  />
                </div>
                {password && (
                  <div className="mt-2">
                    <div className="text-sm text-muted-foreground">
                      비밀번호 강도:{" "}
                      {getPasswordStrengthLabel(passwordStrength)}
                    </div>
                    <div className="h-1 mt-1 bg-muted rounded-full overflow-hidden">
                      <div
                        className={cn("h-full transition-all duration-300", {
                          "w-1/5 bg-destructive": passwordStrength === 0,
                          "w-2/5 bg-orange-500": passwordStrength === 1,
                          "w-3/5 bg-yellow-500": passwordStrength === 2,
                          "w-4/5 bg-lime-500": passwordStrength === 3,
                          "w-full bg-green-500": passwordStrength === 4,
                        })}
                      />
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      8자 이상, 대소문자, 숫자, 특수문자를 포함해야 합니다.
                    </p>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">새 비밀번호 확인</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    value={confirmPassword}
                    onChange={handleConfirmPasswordChange}
                    required
                    className={`h-12 pl-10 input-focus ${
                      !passwordMatchValidation.isValid ? "border-red-500" : ""
                    }`}
                    disabled={loading}
                  />
                </div>
              </div>

              <Button type="submit" className="h-12 w-full" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    변경 중...
                  </>
                ) : (
                  "비밀번호 변경"
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
