"use client";

import type React from "react";
import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Leaf, Loader2, Lock, Mail } from "lucide-react";
import { motion } from "framer-motion";
import { supabase } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";
import { createSystemLog } from "@/lib/utils/system-log";

interface FormErrors {
  email?: string;
  password?: string;
}

export default function LoginPage() {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setErrors((prev) => ({ ...prev, [name]: "" }));
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setErrors({});

    console.log("🔄 Starting login process...");
    console.log("📧 Email:", formData.email);

    try {
      console.log("🔐 Attempting to sign in with Supabase...");
      // 1. 로그인 시도
      const { data, error } = await supabase.auth.signInWithPassword({
        email: formData.email.toLowerCase().trim(),
        password: formData.password,
      });

      console.log("📡 Supabase response:", { data, error });

      if (error) {
        console.error("❌ Login error:", error);
        let errorMessage = "로그인에 실패했습니다.";
        if (error.message.includes("Invalid login credentials")) {
          errorMessage = "이메일 또는 비밀번호가 올바르지 않습니다.";
        } else if (error.message.includes("Email not confirmed")) {
          errorMessage = "이메일 인증이 필요합니다.";
        }

        setErrors({ email: errorMessage });

        // 로그인 실패 로그 생성 시도
        try {
          await createSystemLog(
            "USER_LOGIN_FAILED",
            `로그인 실패: ${formData.email} - ${errorMessage}`,
            "warn",
            undefined,
            "system",
            undefined,
            {
              attempted_email: formData.email,
              error_message: errorMessage,
              login_method: "email_password",
              timestamp: new Date().toISOString(),
            }
          );
        } catch (logError) {
          console.error("Failed to create login failure log:", logError);
          // 로그 생성 실패는 로그인 프로세스에 영향을 주지 않음
        }

        setLoading(false);
        return;
      }

      console.log("✅ Login successful, fetching profile...");
      // 2. 프로필 정보 가져오기
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", data.user.id)
        .maybeSingle();

      console.log("📋 Profile data:", { profile, profileError });

      if (profileError || !profile) {
        console.error("❌ Profile fetch error:", profileError);
        const errorMessage = "프로필 정보를 가져오는데 실패했습니다.";
        setErrors({ email: errorMessage });
        toast({
          title: "오류",
          description: errorMessage,
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      // 3. 로그인 성공 로그 생성 시도
      try {
        console.log("📝 Creating login success log...");
        await createSystemLog(
          "USER_LOGIN",
          `사용자 "${profile.name || formData.email}"가 로그인했습니다`,
          "info",
          data.user.id,
          "user",
          data.user.id,
          {
            user_name: profile.name,
            user_email: profile.email,
            login_method: "email_password",
            account_type: profile.account_type,
            timestamp: new Date().toISOString(),
          }
        );
      } catch (logError) {
        console.error("Failed to create login success log:", logError);
        // 로그 생성 실패는 로그인 진행에 영향을 주지 않음
      }

      // 4. 로그인 성공 처리
      console.log("🎉 Login process completed successfully");
      toast({
        title: "로그인 성공",
        description: "대시보드로 이동합니다.",
      });

      // 관리자 대시보드로 리다이렉션
      console.log("🔄 Redirecting to: /admin/dashboard");
      router.push("/admin/dashboard");
    } catch (error) {
      console.error("❌ Unexpected error during login:", error);
      toast({
        title: "오류",
        description: "로그인 중 오류가 발생했습니다.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-farm p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="border-none shadow-soft-lg">
          <CardHeader className="space-y-1 text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
              <Leaf className="h-6 w-6 text-primary" />
            </div>
            <CardTitle className="text-2xl">로그인</CardTitle>
            <CardDescription>
              농장 방문자 관리 시스템에 로그인하세요
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">이메일</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className={`h-12 pl-10 input-focus ${
                      errors.email ? "border-red-500" : ""
                    }`}
                    disabled={loading}
                  />
                </div>
                {errors.email && (
                  <p className="text-sm text-red-500">{errors.email}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">비밀번호</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                    className={`h-12 pl-10 input-focus ${
                      errors.password ? "border-red-500" : ""
                    }`}
                    disabled={loading}
                  />
                </div>
                {errors.password && (
                  <p className="text-sm text-red-500">{errors.password}</p>
                )}
              </div>

              <Button type="submit" className="h-12 w-full" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    로그인 중...
                  </>
                ) : (
                  "로그인"
                )}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex flex-col space-y-2">
            <div className="text-center text-sm">
              <Link
                href="/reset-password"
                className="text-primary hover:underline"
              >
                비밀번호를 잊으셨나요?
              </Link>
            </div>
            <div className="text-center text-sm">
              계정이 없으신가요?{" "}
              <Link href="/register" className="text-primary hover:underline">
                회원가입
              </Link>
            </div>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
}
