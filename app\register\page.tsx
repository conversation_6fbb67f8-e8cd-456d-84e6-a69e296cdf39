"use client";

import type React from "react";
import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Leaf, Loader2, Mail, User, Lock, Phone } from "lucide-react";
import { motion } from "framer-motion";
import { supabase } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";
import {
  createSystemLog,
  createAuthLog,
  createErrorLog,
} from "@/lib/utils/system-log";
import {
  validatePassword,
  validatePasswordMatch,
  getPasswordStrength,
  getPasswordStrengthLabel,
  type PasswordValidationResult,
} from "@/lib/utils/password-validation";
import { validateEmail } from "@/lib/utils/validation";
import { cn } from "@/lib/utils";

interface FormData {
  email: string;
  password: string;
  confirmPassword: string;
  name: string;
  phone: string;
}

export default function RegisterPage() {
  const [formData, setFormData] = useState<FormData>({
    email: "",
    password: "",
    confirmPassword: "",
    name: "",
    phone: "",
  });
  const [loading, setLoading] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [passwordValidation, setPasswordValidation] =
    useState<PasswordValidationResult>({ isValid: true, errors: [] });
  const [passwordMatchValidation, setPasswordMatchValidation] =
    useState<PasswordValidationResult>({ isValid: true, errors: [] });
  const [emailError, setEmailError] = useState<string>("");

  const router = useRouter();
  const { toast } = useToast();

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setFormData((prev) => ({ ...prev, password: newPassword }));
    setPasswordStrength(getPasswordStrength(newPassword));
    setPasswordValidation(validatePassword(newPassword));
    if (formData.confirmPassword) {
      setPasswordMatchValidation(
        validatePasswordMatch(newPassword, formData.confirmPassword)
      );
    }
  };

  const handleConfirmPasswordChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newConfirmPassword = e.target.value;
    setFormData((prev) => ({ ...prev, confirmPassword: newConfirmPassword }));
    setPasswordMatchValidation(
      validatePasswordMatch(formData.password, newConfirmPassword)
    );
  };

  // 이메일 중복 체크 함수 추가
  const checkEmailDuplicate = async (email: string) => {
    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("email")
        .eq("email", email.toLowerCase().trim())
        .maybeSingle();

      if (error) {
        console.error("Email check error:", error);
        setEmailError("이메일 확인 중 오류가 발생했습니다.");
        return true;
      }

      if (data) {
        setEmailError("이미 사용 중인 이메일입니다.");
        return true;
      }

      setEmailError("");
      return false;
    } catch (error) {
      console.error("Email check error:", error);
      setEmailError("이메일 확인 중 오류가 발생했습니다.");
      return true;
    }
  };

  // 이메일 입력 필드 onBlur 핸들러 추가
  const handleEmailBlur = async (e: React.FocusEvent<HTMLInputElement>) => {
    const email = e.target.value.trim();
    if (email && validateEmail(email)) {
      await checkEmailDuplicate(email);
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);

    try {
      // 이메일 중복 체크
      const isDuplicate = await checkEmailDuplicate(formData.email);
      if (isDuplicate) {
        setLoading(false);
        return;
      }

      // 비밀번호 유효성 검사
      const currentPasswordValidation = validatePassword(formData.password);
      if (!currentPasswordValidation.isValid) {
        toast({
          title: "비밀번호 오류",
          description: currentPasswordValidation.errors[0],
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      // 비밀번호 일치 검사
      const currentPasswordMatchValidation = validatePasswordMatch(
        formData.password,
        formData.confirmPassword
      );
      if (!currentPasswordMatchValidation.isValid) {
        toast({
          title: "비밀번호 불일치",
          description: currentPasswordMatchValidation.errors[0],
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      // 회원가입 처리
      const { data, error } = await supabase.auth.signUp({
        email: formData.email.toLowerCase(),
        password: formData.password,
        options: {
          emailRedirectTo: `${location.origin}/auth/callback`,
          data: {
            name: formData.name,
            phone: formData.phone,
            email_original: formData.email,
          },
        },
      });

      if (error) {
        await createErrorLog(
          "REGISTRATION_FAILED",
          `회원가입 실패: ${error.message}`,
          error.message,
          {
            attempted_email: formData.email,
            error_code: error.status || 500,
            full_error: JSON.stringify(error),
          }
        );
        throw error;
      }

      // 시스템 로그 생성
      if (data?.user) {
        await createSystemLog(
          "USER_REGISTERED",
          `새 사용자 "${formData.name}" (${formData.email})가 회원가입했습니다`,
          "info",
          data.user.id,
          "user",
          data.user.id,
          {
            user_name: formData.name,
            user_email: formData.email,
            user_phone: formData.phone,
            registration_method: "web_form",
            initial_account_type: "user",
            timestamp: new Date().toISOString(),
          }
        );
      }

      toast({
        title: "회원가입 완료",
        description: "회원가입이 완료되었습니다.",
      });

      router.push("/login");
    } catch (error) {
      console.error("Registration error:", error);
      toast({
        title: "회원가입 실패",
        description: "회원가입 중 오류가 발생했습니다. 다시 시도해주세요.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-farm p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="border-none shadow-soft-lg">
          <CardHeader className="space-y-1 text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
              <Leaf className="h-6 w-6 text-primary" />
            </div>
            <CardTitle className="text-2xl">회원가입</CardTitle>
            <CardDescription>
              농장 방문자 관리 시스템에 가입하세요
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm">
                  아이디(이메일) <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        email: e.target.value,
                      }))
                    }
                    onBlur={handleEmailBlur}
                    required
                    className={cn("h-12 pl-10 input-focus", {
                      "border-destructive": emailError,
                    })}
                    disabled={loading}
                  />
                </div>
                {emailError && (
                  <p className="text-sm text-destructive">{emailError}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm">
                  이름 <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    id="name"
                    name="name"
                    placeholder="홍길동"
                    value={formData.name}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, name: e.target.value }))
                    }
                    required
                    className={`h-12 pl-10 input-focus`}
                    disabled={loading}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone" className="text-sm">
                  휴대폰 번호 <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    id="phone"
                    name="phone"
                    type="tel"
                    placeholder="010-1234-5678"
                    value={formData.phone}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        phone: e.target.value,
                      }))
                    }
                    required
                    className={`h-12 pl-10 input-focus`}
                    disabled={loading}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">비밀번호</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-5 w-5" />
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    value={formData.password}
                    onChange={handlePasswordChange}
                    required
                    className={cn("h-12 pl-10 input-focus", {
                      "border-destructive":
                        !passwordValidation.isValid && formData.password,
                    })}
                    disabled={loading}
                  />
                </div>
                {formData.password && (
                  <div className="mt-2">
                    <div className="text-sm text-muted-foreground">
                      비밀번호 강도:{" "}
                      {getPasswordStrengthLabel(passwordStrength)}
                    </div>
                    <div className="h-1 mt-1 bg-muted rounded-full overflow-hidden">
                      <div
                        className={cn("h-full transition-all duration-300", {
                          "w-1/5 bg-destructive": passwordStrength === 0,
                          "w-2/5 bg-orange-500": passwordStrength === 1,
                          "w-3/5 bg-yellow-500": passwordStrength === 2,
                          "w-4/5 bg-lime-500": passwordStrength === 3,
                          "w-full bg-green-500": passwordStrength === 4,
                        })}
                      />
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      8자 이상, 대소문자, 숫자, 특수문자를 포함해야 합니다.
                    </p>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">비밀번호 확인</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-5 w-5" />
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    value={formData.confirmPassword}
                    onChange={handleConfirmPasswordChange}
                    required
                    className={cn("h-12 pl-10 input-focus", {
                      "border-destructive":
                        !passwordMatchValidation.isValid &&
                        formData.confirmPassword,
                    })}
                    disabled={loading}
                  />
                </div>
              </div>

              <Button type="submit" className="h-12 w-full" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    가입 중...
                  </>
                ) : (
                  "회원가입"
                )}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex justify-center">
            <div className="text-center text-sm">
              이미 계정이 있으신가요?{" "}
              <Link href="/login" className="text-primary hover:underline">
                로그인
              </Link>
            </div>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
}
