import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useState, useCallback } from "react";
import { useSystemLog } from "./useSystemLog";
import {
  PaginationParams,
  PaginatedResponse,
  defaultQueryConfig,
} from "../utils/query-utils";

interface DataManagementOptions<T> {
  fetchData: (params: PaginationParams) => Promise<PaginatedResponse<T>>;
  searchFields?: (keyof T)[];
  initialFilter?: string;
  resourceName: string;
  defaultLimit?: number;
}

export function useDataManagement<T>({
  fetchData,
  searchFields = [],
  initialFilter = "all",
  resourceName,
  defaultLimit = 10,
}: DataManagementOptions<T>) {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentFilter, setCurrentFilter] = useState(initialFilter);
  const [currentPage, setCurrentPage] = useState(1);
  const queryClient = useQueryClient();
  const { logError } = useSystemLog();

  const queryKey = [
    resourceName,
    {
      page: currentPage,
      limit: defaultLimit,
      search: searchTerm,
      filter: currentFilter,
    },
  ];

  const { data, isLoading, error, refetch } = useQuery({
    queryKey,
    queryFn: async () => {
      try {
        const response = await fetchData({
          page: currentPage,
          limit: defaultLimit,
        });
        return response as PaginatedResponse<T>;
      } catch (error) {
        logError("DATA_FETCH_ERROR", error, `${resourceName} 데이터 로딩`);
        throw error;
      }
    },
    ...defaultQueryConfig,
  });

  const paginatedData = data as PaginatedResponse<T>;

  // Prefetch next page
  const prefetchNextPage = useCallback(() => {
    if (paginatedData && currentPage < paginatedData.totalPages) {
      const nextPageQueryKey = [
        resourceName,
        {
          page: currentPage + 1,
          limit: defaultLimit,
          search: searchTerm,
          filter: currentFilter,
        },
      ];

      queryClient.prefetchQuery({
        queryKey: nextPageQueryKey,
        queryFn: async () => {
          try {
            const response = await fetchData({
              page: currentPage + 1,
              limit: defaultLimit,
            });
            return response as PaginatedResponse<T>;
          } catch (error) {
            logError(
              "DATA_FETCH_ERROR",
              error,
              `${resourceName} 다음 페이지 프리페치`
            );
            throw error;
          }
        },
        ...defaultQueryConfig,
      });
    }
  }, [
    currentPage,
    paginatedData,
    queryClient,
    resourceName,
    searchTerm,
    currentFilter,
    defaultLimit,
    fetchData,
  ]);

  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset to first page on search
  }, []);

  const handleFilter = useCallback((filter: string) => {
    setCurrentFilter(filter);
    setCurrentPage(1); // Reset to first page on filter change
  }, []);

  const handlePageChange = useCallback(
    (page: number) => {
      setCurrentPage(page);
      // Prefetch next page when changing pages
      if (page < (paginatedData?.totalPages || 0)) {
        prefetchNextPage();
      }
    },
    [prefetchNextPage, paginatedData?.totalPages]
  );

  return {
    data: paginatedData?.data || [],
    total: paginatedData?.total || 0,
    currentPage: paginatedData?.currentPage || 1,
    totalPages: paginatedData?.totalPages || 1,
    isLoading,
    error,
    searchTerm,
    currentFilter,
    handleSearch,
    handleFilter,
    handlePageChange,
    refetch,
  };
}
