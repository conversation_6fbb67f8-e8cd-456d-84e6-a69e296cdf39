import { create } from "zustand";
import { supabase } from "@/lib/supabase";
import { createSystemLog } from "@/lib/utils/system-log";

export interface FarmMember {
  id: string;
  farm_id: string;
  user_id: string;
  role: "owner" | "manager" | "viewer";
  created_at: string;
  updated_at: string;
  // 프로필 정보
  email: string;
  company_name: string;
  representative_name: string;
}

interface FarmMembersState {
  members: FarmMember[];
  loading: boolean;
  initialized: boolean;
  fetchMembers: (farmId: string) => Promise<void>;
  addMember: (
    farmId: string,
    email: string,
    role: "manager" | "viewer"
  ) => Promise<void>;
  updateMemberRole: (
    memberId: string,
    role: "manager" | "viewer"
  ) => Promise<void>;
  removeMember: (memberId: string) => Promise<void>;
  reset: () => void;
}

export const useFarmMembersStore = create<FarmMembersState>((set, get) => ({
  members: [],
  loading: false,
  initialized: false,

  fetchMembers: async (farmId: string) => {
    try {
      set({ loading: true });

      const { data: members, error } = await supabase
        .from("farm_members")
        .select(
          `
          *,
          profiles:user_id (
            email,
            name
          )
        `
        )
        .eq("farm_id", farmId)
        .order("created_at", { ascending: true });

      if (error) throw error;

      // 클라이언트에서 권한별 우선순위로 정렬
      const sortedMembers =
        members?.sort((a, b) => {
          const roleOrder = { owner: 1, manager: 2, viewer: 3 };
          const aOrder = roleOrder[a.role as keyof typeof roleOrder] || 999;
          const bOrder = roleOrder[b.role as keyof typeof roleOrder] || 999;

          if (aOrder !== bOrder) {
            return aOrder - bOrder; // 권한 순서로 정렬
          }

          // 같은 권한이면 생성일 순서로 정렬
          return (
            new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
          );
        }) || [];

      const transformedMembers = sortedMembers.map((member) => ({
        ...member,
        email: member.profiles.email,
        company_name: "", // 사용하지 않음
        representative_name: member.profiles.name || "",
      }));

      set({ members: transformedMembers, initialized: true });
    } catch (error) {
      console.error("Error fetching farm members:", error);
      set({ members: [] });
    } finally {
      set({ loading: false });
    }
  },

  addMember: async (
    farmId: string,
    email: string,
    role: "manager" | "viewer"
  ) => {
    try {
      console.log("🚀 Starting addMember:", { farmId, email, role });

      // 이메일 형식 검증
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new Error("올바른 이메일 형식이 아닙니다.");
      }

      // 먼저 이메일로 사용자 찾기
      const { data: profiles, error: profileError } = await supabase
        .from("profiles")
        .select("id, email, name")
        .eq("email", email.toLowerCase().trim())
        .maybeSingle();

      console.log("👤 Profile search result:", { profiles, profileError });

      if (profileError) {
        if (profileError.code === "PGRST116") {
          throw new Error("해당 이메일로 가입된 사용자를 찾을 수 없습니다.");
        }
        throw new Error(
          `사용자 검색 중 오류가 발생했습니다: ${profileError.message}`
        );
      }

      if (!profiles) {
        throw new Error("해당 이메일로 가입된 사용자를 찾을 수 없습니다.");
      }

      // 이미 이 농장의 구성원인지 확인 (다른 농장 구성원은 OK)
      const { data: existingMember, error: memberCheckError } = await supabase
        .from("farm_members")
        .select("id, role")
        .eq("farm_id", farmId)
        .eq("user_id", profiles.id)
        .maybeSingle();

      console.log("🔍 Existing member check:", {
        farmId,
        userId: profiles.id,
        existingMember,
        memberCheckError,
      });

      if (memberCheckError) {
        throw new Error(
          `구성원 중복 확인 중 오류가 발생했습니다: ${memberCheckError.message}`
        );
      }

      if (existingMember) {
        const roleText =
          existingMember.role === "owner"
            ? "소유자"
            : existingMember.role === "manager"
            ? "관리자"
            : "조회자";
        throw new Error(`이미 이 농장의 ${roleText}입니다.`);
      }

      // 구성원 추가
      const { data: member, error: insertError } = await supabase
        .from("farm_members")
        .insert({
          farm_id: farmId,
          user_id: profiles.id,
          role,
        })
        .select()
        .single();

      console.log("✅ Insert result:", { member, insertError });

      if (insertError) {
        throw new Error(
          `구성원 추가 중 오류가 발생했습니다: ${insertError.message}`
        );
      }

      if (!member) {
        throw new Error("구성원 추가에 실패했습니다.");
      }

      const newMember: FarmMember = {
        ...member,
        email: profiles.email,
        company_name: "", // 사용하지 않음
        representative_name: profiles.name || "",
      };

      // 구성원 추가 성공 로그 생성
      await createSystemLog(
        "FARM_MEMBER_ADDED",
        `농장 구성원 "${
          newMember.representative_name || newMember.email
        }"이 "${role}" 권한으로 추가되었습니다`,
        "info",
        undefined,
        "farm",
        member.id,
        {
          farm_id: farmId,
          member_user_id: profiles.id,
          member_name: profiles.name,
          member_email: profiles.email,
          assigned_role: role,
          action_type: "member_added",
        }
      );

      set((state) => ({
        members: [...state.members, newMember],
      }));

      console.log("🎉 Member added successfully:", newMember);
    } catch (error: any) {
      console.error("Error adding farm member:", error);
      throw error;
    }
  },

  updateMemberRole: async (memberId: string, role: "manager" | "viewer") => {
    try {
      // 기존 구성원 정보 조회 (로그용)
      const { data: currentMember } = await supabase
        .from("farm_members")
        .select(
          `
          role,
          profiles:user_id (
            email,
            name
          )
        `
        )
        .eq("id", memberId)
        .single();

      const { data: member, error } = await supabase
        .from("farm_members")
        .update({ role })
        .eq("id", memberId)
        .select(
          `
          *,
          profiles:user_id (
            email,
            name
          )
        `
        )
        .single();

      if (error) throw error;

      const updatedMember: FarmMember = {
        ...member,
        email: member.profiles.email,
        company_name: "", // 사용하지 않음
        representative_name: member.profiles.name || "",
      };

      // 권한 변경 로그 생성
      await createSystemLog(
        "FARM_MEMBER_ROLE_CHANGED",
        `농장 구성원 "${
          updatedMember.representative_name || updatedMember.email
        }"의 권한이 "${
          currentMember?.role || "알 수 없음"
        }"에서 "${role}"로 변경되었습니다`,
        "info",
        undefined,
        "farm",
        memberId,
        {
          member_name: updatedMember.representative_name,
          member_email: updatedMember.email,
          previous_role: currentMember?.role,
          new_role: role,
          action_type: "role_changed",
        }
      );

      set((state) => ({
        members: state.members.map((m) =>
          m.id === memberId ? updatedMember : m
        ),
      }));
    } catch (error) {
      console.error("Error updating farm member role:", error);
      throw error;
    }
  },

  removeMember: async (memberId: string) => {
    try {
      // 삭제 전에 구성원 정보 조회 (로그용)
      const { data: memberToDelete } = await supabase
        .from("farm_members")
        .select(
          `
          role,
          profiles:user_id (
            email,
            name
          )
        `
        )
        .eq("id", memberId)
        .single();

      const { error } = await supabase
        .from("farm_members")
        .delete()
        .eq("id", memberId);

      if (error) throw error;

      // 구성원 제거 로그 생성
      await createSystemLog(
        "FARM_MEMBER_REMOVED",
        `농장 구성원 "${
          memberToDelete?.profiles?.name ||
          memberToDelete?.profiles?.email ||
          "알 수 없음"
        }" (${memberToDelete?.role || "알 수 없는 권한"})이 제거되었습니다`,
        "info",
        undefined,
        "farm",
        memberId,
        {
          member_name: memberToDelete?.profiles?.name,
          member_email: memberToDelete?.profiles?.email,
          removed_role: memberToDelete?.role,
          action_type: "member_removed",
        }
      );

      set((state) => ({
        members: state.members.filter((m) => m.id !== memberId),
      }));
    } catch (error) {
      console.error("Error removing farm member:", error);
      throw error;
    }
  },

  reset: () => {
    set({
      members: [],
      loading: false,
      initialized: false,
    });
  },
}));
