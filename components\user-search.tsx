import { useState, useEffect, useCallback } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useDebounce } from "@/hooks/use-debounce";
import { Loader2 } from "lucide-react";

interface User {
  id: string;
  email: string;
  name: string | null;
  profile_image_url: string | null;
}

interface UserSearchProps {
  farmId: string;
  onSelect: (user: User) => void;
}

export function UserSearch({ farmId, onSelect }: UserSearchProps) {
  const [query, setQuery] = useState("");
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const debouncedQuery = useDebounce(query, 300);

  const searchUsers = useCallback(
    async (searchQuery: string) => {
      if (!searchQuery.trim()) {
        setUsers([]);
        return;
      }

      setLoading(true);
      try {
        const response = await fetch(
          `/api/users/search?query=${encodeURIComponent(
            searchQuery
          )}&farmId=${farmId}`
        );
        const data = await response.json();
        if (Array.isArray(data)) {
          setUsers(data);
        }
      } catch (error) {
        console.error("Failed to search users:", error);
      } finally {
        setLoading(false);
      }
    },
    [farmId]
  );

  useEffect(() => {
    searchUsers(debouncedQuery);
  }, [debouncedQuery, searchUsers]);

  return (
    <div className="space-y-4">
      <div className="relative">
        <Input
          placeholder="이름 또는 이메일로 검색"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="w-full"
        />
        {loading && (
          <div className="absolute right-2 top-2">
            <Loader2 className="h-5 w-5 animate-spin text-gray-400" />
          </div>
        )}
      </div>

      {users.length > 0 && (
        <div className="space-y-2">
          {users.map((user) => (
            <Card key={user.id} className="overflow-hidden">
              <CardContent className="p-4 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Avatar>
                    <AvatarImage src={user.profile_image_url || undefined} />
                    <AvatarFallback>
                      {user.name?.[0]?.toUpperCase() ||
                        user.email[0].toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">
                      {user.name || "이름 없음"}
                    </div>
                    <div className="text-sm text-gray-500">{user.email}</div>
                  </div>
                </div>
                <Button onClick={() => onSelect(user)}>추가</Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {query && !loading && users.length === 0 && (
        <div className="text-center text-gray-500 py-4">
          검색 결과가 없습니다
        </div>
      )}
    </div>
  );
}
