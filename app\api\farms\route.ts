import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const {
      data: { session },
      error: authError,
    } = await supabase.auth.getSession();

    if (authError || !session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { name, address, farm_type, biosecurity_officer_contact, notes } =
      await request.json();

    // Start a transaction
    const { data: farm, error: farmError } = await supabase
      .from("farms")
      .insert({
        name,
        address,
        farm_type,
        biosecurity_officer_contact,
        notes,
        owner_id: session.user.id,
      })
      .select()
      .single();

    if (farmError) {
      throw farmError;
    }

    // 새로운 권한 시스템에서는 profiles.account_type은 시스템 레벨 권한만 관리
    // 농장 소유자 권한은 farms 테이블의 owner_id로 관리됨
    // 따라서 profiles.role 업데이트는 더 이상 필요하지 않음

    return NextResponse.json({ farm }, { status: 201 });
  } catch (error) {
    console.error("Error creating farm:", error);
    return NextResponse.json(
      { error: "Failed to create farm" },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const {
      data: { session },
      error: authError,
    } = await supabase.auth.getSession();

    if (authError || !session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { data: farms, error } = await supabase
      .from("farms")
      .select("*")
      .eq("owner_id", session.user.id);

    if (error) {
      throw error;
    }

    return NextResponse.json({ farms });
  } catch (error) {
    console.error("Error fetching farms:", error);
    return NextResponse.json(
      { error: "Failed to fetch farms" },
      { status: 500 }
    );
  }
}
