import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("query");
    const farmId = searchParams.get("farmId");

    if (!query || !farmId) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    const supabase = createRouteHandlerClient({ cookies });

    // 현재 농장에 이미 추가된 사용자를 제외하고 검색
    const { data: users, error } = await supabase
      .from("users")
      .select("id, email, name, avatar_url")
      .or(`name.ilike.%${query}%,email.ilike.%${query}%`)
      .not(
        "id",
        "in",
        supabase.from("farm_members").select("user_id").eq("farm_id", farmId)
      )
      .limit(5);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(users);
  } catch (error) {
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
