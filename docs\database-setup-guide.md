# 데이터베이스 완전 재구축 가이드

## 🗄️ 개선된 Role 시스템 데이터베이스 스키마

### **📋 주요 개선사항:**

- **시스템 권한과 농장 권한 분리**
- **단순화된 role 구조**
- **완전한 RLS 보안 정책**
- **자동화된 로깅 시스템**
- **성능 최적화된 인덱스**

## 🚀 실행 방법

### **1단계: 백업 (중요!)**

```bash
# 기존 데이터 백업 (필요한 경우)
pg_dump -h localhost -U postgres -d your_database > backup_$(date +%Y%m%d_%H%M%S).sql
```

### **2단계: 스크립트 실행**

```bash
# Supabase CLI 사용
supabase db reset
supabase db push

# 또는 직접 SQL 실행
psql -h your-host -U your-user -d your-database -f scripts/database-reset-and-rebuild.sql
```

### **3단계: 확인**

```sql
-- 테이블 생성 확인
\dt public.*

-- 함수 생성 확인
\df public.*

-- RLS 정책 확인
SELECT * FROM pg_policies WHERE schemaname = 'public';
```

## 📊 새로운 테이블 구조

### **1. profiles (사용자 프로필)**

```sql
- id: UUID (Primary Key)
- email: TEXT (Unique)
- name: TEXT
- account_type: 'admin' | 'user' (시스템 레벨 권한)
- company_name, company_address, business_type
- position, department, bio, profile_image_url
- last_login_at, login_count, is_active
- created_at, updated_at
```

### **2. farms (농장)**

```sql
- id: UUID (Primary Key)
- farm_name: TEXT
- address, postal_code, detailed_address
- farm_type, area_size, established_date
- owner_id: UUID (Foreign Key to profiles)
- contact_phone, contact_email
- is_active, created_at, updated_at
```

### **3. farm_members (농장 구성원)**

```sql
- id: UUID (Primary Key)
- farm_id: UUID (Foreign Key to farms)
- user_id: UUID (Foreign Key to profiles)
- role: 'manager' | 'viewer' (농장별 권한)
- position, responsibilities
- is_active, created_at, updated_at
```

### **4. visitor_entries (방문자 기록)**

```sql
- id: UUID (Primary Key)
- farm_id: UUID (Foreign Key to farms)
- visitor_name, visitor_phone, visitor_company
- visit_date, visit_time, departure_time
- vehicle_number, vehicle_type
- notes, visitor_count
- registered_by: UUID (Foreign Key to profiles)
- created_at, updated_at
```

### **5. system_logs (시스템 로그)**

```sql
- id: UUID (Primary Key)
- level: 'error' | 'warn' | 'info' | 'debug'
- action, message
- user_id, user_email, user_ip, user_agent
- resource_type, resource_id
- metadata: JSONB
- created_at
```

## 🔐 권한 시스템

### **시스템 레벨 권한:**

- **admin**: 시스템 관리자 (모든 농장/사용자 관리)
- **user**: 일반 사용자 (자신 관련 농장만 접근)

### **농장 레벨 권한:**

- **owner**: 농장 소유자 (모든 농장 권한)
- **manager**: 농장 관리자 (방문자 관리, 농장 정보 수정)
- **viewer**: 조회자 (방문자 목록 조회만)
- **none**: 권한 없음

### **사용자 카테고리:**

- **system_admin**: 시스템 관리자
- **farm_owner**: 농장 소유자
- **farm_member**: 농장 구성원
- **general_user**: 일반 사용자 (농장에 속하지 않음)

## 🛠️ 주요 함수들

### **권한 확인 함수:**

```sql
-- 시스템 관리자 확인
SELECT public.is_system_admin('user-uuid');

-- 농장별 권한 확인
SELECT public.get_farm_role('user-uuid', 'farm-uuid');

-- 농장 관리 권한 확인
SELECT public.can_manage_farm('user-uuid', 'farm-uuid');

-- 농장 조회 권한 확인
SELECT public.can_view_farm('user-uuid', 'farm-uuid');

-- 접근 가능한 농장 목록
SELECT * FROM public.get_user_accessible_farms('user-uuid');

-- 사용자 상태 확인
SELECT * FROM public.get_user_status('user-uuid');
```

## 📈 유용한 뷰들

### **1. user_farm_permissions**

```sql
-- 사용자별 농장 권한 종합 정보
SELECT * FROM user_farm_permissions WHERE user_id = 'user-uuid';
```

### **2. role_statistics**

```sql
-- 권한별 사용자 통계
SELECT * FROM role_statistics;
```

### **3. farm_statistics**

```sql
-- 농장별 통계 (구성원 수, 방문자 수 등)
SELECT * FROM farm_statistics;
```

## 🔒 보안 정책 (RLS)

### **자동 적용되는 보안 규칙:**

- 사용자는 자신의 프로필만 조회/수정 가능
- 농장 소유자/구성원만 해당 농장 접근 가능
- 시스템 관리자는 모든 데이터 접근 가능
- 방문자 기록은 농장 권한에 따라 접근 제한
- 시스템 로그는 본인 관련 로그만 조회 가능

## 🔄 자동화 기능

### **자동 트리거:**

- 새 사용자 가입 시 프로필 자동 생성
- 데이터 수정 시 updated_at 자동 업데이트
- 농장 생성/수정/삭제 시 자동 로그 생성

## 📝 사용 예시

### **1. 새 사용자 등록:**

```sql
-- 회원가입 시 자동으로 profiles 테이블에 생성됨
-- account_type: 'user', user_category: 'general_user'
```

### **2. 농장 등록:**

```sql
INSERT INTO public.farms (farm_name, address, owner_id)
VALUES ('테스트 농장', '서울시 강남구', 'user-uuid');
-- 자동으로 owner 권한 부여됨
```

### **3. 구성원 추가:**

```sql
INSERT INTO public.farm_members (farm_id, user_id, role)
VALUES ('farm-uuid', 'member-uuid', 'manager');
-- 해당 사용자가 farm_member 카테고리로 변경됨
```

### **4. 권한 확인:**

```sql
-- 사용자가 특정 농장을 관리할 수 있는지 확인
SELECT public.can_manage_farm('user-uuid', 'farm-uuid');
```

## ⚠️ 주의사항

### **1. 데이터 손실 방지:**

- 반드시 기존 데이터를 백업한 후 실행
- 테스트 환경에서 먼저 검증

### **2. 환경 변수 확인:**

- Supabase 프로젝트 설정 확인
- 데이터베이스 연결 정보 확인

### **3. 권한 설정:**

- RLS 정책이 올바르게 적용되었는지 확인
- 함수 실행 권한 확인

이제 완전히 새로운 구조의 데이터베이스가 구축됩니다!
