"use client";

import { useState, useEffect } from "react";
import { supabase } from "@/lib/supabase";

/**
 * 농장 구성원 미리보기 데이터 인터페이스
 */
export interface FarmMemberPreview {
  id: string;
  name: string;
  role: "owner" | "manager" | "viewer";
  email: string;
}

/**
 * 농장별 구성원 데이터 인터페이스
 */
export interface FarmMembersData {
  [farmId: string]: {
    count: number;
    members: FarmMemberPreview[];
    loading: boolean;
  };
}

/**
 * 농장 구성원 미리보기 훅
 *
 * 여러 농장의 구성원 정보를 동시에 로드하고 관리합니다.
 *
 * @param farmIds - 조회할 농장 ID 배열
 * @returns 농장별 구성원 데이터와 유틸리티 함수
 */
export function useFarmMembersPreview(farmIds: string[]) {
  const [membersData, setMembersData] = useState<FarmMembersData>({});

  useEffect(() => {
    if (farmIds.length === 0) return;

    const fetchFarmMembers = async () => {
      // 모든 농장의 로딩 상태를 true로 설정
      setMembersData((prev) => {
        const newData = { ...prev };
        farmIds.forEach((farmId) => {
          newData[farmId] = {
            count: 0,
            members: [],
            loading: true,
          };
        });
        return newData;
      });

      try {
        // 모든 농장의 구성원 정보를 한 번에 가져오기
        const { data: members, error } = await supabase
          .from("farm_members")
          .select(
            `
            id,
            farm_id,
            role,
            profiles:user_id (
              id,
              email,
              name
            )
          `
          )
          .in("farm_id", farmIds);

        if (error) throw error;

        // 농장별로 구성원 데이터 그룹화
        const groupedMembers: FarmMembersData = {};

        farmIds.forEach((farmId) => {
          const farmMembers =
            members?.filter((member) => member.farm_id === farmId) || [];

          groupedMembers[farmId] = {
            count: farmMembers.length,
            members: farmMembers.slice(0, 3).map((member: any) => ({
              id: member.id,
              name: member.profiles?.name || "Unknown",
              role: member.role as "owner" | "manager" | "viewer",
              email: member.profiles?.email || "",
            })),
            loading: false,
          };
        });

        setMembersData(groupedMembers);
      } catch (error) {
        console.error("Error fetching farm members:", error);

        // 에러 발생 시 로딩 상태를 false로 설정
        setMembersData((prev) => {
          const newData = { ...prev };
          farmIds.forEach((farmId) => {
            newData[farmId] = {
              count: 0,
              members: [],
              loading: false,
            };
          });
          return newData;
        });
      }
    };

    fetchFarmMembers();
  }, [farmIds.join(",")]);

  const getMembersForFarm = (farmId: string) => {
    return (
      membersData[farmId] || {
        count: 0,
        members: [],
        loading: true,
      }
    );
  };

  return {
    membersData,
    getMembersForFarm,
  };
}
