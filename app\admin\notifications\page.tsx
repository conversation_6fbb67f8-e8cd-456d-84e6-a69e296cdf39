"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Bell, Mail, Save, Settings } from "lucide-react";
import { motion } from "framer-motion";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import Link from "next/link";
import { Home } from "lucide-react";

export default function NotificationsPage() {
  const [settings, setSettings] = useState({
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    visitorAlerts: true,
    dailyReports: true,
    weeklyReports: false,
    emergencyAlerts: true,
    maintenanceAlerts: true,
    emailAddress: "<EMAIL>",
    phoneNumber: "010-1234-5678",
    notificationTime: "09:00",
    alertFrequency: "immediate",
  });

  const handleSave = () => {
    // 로컬 스토리지에 설정 저장
    localStorage.setItem("notification-settings", JSON.stringify(settings));
    alert("알림 설정이 저장되었습니다.");
  };

  const handleSettingChange = (key: string, value: any) => {
    setSettings((prev) => ({ ...prev, [key]: value }));
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-6 pt-2 md:pt-4">
      {/* 브레드크럼 */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/admin/dashboard" className="flex items-center gap-1">
                <Home className="h-4 w-4" />
                대시보드
              </Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>알림 설정</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <SidebarTrigger />
          <div>
            <h2 className="text-3xl font-bold tracking-tight mb-4 flex items-center gap-2">
              <Bell className="h-8 w-8 text-primary" />
              알림 설정
            </h2>
            <p className="text-muted-foreground mt-2">
              방문자 알림 및 보고서 설정을 관리하세요
            </p>
          </div>
        </div>
        <Button onClick={handleSave}>
          <Save className="mr-2 h-4 w-4" />
          설정 저장
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* 알림 유형 설정 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                알림 유형
              </CardTitle>
              <CardDescription>
                받고 싶은 알림 유형을 선택하세요
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">이메일 알림</Label>
                  <p className="text-sm text-muted-foreground">
                    이메일로 알림을 받습니다
                  </p>
                </div>
                <Switch
                  checked={settings.emailNotifications}
                  onCheckedChange={(checked) =>
                    handleSettingChange("emailNotifications", checked)
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">SMS 알림</Label>
                  <p className="text-sm text-muted-foreground">
                    문자 메시지로 알림을 받습니다
                  </p>
                </div>
                <Switch
                  checked={settings.smsNotifications}
                  onCheckedChange={(checked) =>
                    handleSettingChange("smsNotifications", checked)
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">푸시 알림</Label>
                  <p className="text-sm text-muted-foreground">
                    브라우저 푸시 알림을 받습니다
                  </p>
                </div>
                <Switch
                  checked={settings.pushNotifications}
                  onCheckedChange={(checked) =>
                    handleSettingChange("pushNotifications", checked)
                  }
                />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">방문자 알림</Label>
                  <p className="text-sm text-muted-foreground">
                    새 방문자 등록 시 알림
                  </p>
                </div>
                <Switch
                  checked={settings.visitorAlerts}
                  onCheckedChange={(checked) =>
                    handleSettingChange("visitorAlerts", checked)
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">긴급 알림</Label>
                  <p className="text-sm text-muted-foreground">
                    긴급 상황 발생 시 알림
                  </p>
                </div>
                <Switch
                  checked={settings.emergencyAlerts}
                  onCheckedChange={(checked) =>
                    handleSettingChange("emergencyAlerts", checked)
                  }
                />
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* 보고서 설정 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                보고서 설정
              </CardTitle>
              <CardDescription>정기 보고서 및 알림 설정</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">일일 보고서</Label>
                  <p className="text-sm text-muted-foreground">
                    매일 방문자 현황 보고서
                  </p>
                </div>
                <Switch
                  checked={settings.dailyReports}
                  onCheckedChange={(checked) =>
                    handleSettingChange("dailyReports", checked)
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">주간 보고서</Label>
                  <p className="text-sm text-muted-foreground">
                    주간 통계 및 분석 보고서
                  </p>
                </div>
                <Switch
                  checked={settings.weeklyReports}
                  onCheckedChange={(checked) =>
                    handleSettingChange("weeklyReports", checked)
                  }
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="notificationTime">보고서 발송 시간</Label>
                <Input
                  id="notificationTime"
                  type="time"
                  value={settings.notificationTime}
                  onChange={(e) =>
                    handleSettingChange("notificationTime", e.target.value)
                  }
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="alertFrequency">알림 빈도</Label>
                <Select
                  value={settings.alertFrequency}
                  onValueChange={(value) =>
                    handleSettingChange("alertFrequency", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="immediate">즉시</SelectItem>
                    <SelectItem value="hourly">1시간마다</SelectItem>
                    <SelectItem value="daily">하루에 한 번</SelectItem>
                    <SelectItem value="weekly">일주일에 한 번</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* 연락처 정보 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="md:col-span-2"
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                연락처 정보
              </CardTitle>
              <CardDescription>
                알림을 받을 연락처 정보를 설정하세요
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="emailAddress">이메일 주소</Label>
                  <Input
                    id="emailAddress"
                    type="email"
                    value={settings.emailAddress}
                    onChange={(e) =>
                      handleSettingChange("emailAddress", e.target.value)
                    }
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phoneNumber">전화번호</Label>
                  <Input
                    id="phoneNumber"
                    type="tel"
                    value={settings.phoneNumber}
                    onChange={(e) =>
                      handleSettingChange("phoneNumber", e.target.value)
                    }
                    placeholder="010-1234-5678"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
