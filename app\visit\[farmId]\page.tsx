/**
 * 방문자 등록 페이지
 *
 * QR 코드를 스캔한 방문자가 농장 방문 정보를 등록하는 페이지입니다.
 *
 * 주요 기능:
 * - 농장 정보 표시 (농장명, 관리자, 연락처)
 * - 축사출입금지 안내문구 표시
 * - 방문자 정보 입력 폼 (성명, 연락처, 주소, 차량번호, 방문목적, 소독여부, 비고)
 * - 카카오 주소 API 연동
 * - 개인정보 수집 동의
 * - 등록 완료 후 회사 홍보 페이지 연결
 *
 * @route /visit/[farmId]
 * @param farmId - 농장 고유 식별자 (UUID)
 */

"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Shield,
  Phone,
  MapPin,
  Clock,
  AlertTriangle,
  CheckCircle,
  Loader2,
  User,
  Car,
  FileText,
} from "lucide-react";
import { supabase } from "@/lib/supabase";
import { AddressSearch } from "@/components/address-search";
import { createVisitorLog, createErrorLog } from "@/lib/utils/system-log";

/**
 * 농장 정보 인터페이스
 * 데이터베이스의 farms 테이블과 매핑됩니다.
 */
interface Farm {
  id: string; // 농장 고유 식별자 (UUID)
  farm_name: string; // 농장명
  farm_address: string; // 농장 주소
  manager_name: string; // 관리자 이름
  manager_phone: string; // 관리자 연락처
  farm_type?: string; // 농장 유형
}

/**
 * 방문자 폼 데이터 인터페이스
 * 사용자가 입력하는 방문자 정보를 관리합니다.
 */
interface VisitorFormData {
  fullName: string; // 방문자 성명 (필수)
  phoneNumber: string; // 연락처 (필수)
  address: string; // 기본 주소 (카카오 API에서 선택, 필수)
  detailedAddress: string; // 상세 주소 (사용자 입력)
  carPlateNumber: string; // 차량번호 (선택)
  visitPurpose: string; // 방문목적 (필수)
  disinfectionCheck: boolean; // 소독 완료 여부
  notes: string; // 비고 (선택)
  consentGiven: boolean; // 개인정보 수집 동의 (필수)
}

/**
 * 방문자 등록 페이지 메인 컴포넌트
 *
 * QR 코드 스캔을 통해 접근하는 방문자 등록 폼을 제공합니다.
 * 농장 정보 표시, 방문자 정보 입력, 데이터 저장 기능을 포함합니다.
 */
export default function VisitPage() {
  // URL 파라미터에서 농장 ID 추출
  const params = useParams();
  const farmId = params.farmId as string;

  // 컴포넌트 상태 관리
  const [farm, setFarm] = useState<Farm | null>(null); // 농장 정보
  const [loading, setLoading] = useState(true); // 초기 로딩 상태
  const [submitting, setSubmitting] = useState(false); // 폼 제출 중 상태
  const [submitted, setSubmitted] = useState(false); // 제출 완료 상태
  const [error, setError] = useState<string | null>(null); // 오류 메시지

  // 방문자 폼 데이터 상태 (초기값 설정)
  const [formData, setFormData] = useState<VisitorFormData>({
    fullName: "",
    phoneNumber: "",
    address: "",
    detailedAddress: "",
    carPlateNumber: "",
    visitPurpose: "",
    disinfectionCheck: false,
    notes: "",
    consentGiven: false,
  });

  /**
   * 농장 정보를 데이터베이스에서 로드하는 useEffect
   *
   * farmId가 변경될 때마다 실행되며, 해당 농장의 기본 정보를
   * (농장명, 주소, 관리자 정보 등)를 가져와서 상태에 저장합니다.
   */
  useEffect(() => {
    /**
     * 농장 정보를 비동기로 가져오는 함수
     *
     * @throws {Error} 농장 정보 조회 실패 시
     */
    const fetchFarm = async () => {
      try {
        // Supabase에서 농장 정보 조회
        const { data, error } = await supabase
          .from("farms")
          .select(
            "id, farm_name, farm_address, manager_name, manager_phone, farm_type"
          )
          .eq("id", farmId)
          .single();

        if (error) throw error;
        setFarm(data);
      } catch (err) {
        setError("농장 정보를 불러올 수 없습니다.");
        console.error("Error fetching farm:", err);
      } finally {
        setLoading(false);
      }
    };

    // farmId가 존재할 때만 농장 정보 로드
    if (farmId) {
      fetchFarm();
    }
  }, [farmId]);

  /**
   * 창 닫기 함수 (수동 방식)
   *
   * 브라우저 호환성 문제로 인해 자동 닫기 대신 수동 닫기 방식을 사용합니다.
   * 다양한 브라우저 환경에 대응하여 적절한 닫기 방법을 시도합니다.
   *
   * 동작 순서:
   * 1. QR 스캔 앱이나 팝업에서 열린 경우 window.close() 시도
   * 2. 일반 브라우저 탭인 경우 히스토리 백 시도
   * 3. 히스토리가 없는 경우 홈페이지로 이동
   * 4. 모든 방법이 실패하면 사용자에게 수동 닫기 안내
   */
  const handleClose = () => {
    try {
      // QR 스캔 앱이나 팝업에서 열린 경우 (window.opener가 존재)
      if (window.opener) {
        window.close();
      } else {
        // 일반 브라우저 탭인 경우 히스토리 백 또는 홈으로 이동
        if (window.history.length > 1) {
          window.history.back();
        } else {
          window.location.href = "/";
        }
      }
    } catch (error) {
      // 창 닫기가 실패한 경우 사용자에게 안내
      alert("브라우저의 뒤로가기 버튼을 사용하거나 직접 창을 닫아주세요.");
    }
  };

  /**
   * 폼 입력 데이터 변경 핸들러
   *
   * 방문자 폼의 각 필드 값이 변경될 때 호출되는 함수입니다.
   * 불변성을 유지하면서 상태를 업데이트합니다.
   *
   * @param field - 변경할 폼 필드명 (VisitorFormData의 키)
   * @param value - 새로운 값 (문자열 또는 불린)
   *
   * @example
   * // 텍스트 입력 시
   * handleInputChange('fullName', '홍길동')
   *
   * // 체크박스 변경 시
   * handleInputChange('disinfectionCheck', true)
   */
  const handleInputChange = (
    field: keyof VisitorFormData,
    value: string | boolean
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  /**
   * 방문자 등록 폼 제출 핸들러
   *
   * 사용자가 방문자 등록 폼을 제출할 때 호출되는 함수입니다.
   * 입력 데이터 검증, 데이터베이스 저장, 오류 처리를 담당합니다.
   *
   * 처리 순서:
   * 1. 폼 기본 제출 동작 방지
   * 2. 필수 항목 검증 (성명, 연락처, 주소, 방문목적, 개인정보 동의)
   * 3. 데이터베이스에 방문자 기록 저장
   * 4. 성공 시 완료 화면 표시, 실패 시 오류 메시지 표시
   *
   * @param e - React 폼 이벤트 객체
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault(); // 폼 기본 제출 동작 방지

    // 필수 항목 검증 - 성명
    if (!formData.fullName.trim()) {
      setError("성명을 입력해주세요.");
      return;
    }

    // 필수 항목 검증 - 연락처
    if (!formData.phoneNumber.trim()) {
      setError("연락처를 입력해주세요.");
      return;
    }

    // 필수 항목 검증 - 주소 (카카오 API에서 검색)
    if (!formData.address.trim()) {
      setError("주소를 검색해주세요.");
      return;
    }

    // 필수 항목 검증 - 방문목적
    if (!formData.visitPurpose.trim()) {
      setError("방문목적을 입력해주세요.");
      return;
    }

    // 필수 항목 검증 - 개인정보 수집 동의
    if (!formData.consentGiven) {
      setError("개인정보 수집 및 이용에 동의해주세요.");
      return;
    }

    // 제출 상태 설정 (로딩 표시)
    setSubmitting(true);
    setError(null);

    try {
      const insertData = {
        farm_id: farmId,
        visit_datetime: new Date().toISOString(),
        visitor_name: formData.fullName.trim(),
        visitor_phone: formData.phoneNumber.trim(),
        visitor_address: `${formData.address.trim()}${
          formData.detailedAddress.trim()
            ? " " + formData.detailedAddress.trim()
            : ""
        }`,
        vehicle_number: formData.carPlateNumber.trim() || null,
        visitor_purpose: formData.visitPurpose.trim(),
        disinfection_check: formData.disinfectionCheck,
        notes: formData.notes.trim() || null,
        consent_given: formData.consentGiven,
      };

      console.log("방문자 등록 데이터:", insertData);

      const { data: insertResult, error: insertError } = await supabase
        .from("visitor_entries")
        .insert(insertData)
        .select(); // 삽입된 데이터 반환

      console.log("방문자 등록 결과:", { insertResult, insertError });

      if (insertError) {
        console.error("방문자 등록 오류:", insertError);
        throw insertError;
      }

      console.log("방문자 등록 성공!");

      // 방문자 등록 성공 로그 생성
      await createVisitorLog(
        "CREATED",
        formData.fullName,
        farm?.farm_name,
        insertResult?.[0]?.id,
        "visitor", // 방문자 등록은 visitor 사용자로 처리
        `방문자 "${formData.fullName}"이 농장 "${farm?.farm_name}"에 등록되었습니다 (연락처: ${formData.phoneNumber}, 방문목적: ${formData.visitPurpose})`
      );

      setSubmitted(true);
    } catch (err: any) {
      console.error("방문자 등록 실패:", err);

      // 방문자 등록 실패 로그 생성
      await createErrorLog(
        "VISITOR_REGISTRATION_FAILED",
        err,
        `방문자 "${formData.fullName}" 등록 실패 - 농장: ${farm?.farm_name}`,
        "visitor"
      );

      setError("방문 등록에 실패했습니다. 다시 시도해주세요.");
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>농장 정보를 불러오는 중...</p>
        </div>
      </div>
    );
  }

  if (!farm) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-center text-red-600">오류</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-center">농장 정보를 찾을 수 없습니다.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (submitted) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-center text-green-600 flex items-center justify-center gap-2">
              <CheckCircle className="h-6 w-6" />
              등록 완료
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <div className="space-y-3">
              <p className="text-lg font-semibold">
                방문 등록이 완료되었습니다!
              </p>
              <p className="text-sm text-muted-foreground">
                농장 관리자에게 알림이 전송되었습니다.
              </p>

              {/* 회사 브랜딩 */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 p-3 rounded-lg">
                <div className="text-center">
                  <p className="text-sm font-medium text-blue-800">
                    🏢 농장 방문자 관리 시스템
                  </p>
                  <p className="text-xs text-blue-600 mt-1">
                    Powered by <span className="font-bold">SWK KOREA</span>
                  </p>
                </div>
              </div>
            </div>

            <Badge variant="outline" className="text-sm">
              등록 시간: {new Date().toLocaleString("ko-KR")}
            </Badge>

            {/* 완료 안내 */}
            <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
              <div className="flex items-center justify-center gap-2 text-green-700">
                <CheckCircle className="h-5 w-5" />
                <span className="font-medium">
                  방문 등록이 성공적으로 완료되었습니다!
                </span>
              </div>
              <p className="text-center text-sm text-green-600 mt-2">
                이제 안전하게 창을 닫으시거나 회사 정보를 확인해보세요.
              </p>
            </div>

            <div className="flex gap-2 pt-2">
              <Button
                onClick={handleClose}
                className="flex-1"
                variant="default"
                size="lg"
              >
                창 닫기
              </Button>
              <Button
                onClick={() => {
                  // 회사 웹사이트로 이동
                  window.open("http://www.swkukorea.com/", "_blank");
                }}
                className="flex-1"
                variant="outline"
                size="lg"
              >
                회사 소개 보기
              </Button>
            </div>

            <div className="text-xs text-muted-foreground mt-4 space-y-1 text-center">
              <p>💡 추가 방문자가 있으시면 QR 코드를 다시 스캔해주세요.</p>
              <p>
                🌐 농장 방문자 관리 시스템은{" "}
                <span className="font-semibold text-blue-600">SWK KOREA</span>
                에서 제공합니다.
              </p>
              <p className="text-gray-400">
                📱 모바일에서는 브라우저의 뒤로가기 버튼을 사용하세요.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-2xl">
        {/* 농장 정보 및 안내사항 */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-xl">
              <Shield className="h-6 w-6 text-red-500" />
              {farm.farm_name}
            </CardTitle>
            <CardDescription className="flex items-center gap-1">
              <MapPin className="h-4 w-4" />
              {farm.farm_address}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* 축사출입금지 안내 */}
            <Alert className="mb-4 border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="font-semibold text-red-700">
                🚫 축사출입금지 - 방역상 출입을 금지합니다
              </AlertDescription>
            </Alert>

            {/* 연락처 안내 */}
            <div className="space-y-3">
              <p className="font-medium text-gray-700">
                용무가 있으신 분은 아래로 연락바랍니다:
              </p>
              <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium text-gray-600">농장명:</span>
                  <span className="font-semibold">{farm.farm_name}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="font-medium text-gray-600">관리자:</span>
                  <span className="font-semibold">{farm.manager_name}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="font-medium text-gray-600">연락처:</span>
                  <span className="font-semibold flex items-center gap-1">
                    <Phone className="h-4 w-4" />
                    {farm.manager_phone}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 방문자 등록 폼 */}
        <Card>
          <CardHeader>
            <CardTitle>방문자 등록</CardTitle>
            <CardDescription>
              방문 정보를 정확히 입력해주세요. 모든 정보는 방역 관리 목적으로만
              사용됩니다.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* 출입일시 - 자동입력, 비활성화 */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2 font-medium">
                  <Clock className="h-4 w-4" />
                  출입일시
                </Label>
                <Input
                  value={new Date().toLocaleString("ko-KR", {
                    year: "numeric",
                    month: "2-digit",
                    day: "2-digit",
                    hour: "2-digit",
                    minute: "2-digit",
                    second: "2-digit",
                  })}
                  disabled
                  className="bg-gray-100 text-gray-700"
                />
              </div>

              {/* 성명 - 필수 */}
              <div className="space-y-2">
                <Label
                  htmlFor="fullName"
                  className="flex items-center gap-2 font-medium"
                >
                  <User className="h-4 w-4" />
                  성명 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="fullName"
                  value={formData.fullName}
                  onChange={(e) =>
                    handleInputChange("fullName", e.target.value)
                  }
                  placeholder="홍길동"
                  required
                  className="h-12"
                />
              </div>

              {/* 연락처 - 필수 */}
              <div className="space-y-2">
                <Label
                  htmlFor="phoneNumber"
                  className="flex items-center gap-2 font-medium"
                >
                  <Phone className="h-4 w-4" />
                  연락처 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={(e) =>
                    handleInputChange("phoneNumber", e.target.value)
                  }
                  placeholder="010-1234-5678"
                  required
                  className="h-12"
                />
              </div>

              {/* 주소 - 필수 */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2 font-medium">
                  <MapPin className="h-4 w-4" />
                  주소 <span className="text-red-500">*</span>
                </Label>
                <AddressSearch
                  onSelect={(address, detailedAddress) => {
                    handleInputChange("address", address);
                    handleInputChange("detailedAddress", detailedAddress);
                  }}
                  defaultDetailedAddress={formData.detailedAddress}
                />
                {formData.address && (
                  <div className="mt-2 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                    <div className="text-sm">
                      <div className="font-medium text-gray-700">
                        선택된 주소:
                      </div>
                      <div className="text-gray-600 mt-1">
                        {formData.address}
                        {formData.detailedAddress && (
                          <span className="text-blue-600">
                            {" "}
                            {formData.detailedAddress}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* 차량번호 - 선택 */}
              <div className="space-y-2">
                <Label
                  htmlFor="carPlateNumber"
                  className="flex items-center gap-2 font-medium"
                >
                  <Car className="h-4 w-4" />
                  차량번호
                </Label>
                <Input
                  id="carPlateNumber"
                  value={formData.carPlateNumber}
                  onChange={(e) =>
                    handleInputChange(
                      "carPlateNumber",
                      e.target.value.toUpperCase()
                    )
                  }
                  placeholder="12가 3456 (선택사항)"
                  className="h-12 uppercase"
                />
              </div>

              {/* 방문목적 - 필수 */}
              <div className="space-y-2">
                <Label
                  htmlFor="visitPurpose"
                  className="flex items-center gap-2 font-medium"
                >
                  <FileText className="h-4 w-4" />
                  방문목적 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="visitPurpose"
                  value={formData.visitPurpose}
                  onChange={(e) =>
                    handleInputChange("visitPurpose", e.target.value)
                  }
                  placeholder="사료 배송, 수의사 진료, 점검 등"
                  required
                  className="h-12"
                />
              </div>

              {/* 소독여부 */}
              <div className="flex items-center space-x-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                <Checkbox
                  id="disinfectionCheck"
                  checked={formData.disinfectionCheck}
                  onCheckedChange={(checked) =>
                    handleInputChange("disinfectionCheck", !!checked)
                  }
                />
                <Label
                  htmlFor="disinfectionCheck"
                  className="flex items-center gap-2 font-medium"
                >
                  <Shield className="h-4 w-4 text-green-600" />
                  소독여부: 소독을 완료했습니다
                </Label>
              </div>

              {/* 비고 */}
              <div className="space-y-2">
                <Label htmlFor="notes" className="font-medium">
                  비고
                </Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  placeholder="추가 사항이 있으면 입력해주세요"
                  rows={3}
                  className="resize-none"
                />
              </div>

              <Separator />

              {/* 개인정보 동의 */}
              <div className="space-y-4">
                <div className="flex items-start space-x-3 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <Checkbox
                    id="consentGiven"
                    checked={formData.consentGiven}
                    onCheckedChange={(checked) =>
                      handleInputChange("consentGiven", !!checked)
                    }
                    className="mt-1"
                  />
                  <Label
                    htmlFor="consentGiven"
                    className="text-sm leading-relaxed"
                  >
                    <span className="font-medium">
                      개인정보 수집 및 이용에 동의합니다.
                    </span>
                    <span className="text-red-500 ml-1">*</span>
                    <br />
                    <span className="text-xs text-muted-foreground mt-1 block">
                      수집된 정보는 방역 관리 목적으로만 사용되며, 관련 법령에
                      따라 보관됩니다.
                    </span>
                  </Label>
                </div>
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Button
                type="submit"
                className="w-full h-12 text-lg font-semibold"
                disabled={submitting}
              >
                {submitting ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    등록 중...
                  </>
                ) : (
                  "방문 등록"
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
