// 인증 관련 헬퍼 함수들
import { supabase } from "./supabase";

/**
 * 로그인 시 last_login_at 업데이트 (SQL 함수 사용)
 */
export async function updateLoginTime(userId: string) {
  try {
    const { error } = await supabase.rpc("update_login_stats", {
      input_user_id: userId,
    });

    if (error) {
      console.error("Failed to update login time:", error);
      return false;
    }

    console.log("Login time updated successfully");
    return true;
  } catch (error) {
    console.error("Error updating login time:", error);
    return false;
  }
}

/**
 * 비밀번호 변경 시 password_changed_at 업데이트 (SQL 함수 사용)
 */
export async function updatePasswordChangedTime(userId: string) {
  try {
    const { error } = await supabase.rpc("update_password_changed", {
      input_user_id: userId,
    });

    if (error) {
      console.error("Failed to update password changed time:", error);
      return false;
    }

    console.log("Password changed time updated successfully");
    return true;
  } catch (error) {
    console.error("Error updating password changed time:", error);
    return false;
  }
}

/**
 * 로그인 활동 기록 생성 (확장 가능)
 */
export async function createLoginRecord(
  userId: string,
  deviceInfo?: string,
  location?: string
) {
  try {
    // 향후 별도 login_history 테이블을 만들 경우 사용
    const loginRecord = {
      user_id: userId,
      login_time: new Date().toISOString(),
      device_info: deviceInfo || "Unknown Device",
      location: location || "Unknown Location",
      ip_address: "", // 클라이언트에서는 IP 주소를 직접 가져올 수 없음
    };

    console.log("Login record created:", loginRecord);

    // 현재는 profiles 테이블의 last_login_at만 업데이트
    return await updateLoginTime(userId);
  } catch (error) {
    console.error("Error creating login record:", error);
    return false;
  }
}

/**
 * 브라우저 정보 감지
 */
export function getDeviceInfo(): string {
  if (typeof window === "undefined") return "Server";

  const userAgent = window.navigator.userAgent;
  let browser = "Unknown Browser";
  let os = "Unknown OS";

  // 브라우저 감지
  if (userAgent.includes("Chrome")) browser = "Chrome";
  else if (userAgent.includes("Firefox")) browser = "Firefox";
  else if (userAgent.includes("Safari")) browser = "Safari";
  else if (userAgent.includes("Edge")) browser = "Edge";

  // OS 감지
  if (userAgent.includes("Windows")) os = "Windows";
  else if (userAgent.includes("Mac")) os = "macOS";
  else if (userAgent.includes("Linux")) os = "Linux";
  else if (userAgent.includes("Android")) os = "Android";
  else if (userAgent.includes("iOS")) os = "iOS";

  return `${browser} on ${os}`;
}

/**
 * 테스트용 로그인 시뮬레이션
 */
export async function simulateLogin(userId: string) {
  const deviceInfo = getDeviceInfo();
  const location = "서울, 대한민국"; // 실제로는 IP 기반 위치 서비스 사용

  console.log("Simulating login for user:", userId);
  console.log("Device:", deviceInfo);
  console.log("Location:", location);

  return await createLoginRecord(userId, deviceInfo, location);
}
