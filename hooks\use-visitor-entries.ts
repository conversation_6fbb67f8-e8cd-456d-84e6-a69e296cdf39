"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/components/providers/auth-provider";

export interface VisitorEntry {
  id: string;
  farm_id: string;
  visit_datetime: string;
  visitor_name: string;
  visitor_phone: string;
  visitor_address: string;
  vehicle_number: string | null;
  visitor_purpose: string;
  disinfection_check: boolean;
  notes: string | null;
  consent_given: boolean;
  session_token: string;
  registered_by?: string;
  created_at: string;
}

// 확장된 데모 방문자 데이터
const generateDemoEntries = (): VisitorEntry[] => {
  const names = [
    "김철수",
    "이영희",
    "박민수",
    "정수진",
    "최영호",
    "한미경",
    "송태현",
    "윤서연",
    "임동혁",
    "강지은",
    "조현우",
    "신예린",
    "오성민",
    "장혜진",
    "배준호",
    "서미영",
    "노태우",
    "문지혜",
    "권상현",
    "안소영",
  ];
  const purposes = [
    "배달",
    "점검",
    "미팅",
    "수의사 방문",
    "장비 점검",
    "연구 조사",
    "교육",
    "계약 협의",
    "예방접종",
    "사료 보충",
  ];
  const addresses = [
    "서울시 강남구 테헤란로 123",
    "경기도 수원시 영통구 광교로 456",
    "인천시 남동구 구월로 789",
    "경기도 성남시 분당구 판교로 321",
    "서울시 서초구 강남대로 654",
    "경기도 용인시 기흥구 용구대로 987",
    "서울시 마포구 월드컵로 147",
    "경기도 고양시 일산동구 중앙로 258",
    "서울시 송파구 올림픽로 369",
    "경기도 안양시 동안구 평촌대로 741",
  ];

  const farmIds = ["demo-farm-1", "demo-farm-2", "demo-farm-3", "demo-farm-4"];
  const entries: VisitorEntry[] = [];

  // 지난 30일간의 데이터 생성
  for (let i = 0; i < 50; i++) {
    const daysAgo = Math.floor(Math.random() * 30);
    const hoursAgo = Math.floor(Math.random() * 24);
    const minutesAgo = Math.floor(Math.random() * 60);

    const entryDate = new Date();
    entryDate.setDate(entryDate.getDate() - daysAgo);
    entryDate.setHours(entryDate.getHours() - hoursAgo);
    entryDate.setMinutes(entryDate.getMinutes() - minutesAgo);

    entries.push({
      id: `entry-${i + 1}`,
      farm_id: farmIds[Math.floor(Math.random() * farmIds.length)],
      visit_datetime: entryDate.toISOString(),
      visitor_name: names[Math.floor(Math.random() * names.length)],
      visitor_phone: `010-${Math.floor(Math.random() * 9000) + 1000}-${
        Math.floor(Math.random() * 9000) + 1000
      }`,
      visitor_address: addresses[Math.floor(Math.random() * addresses.length)],
      vehicle_number:
        Math.random() > 0.3
          ? `${Math.floor(Math.random() * 99) + 1}${
              ["가", "나", "다", "라", "마"][Math.floor(Math.random() * 5)]
            } ${Math.floor(Math.random() * 9000) + 1000}`
          : null,
      visitor_purpose: purposes[Math.floor(Math.random() * purposes.length)],
      disinfection_check: Math.random() > 0.1, // 90% 확률로 소독 완료
      notes:
        Math.random() > 0.7
          ? `${purposes[Math.floor(Math.random() * purposes.length)]} 관련 업무`
          : null,
      consent_given: true,
      session_token: `session-${i + 1}`,
      created_at: entryDate.toISOString(),
    });
  }

  return entries.sort(
    (a, b) =>
      new Date(b.visit_datetime).getTime() -
      new Date(a.visit_datetime).getTime()
  );
};

export function useVisitorEntries() {
  const { user } = useAuth();
  const [entries, setEntries] = useState<VisitorEntry[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadEntries = () => {
      try {
        const storedEntries = localStorage.getItem("visitor_entries");
        let allEntries = generateDemoEntries();

        if (storedEntries) {
          const parsedEntries = JSON.parse(storedEntries);
          // 기존 데이터가 있으면 새로운 데모 데이터와 합치기
          const existingIds = parsedEntries.map((e: VisitorEntry) => e.id);
          const newDemoEntries = allEntries.filter(
            (e) => !existingIds.includes(e.id)
          );
          allEntries = [...parsedEntries, ...newDemoEntries];
        }

        // 사용자 역할에 따라 필터링
        let filteredEntries = allEntries;
        if (user) {
          if (user.account_type === "admin") {
            // 시스템 관리자는 모든 방문자 기록 볼 수 있음
            filteredEntries = allEntries;
          } else {
            // 일반 사용자는 자신의 농장 방문자만 (농장별 권한은 별도 확인 필요)
            const userFarmIds = user.farms || [];
            filteredEntries = allEntries.filter((entry) => {
              // 농장 정보를 가져와서 owner_id 확인
              const farms = JSON.parse(localStorage.getItem("farms") || "[]");
              const farm = farms.find((f: any) => f.id === entry.farm_id);
              return (
                farm &&
                (farm.owner_id === user.id ||
                  userFarmIds.includes(entry.farm_id))
              );
            });
          }
        }

        setEntries(filteredEntries);
        localStorage.setItem("visitor_entries", JSON.stringify(allEntries));
      } catch (error) {
        console.error("방문자 데이터 로드 실패:", error);
        setEntries(generateDemoEntries());
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      loadEntries();
    } else {
      setLoading(false);
    }
  }, [user]);

  const addEntry = (
    entryData: Omit<VisitorEntry, "id" | "created_at" | "session_token">
  ) => {
    const newEntry: VisitorEntry = {
      ...entryData,
      id: `entry-${Date.now()}`,
      session_token: `session-${Date.now()}`,
      created_at: new Date().toISOString(),
    };

    const storedEntries = localStorage.getItem("visitor_entries");
    const allEntries = storedEntries
      ? JSON.parse(storedEntries)
      : generateDemoEntries();
    const updatedEntries = [newEntry, ...allEntries];

    setEntries((prev) => [newEntry, ...prev]);
    localStorage.setItem("visitor_entries", JSON.stringify(updatedEntries));
    return newEntry;
  };

  const getEntriesByFarm = (farmId: string) => {
    return entries.filter((entry) => entry.farm_id === farmId);
  };

  const getTodayEntries = () => {
    const today = new Date().toISOString().split("T")[0];
    return entries.filter((entry) => entry.visit_datetime.startsWith(today));
  };

  const getThisWeekEntries = () => {
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    return entries.filter((entry) => new Date(entry.visit_datetime) >= weekAgo);
  };

  // 전체 방문자 데이터 가져오기 (관리자용)
  const getAllEntries = () => {
    const storedEntries = localStorage.getItem("visitor_entries");
    return storedEntries ? JSON.parse(storedEntries) : generateDemoEntries();
  };

  return {
    entries,
    loading,
    addEntry,
    getEntriesByFarm,
    getTodayEntries,
    getThisWeekEntries,
    getAllEntries,
  };
}
