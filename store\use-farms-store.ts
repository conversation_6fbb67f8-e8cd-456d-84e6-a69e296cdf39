import { create } from "zustand";
import { supabase } from "@/lib/supabase";
import { createSystemLog } from "@/lib/utils/system-log";

export interface Farm {
  id: string;
  owner_id: string;
  farm_name: string;
  farm_address: string;
  farm_detailed_address: string | null;
  description?: string;
  farm_type?: string;
  manager_name?: string;
  manager_phone?: string;
  created_at: string;
  updated_at: string;
}

interface FarmsState {
  farms: Farm[];
  loading: boolean;
  initialized: boolean;
  currentRequest: string | null;
  fetchFarms: (userId: string) => Promise<void>;
  addFarm: (
    userId: string,
    farmData: Omit<Farm, "id" | "owner_id" | "created_at" | "updated_at">
  ) => Promise<Farm | null>;
  updateFarm: (farmId: string, farmData: Partial<Farm>) => Promise<void>;
  deleteFarm: (farmId: string) => Promise<void>;
  reset: () => void;
}

type State = {
  farms: Farm[];
  loading: boolean;
  initialized: boolean;
  currentRequest: string | null;
};

type Actions = {
  fetchFarms: (userId: string) => Promise<void>;
  addFarm: (
    userId: string,
    farmData: Omit<Farm, "id" | "owner_id" | "created_at" | "updated_at">
  ) => Promise<Farm | null>;
  updateFarm: (farmId: string, farmData: Partial<Farm>) => Promise<void>;
  deleteFarm: (farmId: string) => Promise<void>;
};

export const useFarmsStore = create<FarmsState>((set, get) => ({
  farms: [],
  loading: false,
  initialized: false,
  currentRequest: null,

  fetchFarms: async (userId: string) => {
    const state = get();

    // 이미 같은 요청이 진행 중이면 무시
    if (state.currentRequest === userId) {
      console.log(
        "[FarmsStore] Duplicate request in progress, skipping fetch for:",
        userId
      );
      return;
    }

    // 이미 초기화되었고 farms가 있으면 무시
    if (state.initialized && state.farms.length > 0) {
      console.log("[FarmsStore] Already initialized with data, skipping fetch");
      return;
    }

    set({ currentRequest: userId, loading: true });
    console.log("[FarmsStore] Starting fetch for user:", userId);

    try {
      // RLS 정책 문제를 우회하기 위해 특정 필드만 선택
      const { data: farms, error } = await supabase
        .from("farms")
        .select(
          `
          id,
          farm_name,
          farm_address,
          farm_detailed_address,
          description,
          farm_type,
          manager_name,
          manager_phone,
          owner_id,
          created_at,
          updated_at
        `
        )
        .eq("owner_id", userId);

      if (error) {
        console.error("[FarmsStore] Direct query failed:", error);

        // 만약 RLS 오류라면 더 간단한 쿼리 시도
        if (error.code === "42P17") {
          console.log(
            "[FarmsStore] RLS recursion detected, trying minimal query"
          );

          const { data: minimalFarms, error: minimalError } = await supabase
            .from("farms")
            .select("id, farm_name, owner_id")
            .eq("owner_id", userId);

          if (minimalError) throw minimalError;

          // 최소한의 데이터로 Farm 객체 생성
          const farmsWithDefaults =
            minimalFarms?.map((farm: any) => ({
              id: farm.id,
              farm_name: farm.farm_name,
              owner_id: farm.owner_id,
              farm_address: "",
              farm_detailed_address: null,
              description: "",
              farm_type: "",
              manager_name: "",
              manager_phone: "",
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            })) || [];

          console.log(
            "[FarmsStore] Minimal query successful, farms count:",
            farmsWithDefaults?.length
          );
          set({ farms: farmsWithDefaults as Farm[], initialized: true });
        } else {
          throw error;
        }
      } else {
        console.log(
          "[FarmsStore] Full query successful, farms count:",
          farms?.length
        );
        set({ farms: farms as Farm[], initialized: true });
      }
    } catch (error) {
      console.error("[FarmsStore] Error fetching farms:", error);
      set({ farms: [], initialized: false });
    } finally {
      set({ loading: false, currentRequest: null });
    }
  },

  reset: () => {
    set({
      farms: [],
      loading: false,
      initialized: false,
      currentRequest: null,
    });
  },

  addFarm: async (
    userId: string,
    farmData: Omit<Farm, "id" | "owner_id" | "created_at" | "updated_at">
  ) => {
    try {
      const { data: farm, error: farmError } = await supabase
        .from("farms")
        .insert({
          ...farmData,
          owner_id: userId,
        })
        .select()
        .single();

      if (farmError) {
        throw farmError;
      }

      // 농장 소유자를 구성원으로 자동 추가
      const { error: memberError } = await supabase
        .from("farm_members")
        .insert({
          farm_id: farm.id,
          user_id: userId,
          role: "owner",
          is_active: true,
          position: "농장 소유자",
          responsibilities: "농장 전체 관리 및 운영",
        });

      if (memberError) {
        console.warn(
          "[FarmsStore] Failed to add owner as member:",
          memberError
        );
        // 농장은 생성되었지만 구성원 추가 실패 - 농장은 유지
      } else {
        console.log("[FarmsStore] Owner automatically added as member");
      }

      // 농장 등록 성공 로그 생성
      await createSystemLog(
        "FARM_CREATED",
        `농장 "${farmData.farm_name}"이 등록되었습니다 (유형: ${
          farmData.farm_type || "미지정"
        }, 관리자: ${farmData.manager_name || "미지정"})`,
        "info",
        userId,
        "farm",
        farm.id,
        {
          farm_name: farmData.farm_name,
          farm_type: farmData.farm_type,
          farm_address: farmData.farm_address,
          manager_name: farmData.manager_name,
          manager_phone: farmData.manager_phone,
          registration_method: "web_form",
          owner_id: userId,
        }
      );

      set((state: State) => ({ farms: [...state.farms, farm as Farm] }));
      return farm as Farm;
    } catch (error) {
      console.error("Error creating farm:", error);

      // 농장 등록 실패 로그 생성
      await createSystemLog(
        "FARM_CREATION_FAILED",
        `농장 "${farmData.farm_name}" 등록 실패: ${
          error instanceof Error ? error.message : "알 수 없는 오류"
        }`,
        "error",
        userId,
        "farm",
        undefined,
        {
          farm_name: farmData.farm_name,
          farm_type: farmData.farm_type,
          error_message:
            error instanceof Error ? error.message : "알 수 없는 오류",
          registration_method: "web_form",
        }
      );

      return null;
    }
  },

  updateFarm: async (farmId: string, farmData: Partial<Farm>) => {
    try {
      const { data: farm, error } = await supabase
        .from("farms")
        .update(farmData)
        .eq("id", farmId)
        .select()
        .single();

      if (error) {
        throw error;
      }

      // 농장 수정 성공 로그 생성
      await createSystemLog(
        "FARM_UPDATED",
        `농장 "${farmData.farm_name || farm.farm_name}" 정보가 수정되었습니다`,
        "info",
        farm.owner_id,
        "farm",
        farmId,
        {
          farm_name: farmData.farm_name || farm.farm_name,
          farm_type: farmData.farm_type,
          farm_address: farmData.farm_address,
          manager_name: farmData.manager_name,
          manager_phone: farmData.manager_phone,
          owner_id: farm.owner_id,
        }
      );

      set((state: State) => ({
        farms: state.farms.map((f: Farm) =>
          f.id === farmId ? ({ ...f, ...farm } as Farm) : f
        ),
      }));
    } catch (error) {
      console.error("Error updating farm:", error);

      // 농장 수정 실패 로그 생성
      await createSystemLog(
        "FARM_UPDATE_FAILED",
        `농장 수정 실패 (ID: ${farmId}): ${
          error instanceof Error ? error.message : "알 수 없는 오류"
        }`,
        "error",
        undefined, // 실패 시 사용자 ID를 알 수 없을 수 있음
        "farm",
        farmId,
        {
          farm_id: farmId,
          error_message:
            error instanceof Error ? error.message : "알 수 없는 오류",
          attempted_data: farmData,
        }
      );
    }
  },

  deleteFarm: async (farmId: string) => {
    try {
      // 삭제 전에 농장 정보 가져오기 (로그용)
      const { data: farmToDelete } = await supabase
        .from("farms")
        .select("farm_name, owner_id")
        .eq("id", farmId)
        .single();

      const { error } = await supabase.from("farms").delete().eq("id", farmId);

      if (error) {
        throw error;
      }

      // 농장 삭제 성공 로그 생성
      await createSystemLog(
        "FARM_DELETED",
        `농장 "${farmToDelete?.farm_name || "알 수 없음"}"이 삭제되었습니다`,
        "info",
        farmToDelete?.owner_id,
        "farm",
        farmId,
        {
          farm_name: farmToDelete?.farm_name,
          owner_id: farmToDelete?.owner_id,
          farm_id: farmId,
        }
      );

      set((state: State) => ({
        farms: state.farms.filter((f: Farm) => f.id !== farmId),
      }));
    } catch (error) {
      console.error("Error deleting farm:", error);

      // 농장 삭제 실패 로그 생성
      await createSystemLog(
        "FARM_DELETE_FAILED",
        `농장 삭제 실패 (ID: ${farmId}): ${
          error instanceof Error ? error.message : "알 수 없는 오류"
        }`,
        "error",
        undefined, // 실패 시 사용자 ID를 알 수 없을 수 있음
        "farm",
        farmId,
        {
          farm_id: farmId,
          error_message:
            error instanceof Error ? error.message : "알 수 없는 오류",
        }
      );
    }
  },
}));
