"use client";

import { createContext, useContext, useEffect } from "react";
import { useAuth } from "@/components/providers/auth-provider";
import { useFarmsStore } from "@/store/use-farms-store";

const FarmsContext = createContext<{ initialized: boolean } | undefined>(
  undefined
);

export function FarmsProvider({ children }: { children: React.ReactNode }) {
  const { profile } = useAuth();
  const { fetchFarms, initialized } = useFarmsStore();

  useEffect(() => {
    if (profile?.id) {
      console.log("[FarmsProvider] Initializing farms data");
      fetchFarms(profile.id);
    }
  }, [profile?.id]);

  return (
    <FarmsContext.Provider value={{ initialized }}>
      {children}
    </FarmsContext.Provider>
  );
}

export const useFarmsContext = () => {
  const context = useContext(FarmsContext);
  if (context === undefined) {
    throw new Error("useFarmsContext must be used within a FarmsProvider");
  }
  return context;
};
