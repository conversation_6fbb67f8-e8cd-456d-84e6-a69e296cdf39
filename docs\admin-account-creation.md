# 시스템 관리자 계정 생성 절차

## ⚠️ **중요: auth.users와 profiles의 관계**

### **🔗 테이블 구조:**

```
auth.users (Supabase 인증)
    ↓ 1:1 관계
public.profiles (사용자 프로필)
```

### **❌ 잘못된 방법:**

```sql
-- 이 방법은 로그인할 수 없는 계정을 만듭니다!
INSERT INTO public.profiles (id, email, ...)
VALUES (gen_random_uuid(), '<EMAIL>', ...);
-- ❌ auth.users에 없는 UUID이므로 로그인 불가
```

### **✅ 올바른 방법:**

1. **먼저 auth.users에 사용자 생성** (Supabase 대시보드 또는 API)
2. **그 다음 profiles에서 account_type을 'admin'으로 변경**

---

## 🎯 **관리자 계정 생성 방법들**

### **📋 Method 1: Supabase 대시보드 사용 (권장)**

#### **단계별 절차:**

1. **Supabase 대시보드 → Authentication → Users**
2. **"Add user" 버튼 클릭**
3. **이메일과 비밀번호 입력하여 사용자 생성**
4. **생성된 사용자의 UUID 복사**
5. **SQL 에디터에서 관리자 권한 부여:**

```sql
-- 새로 생성된 사용자를 관리자로 승격
UPDATE public.profiles
SET
    account_type = 'admin',
    name = '관리자 이름',
    phone = '010-1234-5678',
    company_name = '회사명',
    updated_at = NOW()
WHERE id = '새로-생성된-사용자-UUID';
```

#### **장점:**

- ✅ **실제 로그인 가능**: auth.users에 정상 등록
- ✅ **비밀번호 설정**: 안전한 비밀번호 설정 가능
- ✅ **이메일 인증**: 필요시 이메일 인증 처리
- ✅ **즉시 사용**: 생성 후 바로 로그인 가능

---

### **📋 Method 2: 기존 사용자를 관리자로 승격 (권장)**

#### **이미 가입한 사용자를 관리자로 변경:**

```sql
-- 기존 사용자를 관리자로 승격
UPDATE public.profiles
SET
    account_type = 'admin',
    updated_at = NOW()
WHERE email = '<EMAIL>';

-- 승격 확인
SELECT
    id,
    email,
    name,
    account_type,
    updated_at
FROM public.profiles
WHERE email = '<EMAIL>';
```

#### **장점:**

- ✅ **안전함**: 이미 auth.users에 등록된 사용자
- ✅ **즉시 적용**: 다음 로그인부터 관리자 권한 적용
- ✅ **기존 데이터 보존**: 사용자의 기존 정보 유지

---

### **📋 Method 3: 스크립트 내 주석 해제 (개발용)**

#### **database-reset-and-rebuild.sql에서:**

```sql
-- 개발/테스트용 관리자 계정 (필요시 주석 해제)
/*
INSERT INTO public.profiles (
    id,
    email,
    name,
    account_type,
    phone,
    company_name
) VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    '시스템 관리자',
    'admin',
    '010-0000-0000',
    '농장 방문자 관리 시스템'
) ON CONFLICT (email) DO NOTHING;
*/
```

**주석을 제거하면 스크립트 실행 시 자동으로 관리자 계정이 생성됩니다.**

---

## 🚀 **단계별 실행 절차**

### **Step 1: 데이터베이스 스크립트 실행**

```bash
# 새로운 데이터베이스 구조 생성
psql -d your_database -f scripts/database-reset-and-rebuild.sql
```

### **Step 2: 관리자 계정 생성**

```sql
-- 방법 1: 함수 사용 (권장)
SELECT public.create_admin_account(
    '<EMAIL>',
    '관리자 이름',
    '010-1234-5678',
    '회사명'
);

-- 또는 방법 2: 직접 INSERT
INSERT INTO public.profiles (
    id, email, name, account_type
) VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    '관리자 이름',
    'admin'
);
```

### **Step 3: 관리자 계정 확인**

```sql
-- 생성된 관리자 계정 확인
SELECT
    id,
    email,
    name,
    account_type,
    created_at
FROM public.profiles
WHERE account_type = 'admin';

-- 관리자 권한 확인
SELECT public.is_system_admin('관리자-UUID');
```

---

## 🔐 **관리자 계정 특징**

### **✅ 시스템 권한:**

- **모든 농장 접근**: 소유자가 아니어도 모든 농장 관리 가능
- **모든 사용자 관리**: 사용자 목록 조회, 권한 변경
- **시스템 로그 접근**: 모든 시스템 활동 로그 조회
- **통계 데이터 접근**: 전체 시스템 통계 및 분석

### **✅ 데이터베이스 레벨 권한:**

```sql
-- 관리자 권한 확인 함수들
SELECT public.is_system_admin('admin-uuid');           -- true
SELECT public.can_manage_farm('admin-uuid', 'any-farm'); -- true
SELECT public.can_view_farm('admin-uuid', 'any-farm');   -- true
```

---

## 🧪 **테스트 방법**

### **1. 관리자 계정 생성 테스트:**

```sql
-- 테스트 관리자 생성
SELECT public.create_admin_account(
    '<EMAIL>',
    '테스트 관리자',
    '010-0000-0000',
    '테스트 회사'
);
```

### **2. 권한 테스트:**

```sql
-- 생성된 관리자 ID 확인
SELECT id FROM public.profiles WHERE email = '<EMAIL>';

-- 권한 확인 (admin-uuid를 실제 UUID로 교체)
SELECT public.is_system_admin('admin-uuid');
SELECT * FROM public.get_user_status('admin-uuid');
```

### **3. 프론트엔드 로그인 테스트:**

```typescript
// 관리자 계정으로 로그인 후 확인
if (user.account_type === "admin") {
  console.log("시스템 관리자로 로그인됨");
  // 모든 농장 접근 가능
  // 사용자 관리 메뉴 접근 가능
}
```

---

## ⚠️ **보안 주의사항**

### **🔒 운영 환경에서:**

1. **강력한 비밀번호**: 복잡한 비밀번호 설정 필수
2. **이메일 보안**: 실제 관리자 이메일 사용
3. **접근 제한**: 관리자 계정 수 최소화
4. **로그 모니터링**: 관리자 활동 정기적 검토

### **🔒 개발 환경에서:**

1. **테스트 이메일**: 실제 이메일 사용 금지
2. **임시 계정**: 개발 완료 후 삭제
3. **권한 테스트**: 모든 관리자 기능 테스트

---

## 📊 **관리자 계정 관리**

### **기존 사용자를 관리자로 승격:**

```sql
-- 일반 사용자를 관리자로 변경
UPDATE public.profiles
SET account_type = 'admin', updated_at = NOW()
WHERE email = '<EMAIL>';
```

### **관리자 권한 해제:**

```sql
-- 관리자를 일반 사용자로 변경
UPDATE public.profiles
SET account_type = 'user', updated_at = NOW()
WHERE email = '<EMAIL>';
```

### **관리자 목록 조회:**

```sql
-- 모든 관리자 계정 조회
SELECT
    id,
    email,
    name,
    phone,
    company_name,
    created_at,
    last_login_at
FROM public.profiles
WHERE account_type = 'admin'
ORDER BY created_at;
```

**이제 안전하고 체계적으로 시스템 관리자 계정을 생성할 수 있습니다!** ✨🔐
