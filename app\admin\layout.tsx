"use client";

import type React from "react";
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import { AdminSidebar } from "@/components/admin/admin-sidebar";
import { MobileHeader } from "@/components/admin/mobile-header";
import { MobileMenuButton } from "@/components/mobile-menu-button";
import { FarmsProvider } from "@/components/providers/farms-provider";
import { ProtectedRoute } from "@/components/providers/protected-route";

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ProtectedRoute>
      <SidebarProvider>
        <FarmsProvider>
          <AdminSidebar />
          <SidebarInset>
            <MobileHeader />
            <main className="flex-1 p-4 md:p-6">{children}</main>
          </SidebarInset>
          <MobileMenuButton />
        </FarmsProvider>
      </SidebarProvider>
    </ProtectedRoute>
  );
}
