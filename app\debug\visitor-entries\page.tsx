"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { supabase } from "@/lib/supabase";

interface VisitorEntry {
  id: string;
  farm_id: string;
  visit_datetime: string;
  visitor_name: string;
  visitor_phone: string;
  visitor_address: string;
  vehicle_number: string | null;
  visitor_purpose: string;
  disinfection_check: boolean;
  notes: string | null;
  consent_given: boolean;
  session_token: string;
  registered_by?: string;
  created_at: string;
}

interface Farm {
  id: string;
  farm_name: string;
  owner_id: string;
}

export default function DebugVisitorEntriesPage() {
  const [visitorEntries, setVisitorEntries] = useState<VisitorEntry[]>([]);
  const [farms, setFarms] = useState<Farm[]>([]);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDebugData = async () => {
      try {
        // 현재 사용자 정보
        const {
          data: { user },
        } = await supabase.auth.getUser();
        console.log("현재 사용자:", user);
        setCurrentUser(user);

        // 모든 농장 조회 (RLS 무시하고 직접 조회)
        const { data: farmsData, error: farmsError } = await supabase
          .from("farms")
          .select("id, farm_name, owner_id");

        console.log("농장 데이터:", { farmsData, farmsError });
        setFarms(farmsData || []);

        // 모든 방문자 기록 조회 (RLS 무시하고 직접 조회)
        const { data: entriesData, error: entriesError } = await supabase
          .from("visitor_entries")
          .select("*")
          .order("created_at", { ascending: false });

        console.log("방문자 기록 데이터:", { entriesData, entriesError });
        setVisitorEntries(entriesData || []);
      } catch (error) {
        console.error("디버깅 데이터 조회 오류:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchDebugData();
  }, []);

  const testRLSPolicy = async () => {
    try {
      console.log("RLS 정책 테스트 시작");

      // RLS 정책이 적용된 상태에서 조회
      const { data, error } = await supabase.from("visitor_entries").select(`
          *,
          farms!inner(farm_name, owner_id)
        `);

      console.log("RLS 정책 테스트 결과:", { data, error });
    } catch (error) {
      console.error("RLS 정책 테스트 오류:", error);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-4">
        <div className="text-center">로딩 중...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>방문자 기록 디버깅</CardTitle>
          <CardDescription>
            데이터베이스에 저장된 방문자 기록과 권한을 확인합니다.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={testRLSPolicy}>RLS 정책 테스트</Button>

          <div>
            <h3 className="font-semibold mb-2">현재 사용자</h3>
            <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
              {JSON.stringify(currentUser, null, 2)}
            </pre>
          </div>

          <div>
            <h3 className="font-semibold mb-2">농장 목록 ({farms.length}개)</h3>
            <div className="space-y-2">
              {farms.map((farm) => (
                <div key={farm.id} className="bg-gray-50 p-2 rounded">
                  <div>
                    <strong>농장명:</strong> {farm.farm_name}
                  </div>
                  <div>
                    <strong>농장 ID:</strong> {farm.id}
                  </div>
                  <div>
                    <strong>소유자 ID:</strong> {farm.owner_id}
                  </div>
                  <div>
                    <strong>내가 소유자:</strong>{" "}
                    {farm.owner_id === currentUser?.id ? "예" : "아니오"}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">
              방문자 기록 ({visitorEntries.length}개)
            </h3>
            <div className="space-y-2 max-h-96 overflow-auto">
              {visitorEntries.map((entry) => {
                const farm = farms.find((f) => f.id === entry.farm_id);
                return (
                  <div key={entry.id} className="bg-blue-50 p-3 rounded border">
                    <div>
                      <strong>방문자:</strong> {entry.full_name}
                    </div>
                    <div>
                      <strong>농장:</strong> {farm?.farm_name || "알 수 없음"} (
                      {entry.farm_id})
                    </div>
                    <div>
                      <strong>연락처:</strong> {entry.phone_number}
                    </div>
                    <div>
                      <strong>방문목적:</strong> {entry.visit_purpose}
                    </div>
                    <div>
                      <strong>등록시간:</strong>{" "}
                      {new Date(entry.entry_datetime).toLocaleString("ko-KR")}
                    </div>
                    <div>
                      <strong>생성시간:</strong>{" "}
                      {new Date(entry.created_at).toLocaleString("ko-KR")}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
