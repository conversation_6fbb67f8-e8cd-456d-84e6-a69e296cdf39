import { useCallback } from "react";
import { useAuth } from "@/components/providers/auth-provider";
import { createSystemLog } from "@/lib/utils/system-log";

interface SystemLogOptions {
  userId?: string;
  resourceType?: string;
  resourceId?: string;
  metadata?: Record<string, any>;
}

export function useSystemLog() {
  const { profile } = useAuth();

  const logAction = useCallback(
    async (
      action: string,
      details: string,
      level: "info" | "warning" | "error" = "info",
      options?: SystemLogOptions
    ) => {
      try {
        await createSystemLog(
          action,
          details,
          level,
          profile?.id,
          options?.resourceType,
          options?.resourceId,
          {
            admin_name: profile?.name,
            admin_email: profile?.email,
            ...options?.metadata,
          }
        );
      } catch (error) {
        console.error("Failed to create system log:", error);
      }
    },
    [profile]
  );

  const logUserAction = useCallback(
    async (
      action: string,
      userName: string,
      userId?: string,
      details?: string
    ) => {
      await logAction(
        action,
        details || `사용자 "${userName}"에 대한 작업: ${action}`,
        "info",
        {
          userId,
          resourceType: "user",
          metadata: { user_name: userName },
        }
      );
    },
    [logAction]
  );

  const logFarmAction = useCallback(
    async (
      action: string,
      farmName: string,
      farmId?: string,
      details?: string
    ) => {
      await logAction(
        action,
        details || `농장 "${farmName}"에 대한 작업: ${action}`,
        "info",
        {
          resourceId: farmId,
          resourceType: "farm",
          metadata: { farm_name: farmName },
        }
      );
    },
    [logAction]
  );

  const logError = useCallback(
    async (action: string, error: any, context?: string) => {
      const errorMessage = error?.message || String(error);
      const details = context
        ? `${context} 중 오류 발생: ${errorMessage}`
        : `오류 발생: ${errorMessage}`;

      await logAction(action, details, "error", {
        metadata: { error_message: errorMessage },
      });
    },
    [logAction]
  );

  return {
    logAction,
    logUserAction,
    logFarmAction,
    logError,
  };
}
