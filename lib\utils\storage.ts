import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";

// 허용되는 이미지 MIME 타입 정의
export const ALLOWED_IMAGE_TYPES = {
  // 기본 웹 이미지 형식
  JPEG: "image/jpeg",
  JPG: "image/jpg",
  PNG: "image/png",
  GIF: "image/gif",
  WEBP: "image/webp",

  // 벡터 이미지
  SVG: "image/svg+xml",

  // 고해상도 이미지
  TIFF: "image/tiff",

  // 디자인 작업용 이미지
  PSD: "image/vnd.adobe.photoshop",

  // 아이콘
  ICO: "image/x-icon",

  // 새로운 이미지 형식
  AVIF: "image/avif",
  HEIC: "image/heic",
  HEIF: "image/heif",
} as const;

// MIME 타입 문자열 유니온 타입
export type AllowedImageType =
  (typeof ALLOWED_IMAGE_TYPES)[keyof typeof ALLOWED_IMAGE_TYPES];

export type UploadImageOptions = {
  file: File;
  bucket: string;
  path: string;
  maxSizeMB?: number;
  allowedFileTypes?: AllowedImageType[];
};

// 기본적으로 허용할 이미지 타입 (웹 최적화 형식)
const DEFAULT_ALLOWED_TYPES: AllowedImageType[] = [
  ALLOWED_IMAGE_TYPES.JPEG,
  ALLOWED_IMAGE_TYPES.JPG,
  ALLOWED_IMAGE_TYPES.PNG,
  ALLOWED_IMAGE_TYPES.WEBP,
  ALLOWED_IMAGE_TYPES.GIF,
  ALLOWED_IMAGE_TYPES.SVG,
  ALLOWED_IMAGE_TYPES.AVIF,
];

export async function uploadImage({
  file,
  bucket,
  path,
  maxSizeMB = 2,
  allowedFileTypes = DEFAULT_ALLOWED_TYPES,
}: UploadImageOptions) {
  try {
    // Validate file type
    if (!allowedFileTypes.includes(file.type as AllowedImageType)) {
      throw new Error(
        `지원하지 않는 파일 형식입니다. 허용된 형식: ${allowedFileTypes
          .map((type) => type.split("/")[1].toUpperCase())
          .join(", ")}`
      );
    }

    // Validate file size
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      throw new Error(`파일 크기는 ${maxSizeMB}MB 이하여야 합니다.`);
    }

    const supabase = createClientComponentClient();

    // Upload file
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file, {
        cacheControl: "3600",
        upsert: true,
      });

    if (error) throw error;

    // Get public URL
    const {
      data: { publicUrl },
    } = supabase.storage.from(bucket).getPublicUrl(path);

    return { data, publicUrl };
  } catch (error) {
    console.error("Error uploading image:", error);
    throw error;
  }
}

export async function deleteImage(bucket: string, path: string) {
  try {
    const supabase = createClientComponentClient();
    const { error } = await supabase.storage.from(bucket).remove([path]);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error("Error deleting image:", error);
    throw error;
  }
}
