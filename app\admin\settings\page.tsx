"use client";

import type React from "react";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Settings,
  Database,
  Shield,
  Globe,
  Save,
  Download,
  Upload,
} from "lucide-react";
import { motion } from "framer-motion";
import {
  Breadcrumb,
  BreadcrumbItem,
  <PERSON>readcrumbLink,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON>,
  <PERSON>readcrumbSeparator,
} from "@/components/ui/breadcrumb";
import Link from "next/link";
import { Home } from "lucide-react";

export default function SettingsPage() {
  const [systemSettings, setSystemSettings] = useState({
    siteName: "농장 방문자 관리 시스템",
    siteDescription: "QR 코드 기반 농장 방문자 관리 플랫폼",
    language: "ko",
    timezone: "Asia/Seoul",
    dateFormat: "YYYY-MM-DD",
    autoBackup: true,
    backupFrequency: "daily",
    maintenanceMode: false,
    debugMode: false,
    maxVisitorsPerDay: 100,
    sessionTimeout: 30,
    enableAnalytics: true,
    enableLogging: true,
  });

  const [securitySettings, setSecuritySettings] = useState({
    requireStrongPassword: true,
    enableTwoFactor: false,
    sessionExpiry: 24,
    maxLoginAttempts: 5,
    enableIpWhitelist: false,
    allowedIps: "",
    enableAuditLog: true,
    dataRetentionDays: 365,
  });

  const handleSystemSettingChange = (key: string, value: any) => {
    setSystemSettings((prev) => ({ ...prev, [key]: value }));
  };

  const handleSecuritySettingChange = (key: string, value: any) => {
    setSecuritySettings((prev) => ({ ...prev, [key]: value }));
  };

  const handleSaveSettings = () => {
    localStorage.setItem("system-settings", JSON.stringify(systemSettings));
    localStorage.setItem("security-settings", JSON.stringify(securitySettings));
    alert("시스템 설정이 저장되었습니다.");
  };

  const handleExportData = () => {
    const data = {
      farms: JSON.parse(localStorage.getItem("farms") || "[]"),
      entries: JSON.parse(localStorage.getItem("visitor-entries") || "[]"),
      settings: { systemSettings, securitySettings },
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `farm-data-backup-${
      new Date().toISOString().split("T")[0]
    }.json`;
    a.click();
    URL.revokeObjectURL(url);

    alert("데이터가 성공적으로 내보내졌습니다.");
  };

  const handleImportData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string);

        if (data.farms)
          localStorage.setItem("farms", JSON.stringify(data.farms));
        if (data.entries)
          localStorage.setItem("visitor-entries", JSON.stringify(data.entries));
        if (data.settings) {
          if (data.settings.systemSettings)
            setSystemSettings(data.settings.systemSettings);
          if (data.settings.securitySettings)
            setSecuritySettings(data.settings.securitySettings);
        }

        alert("데이터가 성공적으로 가져와졌습니다. 페이지를 새로고침하세요.");
      } catch (error) {
        alert("잘못된 파일 형식입니다.");
      }
    };
    reader.readAsText(file);
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-6 pt-2 md:pt-4">
      {/* 브레드크럼 */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/admin/dashboard" className="flex items-center gap-1">
                <Home className="h-4 w-4" />
                대시보드
              </Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>시스템 설정</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <SidebarTrigger />
          <div>
            <h2 className="text-3xl font-bold tracking-tight mb-4 flex items-center gap-2">
              <Settings className="h-8 w-8 text-primary" />
              시스템 설정
            </h2>
            <p className="text-muted-foreground mt-2">
              시스템 전반의 설정을 관리하세요
            </p>
          </div>
        </div>
        <Button onClick={handleSaveSettings}>
          <Save className="mr-2 h-4 w-4" />
          설정 저장
        </Button>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general">일반 설정</TabsTrigger>
          <TabsTrigger value="security">보안 설정</TabsTrigger>
          <TabsTrigger value="data">데이터 관리</TabsTrigger>
          <TabsTrigger value="advanced">고급 설정</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  기본 설정
                </CardTitle>
                <CardDescription>
                  시스템의 기본 정보를 설정합니다
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="siteName">사이트 이름</Label>
                    <Input
                      id="siteName"
                      value={systemSettings.siteName}
                      onChange={(e) =>
                        handleSystemSettingChange("siteName", e.target.value)
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="language">언어</Label>
                    <Select
                      value={systemSettings.language}
                      onValueChange={(value) =>
                        handleSystemSettingChange("language", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ko">한국어</SelectItem>
                        <SelectItem value="en">English</SelectItem>
                        <SelectItem value="ja">日本語</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="siteDescription">사이트 설명</Label>
                  <Textarea
                    id="siteDescription"
                    value={systemSettings.siteDescription}
                    onChange={(e) =>
                      handleSystemSettingChange(
                        "siteDescription",
                        e.target.value
                      )
                    }
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="timezone">시간대</Label>
                    <Select
                      value={systemSettings.timezone}
                      onValueChange={(value) =>
                        handleSystemSettingChange("timezone", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Asia/Seoul">
                          Asia/Seoul (KST)
                        </SelectItem>
                        <SelectItem value="UTC">UTC</SelectItem>
                        <SelectItem value="America/New_York">
                          America/New_York (EST)
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="dateFormat">날짜 형식</Label>
                    <Select
                      value={systemSettings.dateFormat}
                      onValueChange={(value) =>
                        handleSystemSettingChange("dateFormat", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                        <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                        <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  보안 설정
                </CardTitle>
                <CardDescription>
                  시스템 보안 관련 설정을 관리합니다
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">강력한 비밀번호 요구</Label>
                    <p className="text-sm text-muted-foreground">
                      최소 8자, 대소문자, 숫자, 특수문자 포함
                    </p>
                  </div>
                  <Switch
                    checked={securitySettings.requireStrongPassword}
                    onCheckedChange={(checked) =>
                      handleSecuritySettingChange(
                        "requireStrongPassword",
                        checked
                      )
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">2단계 인증</Label>
                    <p className="text-sm text-muted-foreground">
                      추가 보안을 위한 2단계 인증 활성화
                    </p>
                  </div>
                  <Switch
                    checked={securitySettings.enableTwoFactor}
                    onCheckedChange={(checked) =>
                      handleSecuritySettingChange("enableTwoFactor", checked)
                    }
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="sessionExpiry">세션 만료 시간 (시간)</Label>
                    <Input
                      id="sessionExpiry"
                      type="number"
                      value={securitySettings.sessionExpiry}
                      onChange={(e) =>
                        handleSecuritySettingChange(
                          "sessionExpiry",
                          Number.parseInt(e.target.value)
                        )
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="maxLoginAttempts">
                      최대 로그인 시도 횟수
                    </Label>
                    <Input
                      id="maxLoginAttempts"
                      type="number"
                      value={securitySettings.maxLoginAttempts}
                      onChange={(e) =>
                        handleSecuritySettingChange(
                          "maxLoginAttempts",
                          Number.parseInt(e.target.value)
                        )
                      }
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">감사 로그</Label>
                    <p className="text-sm text-muted-foreground">
                      사용자 활동 로그 기록
                    </p>
                  </div>
                  <Switch
                    checked={securitySettings.enableAuditLog}
                    onCheckedChange={(checked) =>
                      handleSecuritySettingChange("enableAuditLog", checked)
                    }
                  />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>

        <TabsContent value="data" className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  데이터 관리
                </CardTitle>
                <CardDescription>
                  데이터 백업, 복원 및 관리 설정
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">자동 백업</Label>
                    <p className="text-sm text-muted-foreground">
                      정기적으로 데이터를 자동 백업합니다
                    </p>
                  </div>
                  <Switch
                    checked={systemSettings.autoBackup}
                    onCheckedChange={(checked) =>
                      handleSystemSettingChange("autoBackup", checked)
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="backupFrequency">백업 주기</Label>
                  <Select
                    value={systemSettings.backupFrequency}
                    onValueChange={(value) =>
                      handleSystemSettingChange("backupFrequency", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hourly">매시간</SelectItem>
                      <SelectItem value="daily">매일</SelectItem>
                      <SelectItem value="weekly">매주</SelectItem>
                      <SelectItem value="monthly">매월</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h4 className="text-sm font-medium">
                    데이터 내보내기/가져오기
                  </h4>
                  <div className="flex gap-4">
                    <Button onClick={handleExportData} variant="outline">
                      <Download className="mr-2 h-4 w-4" />
                      데이터 내보내기
                    </Button>
                    <div>
                      <input
                        type="file"
                        accept=".json"
                        onChange={handleImportData}
                        style={{ display: "none" }}
                        id="import-file"
                      />
                      <Button
                        onClick={() =>
                          document.getElementById("import-file")?.click()
                        }
                        variant="outline"
                      >
                        <Upload className="mr-2 h-4 w-4" />
                        데이터 가져오기
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="dataRetention">데이터 보관 기간 (일)</Label>
                  <Input
                    id="dataRetention"
                    type="number"
                    value={securitySettings.dataRetentionDays}
                    onChange={(e) =>
                      handleSecuritySettingChange(
                        "dataRetentionDays",
                        Number.parseInt(e.target.value)
                      )
                    }
                  />
                  <p className="text-sm text-muted-foreground">
                    설정된 기간이 지난 데이터는 자동으로 삭제됩니다
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  고급 설정
                </CardTitle>
                <CardDescription>
                  개발자 및 고급 사용자를 위한 설정
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">유지보수 모드</Label>
                    <p className="text-sm text-muted-foreground">
                      시스템 점검 시 사용자 접근을 제한합니다
                    </p>
                  </div>
                  <Switch
                    checked={systemSettings.maintenanceMode}
                    onCheckedChange={(checked) =>
                      handleSystemSettingChange("maintenanceMode", checked)
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">디버그 모드</Label>
                    <p className="text-sm text-muted-foreground">
                      개발 및 디버깅을 위한 상세 로그
                    </p>
                  </div>
                  <Switch
                    checked={systemSettings.debugMode}
                    onCheckedChange={(checked) =>
                      handleSystemSettingChange("debugMode", checked)
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">분석 도구</Label>
                    <p className="text-sm text-muted-foreground">
                      사용자 행동 분석 및 통계 수집
                    </p>
                  </div>
                  <Switch
                    checked={systemSettings.enableAnalytics}
                    onCheckedChange={(checked) =>
                      handleSystemSettingChange("enableAnalytics", checked)
                    }
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="maxVisitors">일일 최대 방문자 수</Label>
                    <Input
                      id="maxVisitors"
                      type="number"
                      value={systemSettings.maxVisitorsPerDay}
                      onChange={(e) =>
                        handleSystemSettingChange(
                          "maxVisitorsPerDay",
                          Number.parseInt(e.target.value)
                        )
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="sessionTimeout">세션 타임아웃 (분)</Label>
                    <Input
                      id="sessionTimeout"
                      type="number"
                      value={systemSettings.sessionTimeout}
                      onChange={(e) =>
                        handleSystemSettingChange(
                          "sessionTimeout",
                          Number.parseInt(e.target.value)
                        )
                      }
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
