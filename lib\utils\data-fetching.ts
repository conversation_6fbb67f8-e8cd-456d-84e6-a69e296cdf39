/**
 * 데이터 페칭 유틸리티
 * 
 * 여러 페이지에서 사용되는 공통 데이터 조회 로직을 모아둔 유틸리티입니다.
 */

import { supabase } from "@/lib/supabase";
import { createErrorLog } from "./system-log";

/**
 * 페이징 옵션
 */
export interface PaginationOptions {
  page?: number;
  limit?: number;
  offset?: number;
}

/**
 * 정렬 옵션
 */
export interface SortOptions {
  column: string;
  ascending?: boolean;
}

/**
 * 필터 옵션
 */
export interface FilterOptions {
  [key: string]: any;
}

/**
 * 데이터 조회 결과
 */
export interface DataResult<T> {
  data: T[];
  count?: number;
  error?: string;
}

/**
 * 안전한 데이터 조회 래퍼
 * @param queryFn 조회 함수
 * @param errorContext 에러 컨텍스트
 * @param userId 사용자 ID (로그용)
 * @returns 조회 결과
 */
export const safeDataFetch = async <T>(
  queryFn: () => Promise<{ data: T[] | null; error: any; count?: number }>,
  errorContext: string,
  userId?: string
): Promise<DataResult<T>> => {
  try {
    const result = await queryFn();
    
    if (result.error) {
      console.error(`${errorContext} error:`, result.error);
      
      // 에러 로그 생성
      await createErrorLog(
        `DATA_FETCH_ERROR`,
        result.error,
        errorContext,
        userId
      );
      
      return {
        data: [],
        error: result.error.message || "데이터 조회 중 오류가 발생했습니다.",
      };
    }
    
    return {
      data: result.data || [],
      count: result.count,
    };
  } catch (error) {
    console.error(`${errorContext} exception:`, error);
    
    // 에러 로그 생성
    await createErrorLog(
      `DATA_FETCH_EXCEPTION`,
      error,
      errorContext,
      userId
    );
    
    return {
      data: [],
      error: error instanceof Error ? error.message : "알 수 없는 오류가 발생했습니다.",
    };
  }
};

/**
 * 농장 데이터 조회
 * @param userId 사용자 ID
 * @param options 조회 옵션
 * @returns 농장 데이터
 */
export const fetchFarms = async (
  userId?: string,
  options: {
    includeOwnerInfo?: boolean;
    isActive?: boolean;
    pagination?: PaginationOptions;
    sort?: SortOptions;
  } = {}
): Promise<DataResult<any>> => {
  const {
    includeOwnerInfo = false,
    isActive,
    pagination,
    sort = { column: "created_at", ascending: false },
  } = options;

  return safeDataFetch(async () => {
    let query = supabase.from("farms");

    // 필드 선택
    if (includeOwnerInfo) {
      query = query.select(`
        *,
        profiles!farms_owner_id_fkey(name, email, phone)
      `);
    } else {
      query = query.select("*");
    }

    // 필터 적용
    if (userId) {
      query = query.eq("owner_id", userId);
    }
    if (isActive !== undefined) {
      query = query.eq("is_active", isActive);
    }

    // 정렬 적용
    query = query.order(sort.column, { ascending: sort.ascending });

    // 페이징 적용
    if (pagination) {
      const { page = 1, limit = 50 } = pagination;
      const offset = (page - 1) * limit;
      query = query.range(offset, offset + limit - 1);
    }

    return await query;
  }, "농장 데이터 조회", userId);
};

/**
 * 방문자 데이터 조회
 * @param farmId 농장 ID (선택적)
 * @param options 조회 옵션
 * @returns 방문자 데이터
 */
export const fetchVisitors = async (
  farmId?: string,
  options: {
    includeFarmInfo?: boolean;
    startDate?: string;
    endDate?: string;
    pagination?: PaginationOptions;
    sort?: SortOptions;
  } = {}
): Promise<DataResult<any>> => {
  const {
    includeFarmInfo = false,
    startDate,
    endDate,
    pagination,
    sort = { column: "visit_datetime", ascending: false },
  } = options;

  return safeDataFetch(async () => {
    let query = supabase.from("visitor_entries");

    // 필드 선택
    if (includeFarmInfo) {
      query = query.select(`
        *,
        farms!visitor_entries_farm_id_fkey(farm_name, farm_type)
      `);
    } else {
      query = query.select("*");
    }

    // 필터 적용
    if (farmId) {
      query = query.eq("farm_id", farmId);
    }
    if (startDate) {
      query = query.gte("visit_datetime", startDate);
    }
    if (endDate) {
      query = query.lte("visit_datetime", endDate);
    }

    // 정렬 적용
    query = query.order(sort.column, { ascending: sort.ascending });

    // 페이징 적용
    if (pagination) {
      const { page = 1, limit = 50 } = pagination;
      const offset = (page - 1) * limit;
      query = query.range(offset, offset + limit - 1);
    }

    return await query;
  }, "방문자 데이터 조회");
};

/**
 * 사용자 데이터 조회
 * @param options 조회 옵션
 * @returns 사용자 데이터
 */
export const fetchUsers = async (
  options: {
    isActive?: boolean;
    accountType?: string;
    pagination?: PaginationOptions;
    sort?: SortOptions;
  } = {}
): Promise<DataResult<any>> => {
  const {
    isActive,
    accountType,
    pagination,
    sort = { column: "created_at", ascending: false },
  } = options;

  return safeDataFetch(async () => {
    let query = supabase.from("profiles").select("*");

    // 필터 적용
    if (isActive !== undefined) {
      query = query.eq("is_active", isActive);
    }
    if (accountType) {
      query = query.eq("account_type", accountType);
    }

    // 정렬 적용
    query = query.order(sort.column, { ascending: sort.ascending });

    // 페이징 적용
    if (pagination) {
      const { page = 1, limit = 50 } = pagination;
      const offset = (page - 1) * limit;
      query = query.range(offset, offset + limit - 1);
    }

    return await query;
  }, "사용자 데이터 조회");
};

/**
 * 시스템 로그 조회
 * @param options 조회 옵션
 * @returns 시스템 로그 데이터
 */
export const fetchSystemLogs = async (
  options: {
    userId?: string;
    level?: string;
    action?: string;
    startDate?: string;
    endDate?: string;
    pagination?: PaginationOptions;
    sort?: SortOptions;
  } = {}
): Promise<DataResult<any>> => {
  const {
    userId,
    level,
    action,
    startDate,
    endDate,
    pagination,
    sort = { column: "created_at", ascending: false },
  } = options;

  return safeDataFetch(async () => {
    let query = supabase.from("system_logs").select("*");

    // 필터 적용
    if (userId) {
      query = query.eq("user_id", userId);
    }
    if (level) {
      query = query.eq("level", level);
    }
    if (action) {
      query = query.ilike("action", `%${action}%`);
    }
    if (startDate) {
      query = query.gte("created_at", startDate);
    }
    if (endDate) {
      query = query.lte("created_at", endDate);
    }

    // 정렬 적용
    query = query.order(sort.column, { ascending: sort.ascending });

    // 페이징 적용
    if (pagination) {
      const { page = 1, limit = 200 } = pagination;
      const offset = (page - 1) * limit;
      query = query.range(offset, offset + limit - 1);
    }

    return await query;
  }, "시스템 로그 조회");
};

/**
 * 농장 구성원 조회
 * @param farmId 농장 ID
 * @param options 조회 옵션
 * @returns 농장 구성원 데이터
 */
export const fetchFarmMembers = async (
  farmId: string,
  options: {
    includeUserInfo?: boolean;
    isActive?: boolean;
    role?: string;
    sort?: SortOptions;
  } = {}
): Promise<DataResult<any>> => {
  const {
    includeUserInfo = true,
    isActive,
    role,
    sort = { column: "created_at", ascending: true },
  } = options;

  return safeDataFetch(async () => {
    let query = supabase.from("farm_members");

    // 필드 선택
    if (includeUserInfo) {
      query = query.select(`
        *,
        profiles!farm_members_user_id_fkey(name, email, phone)
      `);
    } else {
      query = query.select("*");
    }

    // 필터 적용
    query = query.eq("farm_id", farmId);
    if (isActive !== undefined) {
      query = query.eq("is_active", isActive);
    }
    if (role) {
      query = query.eq("role", role);
    }

    // 정렬 적용
    query = query.order(sort.column, { ascending: sort.ascending });

    return await query;
  }, "농장 구성원 조회");
};

/**
 * 통계 데이터 조회
 * @param userId 사용자 ID (선택적)
 * @returns 통계 데이터
 */
export const fetchStatistics = async (userId?: string) => {
  try {
    const [farmsResult, visitorsResult, usersResult, logsResult] = await Promise.all([
      fetchFarms(userId),
      fetchVisitors(),
      fetchUsers({ isActive: true }),
      fetchSystemLogs({ pagination: { limit: 100 } }),
    ]);

    const farms = farmsResult.data || [];
    const visitors = visitorsResult.data || [];
    const users = usersResult.data || [];
    const logs = logsResult.data || [];

    // 오늘 날짜
    const today = new Date().toISOString().split("T")[0];

    return {
      totalFarms: farms.length,
      activeFarms: farms.filter((f) => f.is_active).length,
      totalVisitors: visitors.length,
      todayVisitors: visitors.filter((v) => 
        v.visit_datetime?.startsWith(today)
      ).length,
      totalUsers: users.length,
      activeUsers: users.filter((u) => u.last_login_at).length,
      adminUsers: users.filter((u) => u.account_type === "admin").length,
      totalLogs: logs.length,
      errorLogs: logs.filter((l) => l.level === "error").length,
      todayLogs: logs.filter((l) => 
        l.created_at?.startsWith(today)
      ).length,
    };
  } catch (error) {
    console.error("통계 데이터 조회 오류:", error);
    
    await createErrorLog(
      "STATISTICS_FETCH_ERROR",
      error,
      "통계 데이터 조회",
      userId
    );

    return {
      totalFarms: 0,
      activeFarms: 0,
      totalVisitors: 0,
      todayVisitors: 0,
      totalUsers: 0,
      activeUsers: 0,
      adminUsers: 0,
      totalLogs: 0,
      errorLogs: 0,
      todayLogs: 0,
    };
  }
};
