# 농장 방문자 관리 시스템 - 최종 프로젝트 문서

## 📋 **프로젝트 개요**

### **시스템 정보**

- **프로젝트명**: 농장 방문자 관리 시스템 (Farm Visitor Management System)
- **버전**: 0.1.0
- **개발 프레임워크**: Next.js 14.2.16 + TypeScript
- **데이터베이스**: Supabase (PostgreSQL)
- **UI 라이브러리**: Radix UI + Tailwind CSS
- **상태 관리**: Zustand
- **인증**: Supabase Auth

### **시스템 목적**

농장 방문자의 출입 기록을 체계적으로 관리하고, 농장 소유자와 관리자가 효율적으로 방문자 정보를 추적할 수 있는 웹 기반 관리 시스템. 최신 보안 표준을 준수하는 안전한 인증 및 인가 시스템을 제공합니다.

## 🏗️ **시스템 아키텍처**

### **기술 스택**

```
Frontend: Next.js 14 + TypeScript + Tailwind CSS
Backend: Supabase (PostgreSQL + Auth + RLS + Storage)
UI Components: Radix UI + shadcn/ui
State Management: Zustand
Form Handling: React Hook Form + Zod
Charts: Recharts
QR Code: qrcode.react
Address Search: react-daum-postcode
File Upload: Supabase Storage + 공통 유틸리티
```

### **폴더 구조**

```
├── app/                    # Next.js App Router
│   ├── admin/             # 관리자 페이지
│   ├── api/               # API 라우트
│   ├── login/             # 로그인 페이지
│   ├── register/          # 회원가입 페이지
│   ├── visit/             # 방문자 등록 페이지
│   └── layout.tsx         # 루트 레이아웃
├── components/            # 재사용 컴포넌트
│   ├── ui/               # 기본 UI 컴포넌트
│   └── admin/            # 관리자 전용 컴포넌트
├── lib/                  # 유틸리티 라이브러리
│   ├── utils/            # 헬퍼 함수들
│   └── validations/      # 스키마 검증
├── hooks/                # 커스텀 훅
├── store/                # Zustand 스토어
├── contexts/             # React 컨텍스트
├── scripts/              # 데이터베이스 스크립트
└── docs/                 # 프로젝트 문서
```

## 🗄️ **데이터베이스 설계**

### **핵심 테이블 구조**

#### **1. profiles (사용자 프로필)**

```sql
- id: UUID (Primary Key, auth.users 참조)
- email: TEXT (고유)
- name: TEXT (사용자명)
- account_type: TEXT ('admin' | 'user')
- company_name: TEXT (회사명)
- phone: TEXT (연락처)
- last_login_at: TIMESTAMP
- created_at: TIMESTAMP
```

#### **2. farms (농장 정보)**

```sql
- id: UUID (Primary Key)
- farm_name: TEXT (농장명)
- farm_address: TEXT (주소)
- farm_type: TEXT (농장 유형)
- owner_id: UUID (소유자 ID)
- manager_name: TEXT (관리자명)
- manager_phone: TEXT (관리자 연락처)
- is_active: BOOLEAN
- created_at: TIMESTAMP
```

#### **3. farm_members (농장 구성원)**

```sql
- id: UUID (Primary Key)
- farm_id: UUID (농장 ID)
- user_id: UUID (사용자 ID)
- role: TEXT ('owner' | 'manager' | 'viewer')
- position: TEXT (직책)
- is_active: BOOLEAN
- created_at: TIMESTAMP
```

#### **4. visitor_entries (방문자 기록)**

```sql
- id: UUID (Primary Key)
- farm_id: UUID (농장 ID)
- visit_datetime: TIMESTAMP (방문일시)
- visitor_name: TEXT (방문자명)
- visitor_phone: TEXT (연락처)
- visitor_address: TEXT (주소)
- visitor_purpose: TEXT (방문목적)
- disinfection_check: BOOLEAN (소독여부)
- vehicle_number: TEXT (차량번호)
- registered_by: UUID (등록자)
- session_token: TEXT (세션토큰)
- consent_given: BOOLEAN (개인정보동의)
- created_at: TIMESTAMP
```

#### **5. system_logs (시스템 로그)**

```sql
- id: UUID (Primary Key)
- level: TEXT ('error' | 'warn' | 'info' | 'debug')
- action: TEXT (액션 타입)
- message: TEXT (로그 메시지)
- user_id: UUID (사용자 ID)
- user_email: TEXT (사용자 이메일)
- resource_type: TEXT ('farm' | 'user' | 'visitor' | 'system')
- resource_id: UUID (관련 리소스 ID)
- metadata: JSONB (추가 데이터)
- created_at: TIMESTAMP
```

### **Storage 시스템 (2025-06-14 추가)**

#### **Storage 버킷 구조**

```sql
-- profiles 버킷 (프로필 사진)
profiles/
├── {userId}/
│   └── profile.{ext}    # 사용자별 프로필 사진

-- system 버킷 (시스템 파일)
system/
├── logo.{ext}           # 시스템 로고
└── favicon.{ext}        # 사이트 파비콘
```

#### **Storage 설정**

```sql
-- profiles 버킷
- 최대 파일 크기: 5MB
- 허용 타입: JPEG, JPG, PNG, WebP
- 접근 권한: 공개 읽기, 소유자만 쓰기
- 캐시 제어: 1시간 (3600초)

-- system 버킷
- 최대 파일 크기: 2MB (로고), 1MB (파비콘)
- 허용 타입: JPEG, PNG, WebP, SVG, ICO
- 접근 권한: 공개 읽기, 관리자만 쓰기
- 캐시 제어: 24시간 (86400초)
```

### **권한 시스템 (RLS)**

#### **계층적 권한 구조**

```
1. System Admin (시스템 관리자)
   - 모든 데이터 접근 및 관리 권한
   - 사용자 계정 관리
   - 시스템 설정 관리

2. Farm Owner (농장 소유자)
   - 자신의 농장 완전 관리 권한
   - 농장 구성원 관리
   - 방문자 기록 관리

3. Farm Manager (농장 관리자)
   - 할당된 농장의 방문자 관리
   - 농장 정보 수정 권한

4. Farm Viewer (농장 조회자)
   - 할당된 농장의 정보 조회만 가능

5. General User (일반 사용자)
   - 자신의 프로필 관리만 가능
```

## 🔧 **주요 기능**

### **1. 사용자 관리**

- 회원가입/로그인 (Supabase Auth)
- 프로필 관리 (개인정보, 회사정보)
- 권한별 접근 제어
- 로그인 통계 추적

### **2. 농장 관리**

- 농장 등록/수정/삭제
- 농장 유형 분류 (축산, 농업, 원예 등)
- 농장 구성원 관리
- QR 코드 생성 (방문자 등록용)

### **3. 방문자 관리**

- QR 코드 스캔을 통한 방문자 등록
- 방문 목적 및 소독 여부 기록
- 차량 정보 관리
- 개인정보 동의 처리

### **4. 관리자 기능**

- 대시보드 (통계 및 현황)
- 전체 농장/사용자 관리
- 방문자 기록 통합 조회
- 시스템 로그 관리
- CSV 데이터 내보내기

### **5. 시스템 로그**

- 모든 사용자 활동 추적
- 농장 관련 작업 로깅
- 오류 및 보안 이벤트 기록
- 로그 필터링 및 검색
- 로그 삭제 관리 (개별/대량/30일 이전)

### **6. 파일 관리 시스템 (2025-06-14 추가)**

- 프로필 사진 업로드 및 관리
- 시스템 로고/파비콘 업로드 (관리자 전용)
- 파일 유효성 검사 (크기, 타입)
- 실시간 미리보기 기능
- 메모리 누수 방지 (Blob URL 관리)
- 공통 파일 업로드 유틸리티

## 📱 **사용자 인터페이스**

### **반응형 디자인**

- 모바일 우선 설계
- 태블릿/데스크톱 최적화
- 다크/라이트 테마 지원

### **주요 페이지**

```
1. 대시보드 (/admin/dashboard)
   - 통계 카드 (사용자, 농장, 방문자)
   - 최근 활동 차트
   - 빠른 액션 버튼

2. 농장 관리 (/admin/farms)
   - 농장 목록 및 검색
   - 농장 등록/수정 폼
   - QR 코드 생성

3. 사용자 관리 (/admin/management)
   - 사용자 목록 및 필터링
   - 권한 관리
   - 시스템 로그 조회

4. 방문자 관리 (/admin/visitors)
   - 방문자 기록 조회
   - 필터링 및 검색
   - CSV 내보내기

5. 방문자 등록 (/visit/[farmId])
   - QR 코드 스캔 페이지
   - 방문자 정보 입력 폼
   - 개인정보 동의

6. 계정 관리 (/admin/account)
   - 프로필 정보 수정
   - 프로필 사진 업로드
   - 회사 정보 관리

7. 시스템 설정 (/admin/settings)
   - 시스템 로고 업로드
   - 파비콘 업로드
   - 브랜딩 설정 관리
```

## 🔒 **보안 및 인증**

### **인증 시스템**

- Supabase Auth 기반
- 이메일/비밀번호 인증
- 세션 관리 및 자동 갱신
- 비밀번호 재설정

### **보안 기능**

- Row Level Security (RLS) 적용
- CSRF 보호
- XSS 방지
- 개인정보 암호화 저장
- 감사 로그 (Audit Trail)

### **데이터 보호**

- 개인정보 동의 관리
- 데이터 최소 수집 원칙
- 로그 보존 정책 (30일)
- 안전한 데이터 삭제

### **파일 보안 (2025-06-14 추가)**

- Storage RLS 정책 적용
- 사용자별 폴더 분리
- 파일 타입 검증 (MIME 타입 + 확장자)
- 파일 크기 제한
- 관리자 전용 시스템 파일 관리

## 📊 **성능 최적화**

### **프론트엔드 최적화**

- Next.js App Router 활용
- 컴포넌트 지연 로딩
- 이미지 최적화
- 번들 크기 최적화

### **데이터베이스 최적화**

- 적절한 인덱스 설정
- 쿼리 최적화
- 페이지네이션 구현
- 캐싱 전략

### **Storage 최적화 (2025-06-14 추가)**

- CDN 활용 (Supabase Storage)
- 파일 크기별 캐시 전략
- 메모리 누수 방지 (Blob URL 관리)
- 공통 유틸리티로 코드 중복 제거

### **사용자 경험 최적화**

- 로딩 상태 표시
- 오류 처리 및 복구
- 오프라인 지원 (PWA)
- 접근성 (a11y) 준수

## 🚀 **배포 및 운영**

### **개발 환경**

```bash
npm run dev    # 개발 서버 실행
npm run build  # 프로덕션 빌드
npm run start  # 프로덕션 서버 실행
npm run lint   # 코드 린팅
```

### **환경 변수**

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### **배포 플랫폼**

- Vercel (권장)
- Netlify
- 자체 서버 (Docker)

## 📈 **모니터링 및 분석**

### **시스템 모니터링**

- 시스템 로그 분석
- 성능 메트릭 추적
- 오류 모니터링
- 사용자 활동 분석

### **비즈니스 인사이트**

- 농장별 방문자 통계
- 지역별 분포 분석
- 시간대별 방문 패턴
- 농장 유형별 분석

## 🔄 **업데이트 및 유지보수**

### **버전 관리**

- 시맨틱 버저닝 (Semantic Versioning)
- 변경사항 문서화 (CHANGELOG.md)
- 데이터베이스 마이그레이션 관리

### **백업 및 복구**

- 정기적 데이터베이스 백업
- 재해 복구 계획
- 데이터 무결성 검증

## 📚 **개발 가이드**

### **코딩 컨벤션**

- TypeScript 엄격 모드
- ESLint + Prettier 설정
- 컴포넌트 네이밍 규칙
- 파일 구조 가이드라인

### **테스팅 전략**

- 단위 테스트 (Jest)
- 통합 테스트
- E2E 테스트 (Playwright)
- 접근성 테스트

### **기여 가이드**

- 이슈 템플릿
- PR 템플릿
- 코드 리뷰 가이드라인
- 문서화 규칙

## 📁 **관련 문서**

### **핵심 문서**

- [API 및 기능 명세서](./api-and-features-specification.md)
- [데이터베이스 최종 분석](./database-final-analysis.md)
- [배포 및 운영 가이드](./deployment-and-operations-guide.md)
- [개발 히스토리 및 변경사항](./development-history-and-changelog.md)

### **기술 문서**

- [데이터베이스 설정 가이드](./database-setup-guide.md)
- [보안 가이드](./SECURITY.md)
- [개발 가이드](./DEVELOPMENT.md)
- [빠른 참조](./quick-reference.md)
- [파일 업로드 유틸리티 구현](./file-upload-utility-implementation.md)
- [Storage 마이그레이션 가이드](./storage-migration-guide.md)

### **운영 문서**

- [관리자 계정 생성](./admin-account-creation.md)
- [시스템 로그 개선사항](./system-log-improvements.md)
- [오류 페이지 구현](./error-pages-implementation.md)

## 🎯 **프로젝트 상태**

### **개발 완료도**

```
✅ 핵심 기능: 100% 완료
✅ 보안 시스템: 100% 완료
✅ 관리자 기능: 100% 완료
✅ 사용자 인터페이스: 100% 완료
✅ 데이터베이스 설계: 100% 완료
✅ API 개발: 100% 완료
✅ 파일 업로드 시스템: 100% 완료 (2025-06-14 추가)
✅ Storage 시스템: 100% 완료 (2025-06-14 추가)
✅ 테스트: 95% 완료
✅ 문서화: 100% 완료
```

### **배포 준비도**

```
✅ 프로덕션 환경 설정: 완료
✅ 보안 검토: 완료
✅ 성능 최적화: 완료
✅ 모니터링 설정: 완료
✅ 백업 전략: 완료
✅ 장애 대응 계획: 완료
```

## 🚀 **빠른 시작**

### **개발 환경 설정**

```bash
# 1. 저장소 클론
git clone [repository-url]
cd farm-visitor-management

# 2. 의존성 설치
npm install

# 3. 환경 변수 설정
cp .env.example .env.local
# .env.local 파일 편집

# 4. 데이터베이스 설정
# Supabase에서 scripts/database-reset-and-rebuild.sql 실행

# 5. 개발 서버 실행
npm run dev
```

### **프로덕션 배포**

```bash
# Vercel 배포 (권장)
vercel --prod

# 또는 Docker 배포
docker build -t farm-visitor-system .
docker run -p 3000:3000 farm-visitor-system
```

## 📞 **지원 및 문의**

### **기술 지원**

- 이슈 트래커: GitHub Issues
- 문서: `/docs` 폴더 참조
- 이메일: [기술지원 이메일]

### **비즈니스 문의**

- 제품 문의: [비즈니스 이메일]
- 파트너십: [파트너십 이메일]
- 일반 문의: [일반 문의 이메일]

---

**문서 작성일**: 2024-12-14
**최종 업데이트**: 2025-06-14 (파일 업로드 시스템 추가)
**문서 버전**: 1.1.0
**프로젝트 상태**: 프로덕션 준비 완료 ✅

## 시스템 아키텍처 변경사항

### 보안 시스템 강화

#### 비밀번호 검증 시스템

```mermaid
graph TD
  A[사용자 입력] --> B[클라이언트 검증]
  B --> C{검증 통과?}
  C -->|Yes| D[서버 요청]
  C -->|No| E[오류 표시]
  D --> F[서버 검증]
  F --> G{검증 통과?}
  G -->|Yes| H[비밀번호 처리]
  G -->|No| I[오류 응답]
```

#### 아키텍처 구성

1. **프레젠테이션 계층**

   - React 컴포넌트
   - 실시간 유효성 검사
   - 사용자 피드백

2. **비즈니스 로직 계층**

   - 비밀번호 검증 유틸리티
   - 보안 규칙 적용
   - 에러 처리

3. **데이터 계층**
   - Supabase Auth
   - 비밀번호 이력 관리
   - 보안 로깅

### 보안 아키텍처

#### 1. 인증 흐름

```mermaid
sequenceDiagram
  participant U as 사용자
  participant C as 클라이언트
  participant S as 서버
  participant D as 데이터베이스

  U->>C: 비밀번호 입력
  C->>C: 로컬 검증
  C->>S: 인증 요청
  S->>D: 비밀번호 검증
  D-->>S: 결과 반환
  S-->>C: 응답
  C-->>U: 피드백
```

#### 2. 보안 계층

- **클라이언트 보안**

  - HTTPS 통신
  - XSS 방지
  - CSRF 토큰

- **서버 보안**

  - 비밀번호 해싱
  - 레이트 리미팅
  - 세션 관리

- **데이터베이스 보안**
  - 암호화 저장
  - 접근 제어
  - 감사 로깅

## 향후 계획

### 1. 보안 강화 (Q2 2024)

- [ ] 2FA 구현
- [ ] 생체 인증 통합
- [ ] 보안 감사 시스템

### 2. 사용자 경험 개선 (Q3 2024)

- [ ] 비밀번호 생성기 추가
- [ ] 다국어 지원
- [ ] 접근성 개선

### 3. 모니터링 강화 (Q4 2024)

- [ ] 실시간 보안 대시보드
- [ ] 이상 탐지 시스템
- [ ] 자동화된 보고서

### 4. 통합 확장 (2025)

- [ ] SSO 구현
- [ ] OAuth 제공자 추가
- [ ] 엔터프라이즈 기능
