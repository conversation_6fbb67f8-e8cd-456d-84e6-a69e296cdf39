# 공통 파일 업로드 유틸리티 구현

## 📋 **개요**

**목적**: 프로필 사진, 시스템 로고, 파비콘 등 다양한 파일 업로드를 위한 재사용 가능한 공통 유틸리티 구현  
**구현일**: 2025-06-14  
**위치**: `lib/utils/file-upload.ts`

## 🎯 **구현 목표**

### **기존 문제점**

- 계정관리 페이지에 프로필 사진 업로드 로직이 중복되어 있음
- 시스템 설정에서 로고/파비콘 업로드 시 동일한 로직을 재구현해야 함
- 파일 유효성 검사, 업로드, 에러 처리 로직이 분산되어 유지보수 어려움

### **해결 방안**

- 공통 파일 업로드 유틸리티 함수 구현
- 기존 중복 코드 제거 및 공통 함수 활용
- 확장 가능한 구조로 설계하여 향후 다른 파일 업로드에도 재사용 가능

## 🔧 **구현 세부사항**

### **1. 핵심 인터페이스**

#### **FileUploadOptions**

```typescript
interface FileUploadOptions {
  bucket: string; // Supabase Storage 버킷 이름
  filePath: string; // 파일 경로 (예: "user123/avatar")
  allowedTypes?: string[]; // 허용되는 MIME 타입들
  maxSize?: number; // 최대 파일 크기 (바이트)
  upsert?: boolean; // 파일 덮어쓰기 여부
  cacheControl?: string; // 캐시 제어 시간 (초)
}
```

#### **FileUploadResult**

```typescript
interface FileUploadResult {
  success: boolean; // 업로드 성공 여부
  publicUrl?: string; // 업로드된 파일의 공개 URL
  error?: string; // 에러 메시지
  fileInfo?: {
    // 업로드된 파일 정보
    fileName: string;
    fileSize: number;
    fileType: string;
  };
}
```

### **2. 핵심 함수들**

#### **validateFile()**

```typescript
export const validateFile = (
  file: File,
  options: FileUploadOptions
): FileValidationResult => {
  // 파일 크기 검사
  // 파일 타입 검사
  // 유효성 검사 결과 반환
};
```

#### **uploadFile()**

```typescript
export const uploadFile = async (
  file: File,
  options: FileUploadOptions
): Promise<FileUploadResult> => {
  // 1. 파일 유효성 검사
  // 2. Supabase Storage에 업로드
  // 3. 공개 URL 생성
  // 4. 결과 반환
};
```

#### **특화된 헬퍼 함수들**

```typescript
// 프로필 사진 업로드
export const uploadProfileImage = async (file: File, userId: string)

// 시스템 로고 업로드
export const uploadSystemLogo = async (file: File)

// 파비콘 업로드
export const uploadFavicon = async (file: File)
```

### **3. 유틸리티 함수들**

#### **파일 미리보기 관리**

```typescript
// 미리보기 URL 생성
export const createFilePreview = (file: File): string

// 미리보기 URL 해제 (메모리 누수 방지)
export const revokeFilePreview = (previewUrl: string): void
```

#### **파일 정보 처리**

```typescript
// 파일 크기 포맷팅
export const formatFileSize = (bytes: number): string

// 파일 확장자 추출
export const getFileExtension = (mimeType: string): string
```

## 📁 **Storage 버킷 구조**

### **profiles 버킷**

```
profiles/
├── {userId}/
│   └── profile.{ext}    # 사용자별 프로필 사진
```

**설정**:

- 최대 파일 크기: 5MB
- 허용 타입: JPEG, JPG, PNG, WebP
- 접근 권한: 공개 읽기, 소유자만 쓰기

### **system 버킷**

```
system/
├── logo.{ext}           # 시스템 로고
└── favicon.{ext}        # 사이트 파비콘
```

**설정**:

- 최대 파일 크기: 2MB (로고), 1MB (파비콘)
- 허용 타입: JPEG, PNG, WebP, SVG, ICO
- 접근 권한: 공개 읽기, 관리자만 쓰기

## 🔄 **기존 코드 리팩토링**

### **계정관리 페이지 (`app/admin/account/page.tsx`)**

#### **Before (기존 코드)**

```typescript
// 200+ 줄의 중복된 업로드 로직
const handleAvatarUpload = async () => {
  // 파일 유효성 검사
  if (avatarFile.size > 5 * 1024 * 1024) { ... }
  if (!avatarFile.type.startsWith("image/")) { ... }

  // Supabase Storage 업로드
  const fileExt = avatarFile.type.split("/")[1];
  const fileName = `${profile.id}/profile.${fileExt}`;
  const { error: uploadError } = await supabase.storage
    .from("profiles")
    .upload(fileName, avatarFile, { ... });

  // 공개 URL 생성
  const { data: urlData } = supabase.storage
    .from("profiles")
    .getPublicUrl(fileName);

  // 에러 처리 및 상태 업데이트
  // ...
};
```

#### **After (리팩토링 후)**

```typescript
// 간결한 공통 유틸리티 활용
const handleAvatarUpload = async () => {
  if (!avatarFile || !profile?.id) return;

  setLoading(true);
  try {
    // 공통 유틸리티 사용
    const uploadResult = await uploadProfileImage(avatarFile, profile.id);

    if (!uploadResult.success) {
      throw new Error(uploadResult.error || "업로드 실패");
    }

    // 프로필 업데이트
    await updateProfileImage(uploadResult.publicUrl);

    // 상태 정리
    setAvatarFile(null);
    if (avatarPreview?.startsWith("blob:")) {
      revokeFilePreview(avatarPreview);
    }
    setAvatarPreview(uploadResult.publicUrl);
  } catch (error) {
    // 에러 처리
  } finally {
    setLoading(false);
  }
};
```

### **시스템 설정 페이지 (`app/admin/settings/page.tsx`)**

#### **새로 구현된 기능**

```typescript
// 로고 업로드
const handleLogoUpload = async () => {
  const uploadResult = await uploadSystemLogo(logoFile);
  // 결과 처리
};

// 파비콘 업로드
const handleFaviconUpload = async () => {
  const uploadResult = await uploadFavicon(faviconFile);
  // 결과 처리
};
```

## 🎨 **UI 구현**

### **프로필 사진 업로드 UI**

- 원형 프로필 이미지 미리보기
- 카메라 아이콘 버튼으로 파일 선택
- 선택된 파일 정보 표시
- 업로드 진행 상태 표시

### **로고/파비콘 업로드 UI**

- 사각형 미리보기 영역
- 드래그 앤 드롭 스타일 테두리
- 파일 타입별 아이콘 표시
- 업로드 버튼 및 진행 상태

## 🔒 **보안 고려사항**

### **파일 유효성 검사**

- 파일 크기 제한 (프로필: 5MB, 로고: 2MB, 파비콘: 1MB)
- MIME 타입 검증
- 파일 확장자 검증

### **접근 권한 제어**

- 프로필 사진: 소유자만 업로드/수정/삭제 가능
- 시스템 파일: 관리자만 업로드/수정/삭제 가능
- 모든 파일: 공개 읽기 가능

### **Storage 정책 (RLS)**

```sql
-- 프로필 사진: 자신의 폴더에만 업로드 가능
CREATE POLICY "Users can upload their own profile image" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'profiles'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

-- 시스템 파일: 관리자만 업로드 가능
CREATE POLICY "Only admins can upload system files" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'system'
  AND auth.role() = 'authenticated'
  AND EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND account_type = 'admin'
  )
);
```

## 📊 **성능 최적화**

### **메모리 관리**

- `createFilePreview()`: Blob URL 생성
- `revokeFilePreview()`: 사용 후 Blob URL 해제로 메모리 누수 방지

### **캐시 최적화**

- 프로필 사진: 1시간 캐시 (`cacheControl: "3600"`)
- 시스템 파일: 24시간 캐시 (`cacheControl: "86400"`)

### **파일 크기 최적화**

- 적절한 파일 크기 제한 설정
- 압축 권장 안내 메시지

## 🧪 **테스트 시나리오**

### **기능 테스트**

1. **프로필 사진 업로드**

   - 유효한 이미지 파일 업로드
   - 크기 초과 파일 업로드 시도
   - 잘못된 파일 타입 업로드 시도

2. **시스템 로고 업로드**

   - 관리자 계정으로 로고 업로드
   - 일반 사용자 계정으로 업로드 시도 (실패해야 함)
   - SVG 파일 업로드

3. **파비콘 업로드**
   - ICO 파일 업로드
   - PNG 파일 업로드
   - 크기 초과 파일 업로드 시도

### **에러 처리 테스트**

- 네트워크 연결 실패
- Storage 버킷 접근 권한 없음
- 파일 업로드 중 중단

## 🚀 **향후 확장 계획**

### **추가 파일 타입 지원**

- 문서 파일 업로드 (PDF, DOC)
- 동영상 파일 업로드
- 압축 파일 업로드

### **고급 기능**

- 이미지 자동 리사이징
- 썸네일 자동 생성
- 진행률 표시 개선
- 드래그 앤 드롭 지원

### **성능 개선**

- 청크 업로드 (대용량 파일)
- 병렬 업로드
- 업로드 재시도 메커니즘

---

**구현자**: AI Assistant  
**구현 완료일**: 2025-06-14  
**다음 업데이트**: 추가 기능 요청 시
