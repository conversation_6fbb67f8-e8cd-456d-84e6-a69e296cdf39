"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/components/providers/auth-provider";
import { supabase } from "@/lib/supabase";
import {
  getFarmTypeLabel,
  getFarmTypeIcon,
  getFarmTypeColor,
} from "@/lib/constants/farm-types";
import { createSystemLog } from "@/lib/utils/system-log";
import { getRegionFromAddress } from "@/lib/utils/region";
import { formatDateTime } from "@/lib/utils/date";
import {
  Users,
  Building2,
  FileText,
  BarChart3,
  Activity,
  UserCheck,
  UserX,
  Eye,
  Search,
  Filter,
  Download,
  RefreshCw,
  Trash2,
  Clock,
  MapPin,
  Phone,
  Mail,
  Calendar,
  CheckCircle,
  XCircle,
  AlertCircle,
  AlertTriangle,
  Home,
} from "lucide-react";
import Link from "next/link";

interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  account_type: string; // 시스템 레벨 권한 (admin, user)
  status: string;
  is_active?: boolean; // 활성 상태
  last_login_at: string;
  created_at: string;
}

interface Farm {
  id: string;
  farm_name: string; // 데이터베이스 필드명과 일치
  farm_address: string; // 데이터베이스 필드명과 일치
  owner_id: string;
  owner_name: string; // 조인으로 추가되는 필드
  owner_email: string; // 조인으로 추가되는 필드
  owner_phone: string; // 조인으로 추가되는 필드
  description?: string;
  farm_type?: string;
  manager_name?: string;
  manager_phone?: string;
  is_active?: boolean;
  created_at: string;
  updated_at?: string;
}

interface SystemLog {
  id: string;
  user_id: string;
  user_name: string;
  action: string;
  details: string;
  ip_address: string;
  user_agent: string;
  created_at: string;
  level: "info" | "warning" | "error";
}

export default function SystemManagementPage() {
  const { profile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  // 검색 로그 함수
  const handleSearch = async (term: string, type: "user" | "farm" | "log") => {
    setSearchTerm(term);
    if (term.trim() && term.length >= 2) {
      await createSystemLog(
        `${type.toUpperCase()}_SEARCH`,
        `관리자가 ${
          type === "user" ? "사용자" : type === "farm" ? "농장" : "로그"
        }를 "${term}" 키워드로 검색했습니다`,
        "info",
        profile?.id,
        type === "user" ? "user" : "farm",
        undefined,
        {
          search_term: term,
          search_type: type,
          admin_name: profile?.name,
          admin_email: profile?.email,
        }
      );
    }
  };
  const [userFilter, setUserFilter] = useState("all");

  // 필터 변경 로그 함수
  const handleUserFilterChange = async (newFilter: string) => {
    setUserFilter(newFilter);
    if (newFilter !== "all") {
      await createSystemLog(
        "USER_FILTER_APPLIED",
        `관리자가 사용자 필터를 "${newFilter}"로 변경했습니다`,
        "info",
        profile?.id,
        "user",
        undefined,
        {
          filter_type: "user",
          filter_value: newFilter,
          admin_name: profile?.name,
          admin_email: profile?.email,
        }
      );
    }
  };

  const handleFarmFilterChange = async (newFilter: string) => {
    setFarmFilter(newFilter);
    if (newFilter !== "all") {
      await createSystemLog(
        "FARM_FILTER_APPLIED",
        `관리자가 농장 필터를 "${newFilter}"로 변경했습니다`,
        "info",
        profile?.id,
        "farm",
        undefined,
        {
          filter_type: "farm",
          filter_value: newFilter,
          admin_name: profile?.name,
          admin_email: profile?.email,
        }
      );
    }
  };

  // 사용자 카테고리 확인 함수들
  const isSystemAdmin = (user: User) => user.account_type === "admin";
  const isFarmOwner = (user: User) =>
    farms.some((farm) => farm.owner_id === user.id);
  const isGeneralUser = (user: User) =>
    user.account_type === "user" && !isFarmOwner(user);

  // 필터별 사용자 수 계산
  const getUserCountByFilter = (filterType: string) => {
    return users.filter((user) => {
      switch (filterType) {
        case "all":
          return true;
        case "active":
          return !!user.last_login_at;
        case "inactive":
          return !user.last_login_at;
        case "admin":
          return isSystemAdmin(user);
        case "owner":
          return isFarmOwner(user);
        case "user":
          return isGeneralUser(user);
        default:
          return false;
      }
    }).length;
  };

  // 오늘 날짜 기준 통계 계산 함수들
  const getTodayStats = () => {
    const today = new Date().toDateString();

    return {
      todayLogins: users.filter(
        (user) =>
          user.last_login_at &&
          new Date(user.last_login_at).toDateString() === today
      ).length,

      todayFarms: farms.filter(
        (farm) => new Date(farm.created_at).toDateString() === today
      ).length,

      todayVisitors: visitors.filter(
        (visitor) => new Date(visitor.visit_datetime).toDateString() === today
      ).length,

      todayQRScans: visitors.filter(
        (visitor) =>
          visitor.session_token &&
          new Date(visitor.visit_datetime).toDateString() === today
      ).length,

      hasErrors: logs.filter((log) => log.level === "error").length > 0,
    };
  }; // all, active, inactive, admin, farm_owner
  const [farmFilter, setFarmFilter] = useState("all"); // all, recent, has_owner, by_region
  const [logFilter, setLogFilter] = useState("all"); // all, info, warning, error

  // CSV 다운로드 옵션 상태
  const [csvStartDate, setCsvStartDate] = useState("");
  const [csvEndDate, setCsvEndDate] = useState("");
  const [csvUserRole, setCsvUserRole] = useState("all"); // all, admin, farm_owner, farm_manager, user
  const [csvUserActivity, setCsvUserActivity] = useState("all"); // all, active, inactive, new
  const [csvIncludeBasic, setCsvIncludeBasic] = useState(true);
  const [csvIncludeActivity, setCsvIncludeActivity] = useState(true);
  const [csvIncludeContact, setCsvIncludeContact] = useState(false);
  const [csvIncludeFarm, setCsvIncludeFarm] = useState(false);

  // 농장 CSV 옵션
  const [csvFarmBasic, setCsvFarmBasic] = useState(true);
  const [csvFarmLocation, setCsvFarmLocation] = useState(true);
  const [csvFarmOwner, setCsvFarmOwner] = useState(true);
  const [csvFarmStats, setCsvFarmStats] = useState(false);

  // 시스템 로그 CSV 옵션
  const [csvLogBasic, setCsvLogBasic] = useState(true);
  const [csvLogDetails, setCsvLogDetails] = useState(true);
  const [csvLogUser, setCsvLogUser] = useState(true);
  const [csvLogSystem, setCsvLogSystem] = useState(false);

  // 사용자 상세보기 상태
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isUserDetailOpen, setIsUserDetailOpen] = useState(false);

  // 농장 상세보기 상태
  const [selectedFarm, setSelectedFarm] = useState<Farm | null>(null);
  const [isFarmDetailOpen, setIsFarmDetailOpen] = useState(false);

  // 로그 상세보기 상태
  const [selectedLog, setSelectedLog] = useState<SystemLog | null>(null);
  const [isLogDetailOpen, setIsLogDetailOpen] = useState(false);
  const [isDeleteAllConfirmOpen, setIsDeleteAllConfirmOpen] = useState(false);

  // 로그 페이지네이션 상태
  const [logPage, setLogPage] = useState(1);
  const [logsPerPage] = useState(100); // 페이지당 100개 로그
  const [totalLogs, setTotalLogs] = useState(0);
  const [users, setUsers] = useState<User[]>([]);
  const [farms, setFarms] = useState<Farm[]>([]);
  const [logs, setLogs] = useState<SystemLog[]>([]);
  const [visitors, setVisitors] = useState<any[]>([]);
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    farmOwners: 0,
    totalFarms: 0,
    todayLogins: 0,
    totalLogs: 0,
    errorLogs: 0,
  });
  const { toast } = useToast();

  // 데이터 로드
  useEffect(() => {
    loadData();

    // 페이지 접근 로그
    createSystemLog(
      "ADMIN_PAGE_ACCESS",
      "관리자가 시스템 관리 페이지에 접근했습니다",
      "info",
      profile?.id,
      "system",
      undefined,
      {
        page: "admin_management",
        timestamp: new Date().toISOString(),
        admin_name: profile?.name,
        admin_email: profile?.email,
      }
    );
  }, []);

  // 페이지 변경 시 로그 다시 로딩
  useEffect(() => {
    if (logPage > 1) {
      // 첫 페이지는 초기 로딩에서 처리
      loadLogs();
    }
  }, [logPage]);

  const loadData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadUsers(),
        loadFarms(),
        loadLogs(),
        loadVisitors(),
        loadStats(),
      ]);

      // 데이터 로딩 성공 로그
      await createSystemLog(
        "DATA_LOADED",
        "시스템 관리 데이터가 성공적으로 로드되었습니다",
        "info",
        profile?.id,
        "system",
        undefined,
        {
          users_count: users.length,
          farms_count: farms.length,
          logs_count: logs.length,
          visitors_count: visitors.length,
          admin_name: profile?.name,
          admin_email: profile?.email,
        }
      );
    } catch (error) {
      console.error("Data loading error:", error);

      // 데이터 로딩 실패 로그
      await logError("DATA_LOAD_ERROR", error, "시스템 관리 데이터 로딩");

      toast({
        title: "데이터 로드 실패",
        description: "데이터를 불러오는 중 오류가 발생했습니다.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadUsers = async () => {
    try {
      console.log("Loading users...");

      // 활성 사용자만 조회 (클라이언트에서 정렬)
      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("is_active", true);

      if (error) {
        console.error("Users loading error:", error);
        setUsers([]);
        return;
      }

      console.log("Loaded users data:", data);
      console.log("Users count:", data?.length || 0);

      // 사용자 역할별 통계 출력 (account_type 기준)
      if (data) {
        const roleStats = data.reduce((acc, user) => {
          const role = user.account_type || "user";
          acc[role] = (acc[role] || 0) + 1;
          return acc;
        }, {});
        console.log("User account_type statistics:", roleStats);

        // 활성/비활성 사용자 통계
        const activeUsers = data.filter((user) => user.last_login_at).length;
        const inactiveUsers = data.length - activeUsers;
        console.log(
          "Active users:",
          activeUsers,
          "Inactive users:",
          inactiveUsers
        );

        // 농장 소유자 확인 (farms 테이블에서 owner_id로 확인)
        const { data: farmsData } = await supabase
          .from("farms")
          .select("owner_id")
          .eq("is_active", true);

        const farmOwnerIds = new Set(farmsData?.map((f) => f.owner_id) || []);
        const farmOwners = data.filter((user) => farmOwnerIds.has(user.id));

        console.log("Farm owners found:", farmOwners.length);
        console.log(
          "Farm owners details:",
          farmOwners.map((u) => ({
            id: u.id,
            name: u.name,
            email: u.email,
            account_type: u.account_type,
            last_login_at: u.last_login_at,
          }))
        );

        // 모든 사용자의 역할 정보
        console.log(
          "All users account_types:",
          data.map((u) => ({
            name: u.name,
            email: u.email,
            account_type: u.account_type || "undefined",
          }))
        );
      }

      // 사용자 우선순위 기반 정렬
      const sortedUsers = (data || []).sort((a, b) => {
        // 1. 계정 유형별 우선순위 (admin > user)
        const getPriority = (user: any) => {
          if (user.account_type === "admin") return 1;
          if (user.account_type === "user") return 2;
          return 3; // 기타
        };

        const priorityA = getPriority(a);
        const priorityB = getPriority(b);

        if (priorityA !== priorityB) {
          return priorityA - priorityB;
        }

        // 2. 같은 유형 내에서는 농장 소유 여부로 정렬
        const farmOwnerIds = new Set(farms.map((f) => f.owner_id));
        const isOwnerA = farmOwnerIds.has(a.id);
        const isOwnerB = farmOwnerIds.has(b.id);

        if (isOwnerA !== isOwnerB) {
          return isOwnerB ? 1 : -1; // 농장 소유자가 먼저
        }

        // 3. 마지막으로 이름순 정렬
        const nameA = a.name || a.email || "";
        const nameB = b.name || b.email || "";
        return nameA.localeCompare(nameB, "ko-KR");
      });

      setUsers(sortedUsers);
    } catch (error) {
      console.error("Error loading users:", error);
      setUsers([]);
    }
  };

  const loadFarms = async () => {
    try {
      console.log("Loading farms...");
      const { data, error } = await supabase
        .from("farms")
        .select(
          `
          *,
          profiles!farms_owner_id_fkey(name, email, phone)
        `
        )
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Farms loading error:", error);
        // 에러가 있어도 빈 배열로 설정하여 UI가 표시되도록 함
        setFarms([]);
        return;
      }

      console.log("Loaded farms data:", data);

      // 농장 데이터에 소유자 정보 추가
      const farmsWithOwner = (data || []).map((farm) => ({
        ...farm,
        owner_name: farm.profiles?.name || "소유자 정보 없음",
        owner_email: farm.profiles?.email || "이메일 정보 없음",
        owner_phone: farm.profiles?.phone || "연락처 정보 없음",
      }));

      console.log("Processed farms with owner:", farmsWithOwner);
      setFarms(farmsWithOwner);
    } catch (error) {
      console.error("Error loading farms:", error);
      setFarms([]);
    }
  };

  const loadLogs = async () => {
    try {
      console.log("Loading system logs...");

      // 실제 시스템 로그 데이터 조회 (페이징 적용)
      const offset = (logPage - 1) * logsPerPage;
      const {
        data: systemLogsData,
        error: systemLogsError,
        count,
      } = await supabase
        .from("system_logs")
        .select("*", { count: "exact" })
        .order("created_at", { ascending: false })
        .range(offset, offset + logsPerPage - 1); // 페이징 적용

      console.log("System logs query result:", {
        systemLogsData,
        systemLogsError,
        totalCount: count,
        loadedCount: systemLogsData?.length,
      });

      if (systemLogsError) {
        console.error("System logs loading error:", systemLogsError);
        // 오류가 있어도 계속 진행하여 Mock 로그 생성
      }

      // 실제 로그가 있으면 우선 사용, 없으면 Mock 로그와 혼합
      let finalLogs: SystemLog[] = [];

      if (systemLogsData && systemLogsData.length > 0) {
        console.log(`Found ${systemLogsData.length} real system logs`);

        // 사용자 정보 별도 조회
        const { data: usersData } = await supabase
          .from("profiles")
          .select("id, name, email");

        const usersMap = new Map(usersData?.map((u) => [u.id, u]) || []);

        // 실제 system_logs 데이터 처리
        const systemLogs: SystemLog[] = systemLogsData.map((log) => {
          const user = usersMap.get(log.user_id);
          return {
            id: log.id,
            user_id: log.user_id,
            user_name:
              user?.name ||
              user?.email ||
              log.user_email ||
              "알 수 없는 사용자",
            action: log.action || "UNKNOWN",
            details: log.message || "상세 정보 없음", // ← message 필드 사용
            ip_address: log.user_ip || "알 수 없음", // ← user_ip 필드 사용
            user_agent: log.user_agent || "알 수 없음",
            created_at: log.created_at,
            level: log.level || "info",
          };
        });

        finalLogs = systemLogs;
        setTotalLogs(count || systemLogs.length);
        console.log(
          "Using real system logs:",
          finalLogs.length,
          "Total in DB:",
          count
        );
      } else {
        console.log("No system logs found");
        finalLogs = [];
      }

      // 최종 로그 설정
      setLogs(finalLogs);
      console.log("Final logs set:", finalLogs.length, "logs loaded");
    } catch (error) {
      console.error("Logs loading error:", error);
      setLogs([]);
    }
  };

  // 방문자 데이터 로드
  const loadVisitors = async () => {
    try {
      const { data, error } = await supabase
        .from("visitor_entries")
        .select(
          `
          *,
          farms (
            farm_name,
            farm_address,
            farm_type
          )
        `
        )
        .order("visit_datetime", { ascending: false });

      if (error) throw error;

      setVisitors(data || []);
    } catch (error) {
      console.error("Error loading visitors:", error);
      setVisitors([]); // 오류 시 빈 배열로 설정
    }
  };

  const loadStats = async () => {
    try {
      console.log("Loading stats...");
      // 통계 데이터 계산
      const today = new Date().toISOString().split("T")[0];

      // 전체 오류 로그 개수 조회
      const { count: errorLogsCount } = await supabase
        .from("system_logs")
        .select("*", { count: "exact", head: true })
        .eq("level", "error");

      const { data: usersData, error: usersError } = await supabase
        .from("profiles")
        .select("id, last_login_at, account_type")
        .eq("is_active", true);

      const { data: farmsData, error: farmsError } = await supabase
        .from("farms")
        .select("id, owner_id")
        .eq("is_active", true);

      if (usersError) {
        console.error("Users stats loading error:", usersError);
      }
      if (farmsError) {
        console.error("Farms stats loading error:", farmsError);
      }

      if (usersData && farmsData) {
        const todayLogins = usersData.filter(
          (user) => user.last_login_at && user.last_login_at.startsWith(today)
        ).length;

        const activeUsers = usersData.filter(
          (user) => user.last_login_at
        ).length;

        // 농장 소유자 수 계산 (중복 제거)
        const farmOwnerIds = new Set(farmsData.map((f) => f.owner_id));
        const farmOwnersCount = farmOwnerIds.size;

        const newStats = {
          totalUsers: usersData.length,
          activeUsers: activeUsers,
          farmOwners: farmOwnersCount,
          totalFarms: farmsData.length,
          todayLogins,
          totalLogs: totalLogs, // 전체 로그 개수 사용
          errorLogs: errorLogsCount || 0, // 전체 오류 로그 개수 사용
        };

        console.log("Calculated stats:", newStats);
        setStats(newStats);
      } else {
        console.warn("Failed to load stats data");
        // 기본값 설정
        const farmOwnerIds = new Set(farms.map((f) => f.owner_id));
        setStats({
          totalUsers: users.length,
          activeUsers: users.filter((user) => user.last_login_at).length,
          farmOwners: farmOwnerIds.size,
          totalFarms: farms.length,
          todayLogins: 0,
          totalLogs: totalLogs, // 전체 로그 개수 사용
          errorLogs: errorLogsCount || 0, // 전체 오류 로그 개수 사용
        });
      }
    } catch (error) {
      console.error("Error loading stats:", error);
      // 오류 발생 시 현재 데이터로 통계 계산
      const farmOwnerIds = new Set(farms.map((f) => f.owner_id));
      setStats({
        totalUsers: users.length,
        activeUsers: users.filter((user) => user.last_login_at).length,
        farmOwners: farmOwnerIds.size,
        totalFarms: farms.length,
        todayLogins: 0,
        totalLogs: totalLogs, // 전체 로그 개수 사용
        errorLogs: 0, // 오류 발생 시 기본값
      });
    }
  };

  const handleRefresh = async () => {
    await loadData();

    // 시스템 로그 생성
    await createSystemLog(
      "SYSTEM_REFRESH",
      "관리자가 시스템 관리 페이지를 새로고침했습니다",
      "info",
      profile?.id,
      "system",
      undefined,
      {
        action: "manual_refresh",
        timestamp: new Date().toISOString(),
        admin_name: profile?.name,
        admin_email: profile?.email,
      }
    );

    toast({
      title: "새로고침 완료",
      description: "데이터가 성공적으로 새로고침되었습니다.",
    });
  };

  // 모든 모달 닫기 함수
  const closeAllModals = () => {
    setIsUserDetailOpen(false);
    setIsFarmDetailOpen(false);
    setIsLogDetailOpen(false);
    setSelectedUser(null);
    setSelectedFarm(null);
    setSelectedLog(null);
  };

  // 사용자 상세보기 함수
  const handleUserDetail = async (user: User) => {
    closeAllModals(); // 다른 모달들 먼저 닫기
    setTimeout(() => {
      setSelectedUser(user);
      setIsUserDetailOpen(true);
    }, 100); // 약간의 지연으로 모달 전환 부드럽게

    // 시스템 로그 생성
    await createSystemLog(
      "USER_DETAIL_VIEW",
      `관리자가 사용자 "${user.name || user.email}" 상세 정보를 조회했습니다`,
      "info",
      profile?.id,
      "user",
      user.id,
      {
        user_name: user.name,
        user_email: user.email,
        account_type: user.account_type,
        viewed_by: "admin",
        admin_name: profile?.name,
        admin_email: profile?.email,
      }
    );
  };

  // 농장 상세보기 함수
  const handleFarmDetail = async (farm: Farm) => {
    closeAllModals(); // 다른 모달들 먼저 닫기
    setTimeout(() => {
      setSelectedFarm(farm);
      setIsFarmDetailOpen(true);
    }, 100);

    // 시스템 로그 생성
    await createSystemLog(
      "FARM_DETAIL_VIEW",
      `관리자가 농장 "${
        farm.farm_name || "이름 없음"
      }" 상세 정보를 조회했습니다`,
      "info",
      profile?.id,
      "farm",
      farm.id,
      {
        farm_name: farm.farm_name,
        farm_address: farm.farm_address,
        owner_id: farm.owner_id,
        viewed_by: "admin",
        admin_name: profile?.name,
        admin_email: profile?.email,
      }
    );
  };

  // 로그 상세보기 함수
  const handleLogDetail = async (log: SystemLog) => {
    closeAllModals(); // 다른 모달들 먼저 닫기
    setTimeout(() => {
      setSelectedLog(log);
      setIsLogDetailOpen(true);
    }, 100);

    // 시스템 로그 생성
    await createSystemLog(
      "LOG_DETAIL_VIEW",
      `관리자가 시스템 로그 "${log.action}" 상세 정보를 조회했습니다`,
      "info",
      profile?.id,
      "system_log",
      log.id,
      {
        log_action: log.action,
        log_level: log.level,
        log_created_at: log.created_at,
        viewed_by: "admin",
        admin_name: profile?.name,
        admin_email: profile?.email,
      }
    );
  };

  // 개별 로그 삭제 함수
  const handleDeleteLog = async (logId: string) => {
    try {
      console.log("Deleting log:", logId);

      const { error } = await supabase
        .from("system_logs")
        .delete()
        .eq("id", logId);

      if (error) {
        console.error("Error deleting log:", error);
        toast({
          title: "삭제 실패",
          description: "로그 삭제 중 오류가 발생했습니다.",
          variant: "destructive",
        });
        return;
      }

      // 로그 목록에서 제거
      setLogs((prevLogs) => prevLogs.filter((log) => log.id !== logId));

      toast({
        title: "삭제 완료",
        description: "시스템 로그가 삭제되었습니다.",
      });

      // 개별 삭제 로그 생성
      const logToDelete = logs.find((log) => log.id === logId);
      await createSystemLog(
        "LOG_DELETE_INDIVIDUAL",
        `관리자가 시스템 로그를 개별 삭제했습니다`,
        "info",
        profile?.id,
        "system",
        undefined,
        {
          action: "delete_individual_log",
          deleted_log_id: logId,
          deleted_log_action: logToDelete?.action || "unknown",
          deleted_log_level: logToDelete?.level || "unknown",
          deleted_log_created_at: logToDelete?.created_at,
          admin_name: profile?.name,
          admin_email: profile?.email,
          deleted_at: new Date().toISOString(),
        }
      );
    } catch (error) {
      console.error("Error in handleDeleteLog:", error);
      toast({
        title: "삭제 실패",
        description: "로그 삭제 중 예상치 못한 오류가 발생했습니다.",
        variant: "destructive",
      });
    }
  };

  // 대량 로그 삭제 함수 (30일 이전 로그 삭제)
  const handleBulkDeleteOldLogs = async () => {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const cutoffDate = thirtyDaysAgo.toISOString();

      console.log("Deleting logs older than:", cutoffDate);

      const { data: logsToDelete, error: selectError } = await supabase
        .from("system_logs")
        .select("id")
        .lt("created_at", cutoffDate);

      if (selectError) {
        console.error("Error selecting old logs:", selectError);
        toast({
          title: "삭제 실패",
          description: "오래된 로그 조회 중 오류가 발생했습니다.",
          variant: "destructive",
        });
        return;
      }

      const deleteCount = logsToDelete?.length || 0;

      if (deleteCount === 0) {
        toast({
          title: "삭제할 로그 없음",
          description: "30일 이전의 로그가 없습니다.",
        });
        return;
      }

      const { error: deleteError } = await supabase
        .from("system_logs")
        .delete()
        .lt("created_at", cutoffDate);

      if (deleteError) {
        console.error("Error deleting old logs:", deleteError);
        toast({
          title: "삭제 실패",
          description: "오래된 로그 삭제 중 오류가 발생했습니다.",
          variant: "destructive",
        });
        return;
      }

      // 로그 목록에서 제거
      setLogs((prevLogs) =>
        prevLogs.filter((log) => new Date(log.created_at) >= thirtyDaysAgo)
      );

      toast({
        title: "대량 삭제 완료",
        description: `30일 이전의 로그 ${deleteCount}개가 삭제되었습니다.`,
      });

      // 30일 이전 로그 삭제 요약 로그 생성
      await createSystemLog(
        "BULK_LOG_DELETE_OLD",
        `30일 이전 시스템 로그 ${deleteCount}개가 자동 정리되었습니다`,
        "info",
        profile?.id,
        "system",
        undefined,
        {
          action: "delete_old_logs",
          cutoff_date: cutoffDate,
          deleted_count: deleteCount,
          retention_days: 30,
          admin_name: profile?.name,
          admin_email: profile?.email,
          cleanup_timestamp: new Date().toISOString(),
        }
      );
    } catch (error) {
      console.error("Error in handleBulkDeleteOldLogs:", error);
      toast({
        title: "삭제 실패",
        description: "대량 삭제 중 예상치 못한 오류가 발생했습니다.",
        variant: "destructive",
      });
    }
  };

  // 전체 로그 삭제 함수
  const handleDeleteAllLogs = async () => {
    try {
      console.log("Deleting all logs");

      // 현재 로그 개수 확인
      const currentLogCount = logs.length;

      if (currentLogCount === 0) {
        toast({
          title: "삭제할 로그 없음",
          description: "삭제할 로그가 없습니다.",
        });
        return;
      }

      // 현재 사용자 세션 보호를 위해 최근 1시간 로그는 제외하고 삭제
      const oneHourAgo = new Date();
      oneHourAgo.setHours(oneHourAgo.getHours() - 1);
      const cutoffTime = oneHourAgo.toISOString();

      console.log("Deleting logs older than:", cutoffTime);

      const { error } = await supabase
        .from("system_logs")
        .delete()
        .lt("created_at", cutoffTime); // 1시간 이전 로그만 삭제

      if (error) {
        console.error("Error deleting all logs:", error);
        toast({
          title: "삭제 실패",
          description: "전체 로그 삭제 중 오류가 발생했습니다.",
          variant: "destructive",
        });
        return;
      }

      // 로그 목록에서 삭제된 로그 제거 (1시간 이전 로그만)
      const updatedLogs = logs.filter(
        (log) => new Date(log.created_at) >= oneHourAgo
      );
      setLogs(updatedLogs);

      const deletedCount = currentLogCount - updatedLogs.length;

      toast({
        title: "전체 삭제 완료",
        description: `${deletedCount}개의 시스템 로그가 삭제되었습니다. (최근 1시간 로그는 보존됨)`,
      });

      // 삭제 완료 로그 생성 (무한 로그 생성 방지를 위해 비활성화)
      // await createSystemLog(
      //   "BULK_LOG_DELETE",
      //   `관리자가 전체 로그 삭제를 수행했습니다 (${deletedCount}개 삭제)`,
      //   "info",
      //   profile?.id,
      //   "system",
      //   undefined,
      //   {
      //     deleted_count: deletedCount,
      //     preserved_count: updatedLogs.length,
      //     cutoff_time: cutoffTime,
      //     admin_name: profile?.name,
      //     admin_email: profile?.email,
      //   }
      // );
    } catch (error) {
      console.error("Error in handleDeleteAllLogs:", error);
      toast({
        title: "삭제 실패",
        description: "전체 로그 삭제 중 예상치 못한 오류가 발생했습니다.",
        variant: "destructive",
      });
    }
  };

  // 진짜 전체 로그 삭제 함수
  const handleTrueDeleteAllLogs = async () => {
    try {
      console.log("=== 전체 로그 삭제 시작 ===");

      // 삭제 전 데이터베이스 상태 확인
      const { count: beforeCount } = await supabase
        .from("system_logs")
        .select("*", { count: "exact", head: true });

      console.log(`삭제 전 데이터베이스 로그 개수: ${beforeCount}`);

      // 현재 로그 개수 확인
      const currentLogCount = logs.length;
      console.log(`현재 화면 로그 개수: ${currentLogCount}`);

      if (currentLogCount === 0) {
        toast({
          title: "삭제할 로그 없음",
          description: "삭제할 로그가 없습니다.",
        });
        return;
      }

      // 모든 로그 삭제 (예외 없음) - 여러 방법 시도
      console.log("Attempting to delete all logs from database...");

      // 방법 1: 날짜 조건으로 삭제
      let { error, count: deletedCount } = await supabase
        .from("system_logs")
        .delete({ count: "exact" })
        .gte("created_at", "1900-01-01");

      // 방법 1이 실패하면 방법 2 시도
      if (error) {
        console.log("Method 1 failed, trying method 2...");
        const result = await supabase
          .from("system_logs")
          .delete({ count: "exact" })
          .neq("id", "00000000-0000-0000-0000-000000000000");
        error = result.error;
        deletedCount = result.count;
      }

      // 방법 2도 실패하면 방법 3 시도 (조건 없이 삭제)
      if (error) {
        console.log("Method 2 failed, trying method 3...");
        const result = await supabase
          .from("system_logs")
          .delete({ count: "exact" })
          .not("id", "is", null); // id가 null이 아닌 모든 레코드 (즉, 모든 레코드)
        error = result.error;
        deletedCount = result.count;
      }

      if (error) {
        console.error("All deletion methods failed:", error);
        toast({
          title: "삭제 실패",
          description: `전체 로그 삭제 중 오류가 발생했습니다: ${error.message}`,
          variant: "destructive",
        });
        return;
      }

      console.log(`Successfully deleted ${deletedCount} logs from database`);

      // 삭제 후 데이터베이스 상태 확인
      const { count: afterCount } = await supabase
        .from("system_logs")
        .select("*", { count: "exact", head: true });

      console.log(`삭제 후 데이터베이스 로그 개수: ${afterCount}`);

      // 로그 목록 완전 초기화
      setLogs([]);
      setTotalLogs(0);

      // 통계도 업데이트
      await loadStats();

      toast({
        title: "전체 삭제 완료",
        description: `데이터베이스에서 ${
          deletedCount || beforeCount
        }개의 시스템 로그가 완전히 삭제되었습니다. (남은 로그: ${afterCount}개)`,
      });

      // 전체 삭제 로그 생성
      await createSystemLog(
        "BULK_LOG_DELETE_ALL",
        `관리자가 모든 시스템 로그를 완전히 삭제했습니다 (${
          deletedCount || beforeCount
        }개 삭제)`,
        "info",
        profile?.id,
        "system",
        undefined,
        {
          action: "delete_all_logs",
          deleted_count: deletedCount || beforeCount,
          remaining_count: afterCount,
          admin_name: profile?.name,
          admin_email: profile?.email,
          timestamp: new Date().toISOString(),
        }
      );

      console.log("=== 전체 로그 삭제 완료 ===");

      // 삭제 후 데이터 확인을 위해 로그 다시 로딩
      setTimeout(async () => {
        await loadLogs();
        console.log("Logs reloaded after deletion");
      }, 1000);
    } catch (error) {
      console.error("Error in handleTrueDeleteAllLogs:", error);
      toast({
        title: "삭제 실패",
        description: "전체 로그 삭제 중 예상치 못한 오류가 발생했습니다.",
        variant: "destructive",
      });
    }
  };

  // 페이지 변경 함수
  const handlePageChange = (newPage: number) => {
    setLogPage(newPage);
    // 페이지 변경 시 로그는 useEffect에서 자동으로 다시 로딩됨
  };

  // 로그 필터 변경 로그 함수
  const handleLogFilterChange = async (newFilter: string) => {
    setLogFilter(newFilter);
    if (newFilter !== "all") {
      await createSystemLog(
        "LOG_FILTER_APPLIED",
        `관리자가 로그 필터를 적용했습니다: ${
          newFilter === "info"
            ? "정보 로그"
            : newFilter === "warning"
            ? "경고 로그"
            : newFilter === "error"
            ? "오류 로그"
            : newFilter
        }`,
        "info",
        profile?.id,
        "system",
        undefined,
        {
          filter_type: newFilter,
          admin_name: profile?.name,
          admin_email: profile?.email,
        }
      );
    }
  };

  // 농장 관련 액션 로그 함수들
  const logFarmAction = async (
    action: string,
    farmName: string,
    farmId?: string,
    details?: string
  ) => {
    const actionDetails =
      details || `농장 "${farmName}"에 대한 ${action} 작업이 수행되었습니다`;
    await createSystemLog(
      action,
      actionDetails,
      "info",
      profile?.id,
      "farm",
      farmId,
      {
        farm_name: farmName,
        admin_name: profile?.name,
        admin_email: profile?.email,
      }
    );
  };

  // 사용자 관련 액션 로그 함수들
  const logUserAction = async (
    action: string,
    userName: string,
    userId?: string,
    details?: string
  ) => {
    const actionDetails =
      details || `사용자 "${userName}"에 대한 ${action} 작업이 수행되었습니다`;
    await createSystemLog(
      action,
      actionDetails,
      "info",
      profile?.id,
      "user",
      userId,
      {
        target_user_name: userName,
        admin_name: profile?.name,
        admin_email: profile?.email,
      }
    );
  };

  // 오류 로그 자동화 함수
  const logError = async (action: string, error: any, context?: string) => {
    const errorMessage =
      error?.message || error?.toString() || "알 수 없는 오류";
    const details = context
      ? `${context}에서 오류 발생: ${errorMessage}`
      : `${action} 중 오류 발생: ${errorMessage}`;

    await createSystemLog(
      action,
      details,
      "error",
      profile?.id,
      "system",
      undefined,
      {
        error_message: errorMessage,
        context: context,
        admin_name: profile?.name,
        admin_email: profile?.email,
      }
    );
  };

  // 인증 관련 로그 함수들
  const logAuthAction = async (
    action:
      | "LOGIN"
      | "LOGOUT"
      | "LOGIN_FAILED"
      | "UNAUTHORIZED_ACCESS"
      | "SESSION_EXPIRED",
    userInfo?: string,
    details?: string
  ) => {
    const actionMessages = {
      LOGIN: `사용자 "${userInfo || "알 수 없음"}"가 로그인했습니다`,
      LOGOUT: `사용자 "${userInfo || "알 수 없음"}"가 로그아웃했습니다`,
      LOGIN_FAILED: `로그인 실패: ${details || "알 수 없는 이유"}`,
      UNAUTHORIZED_ACCESS: `권한 없는 접근 시도: ${
        details || "알 수 없는 경로"
      }`,
      SESSION_EXPIRED: `세션 만료: ${userInfo || "알 수 없는 사용자"}`,
    };

    const level =
      action === "LOGIN_FAILED" || action === "UNAUTHORIZED_ACCESS"
        ? "warn"
        : "info";

    await createSystemLog(
      `USER_${action}`,
      actionMessages[action],
      level,
      undefined, // 인증 관련 액션은 특정 사용자 ID가 없을 수 있음
      "user",
      undefined,
      {
        action_type: action,
        user_info: userInfo,
        details: details,
        timestamp: new Date().toISOString(),
      }
    );
  };

  // 기간 유효성 검사 함수
  const validateDateRange = () => {
    if (!csvStartDate || !csvEndDate) return true;

    const start = new Date(csvStartDate);
    const end = new Date(csvEndDate);
    const diffYears =
      (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 365);

    if (diffYears > 5) {
      toast({
        title: "기간 선택 오류",
        description: "기간 선택은 최대 5년까지 가능합니다.",
        variant: "destructive",
      });
      return false;
    }

    if (start > end) {
      toast({
        title: "기간 선택 오류",
        description: "시작일이 종료일보다 늦을 수 없습니다.",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const handleExportUsers = async () => {
    try {
      let dataToDownload = [...filteredUsers];

      // 역할별 필터 적용
      if (csvUserRole !== "all") {
        dataToDownload = dataToDownload.filter(
          (user) => user.account_type === csvUserRole
        );
      }

      // 활동 상태별 필터 적용
      if (csvUserActivity !== "all") {
        const now = new Date();
        const thirtyDaysAgo = new Date(
          now.getTime() - 30 * 24 * 60 * 60 * 1000
        );
        const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

        dataToDownload = dataToDownload.filter((user) => {
          const lastLogin = user.last_login_at
            ? new Date(user.last_login_at)
            : null;
          const createdAt = new Date(user.created_at);

          switch (csvUserActivity) {
            case "active":
              return lastLogin && lastLogin > thirtyDaysAgo;
            case "inactive":
              return !lastLogin || lastLogin <= thirtyDaysAgo;
            case "new":
              return createdAt > sevenDaysAgo;
            default:
              return true;
          }
        });
      }

      // 헤더 구성
      const headers = [];
      if (csvIncludeBasic) {
        headers.push("이름", "이메일", "역할");
      }
      if (csvIncludeActivity) {
        headers.push("마지막 로그인", "가입일");
      }
      if (csvIncludeContact) {
        headers.push("전화번호");
      }
      if (csvIncludeFarm) {
        headers.push("소유 농장 수", "관리 농장 수");
      }

      // 데이터 구성
      const csvData = dataToDownload.map((user) => {
        const row = [];

        if (csvIncludeBasic) {
          row.push(
            user.name || "이름 없음",
            user.email,
            user.account_type === "admin" ? "시스템 관리자" : "일반 사용자"
          );
        }

        if (csvIncludeActivity) {
          row.push(
            user.last_login_at
              ? formatDateTime(user.last_login_at)
              : "로그인 기록 없음",
            formatDateTime(user.created_at)
          );
        }

        if (csvIncludeContact) {
          row.push(user.phone || "전화번호 없음");
        }

        if (csvIncludeFarm) {
          // 실제로는 farms 테이블에서 조회해야 하지만, 현재는 예시 데이터
          const ownedFarms = farms.filter((f) => f.owner_id === user.id).length;
          const managedFarms = 0; // 농장 관리자 테이블이 있다면 조회
          row.push(ownedFarms.toString(), managedFarms.toString());
        }

        return row;
      });

      const csvContent = [headers, ...csvData]
        .map((row) => row.map((field) => `"${field}"`).join(","))
        .join("\n");

      const BOM = "\uFEFF";
      const csvWithBOM = BOM + csvContent;
      const blob = new Blob([csvWithBOM], { type: "text/csv;charset=utf-8;" });

      const roleText =
        csvUserRole === "all"
          ? "전체"
          : csvUserRole === "admin"
          ? "관리자"
          : csvUserRole === "farm_owner"
          ? "농장소유자"
          : csvUserRole === "farm_manager"
          ? "농장관리자"
          : "일반사용자";

      const activityText =
        csvUserActivity === "all"
          ? "전체"
          : csvUserActivity === "active"
          ? "활성"
          : csvUserActivity === "inactive"
          ? "비활성"
          : "신규";

      const fileName = `사용자_목록_${roleText}_${activityText}_${
        new Date().toISOString().split("T")[0]
      }.csv`;

      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", fileName);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      // 시스템 로그 생성
      await createSystemLog(
        "USER_CSV_EXPORT",
        `사용자 데이터 CSV 내보내기 (${dataToDownload.length}명)`,
        "info",
        profile?.id,
        "user",
        undefined,
        {
          export_count: dataToDownload.length,
          role_filter: csvUserRole,
          activity_filter: csvUserActivity,
          include_basic: csvIncludeBasic,
          include_activity: csvIncludeActivity,
          include_contact: csvIncludeContact,
          include_farm: csvIncludeFarm,
          admin_name: profile?.name,
          admin_email: profile?.email,
        }
      );

      toast({
        title: "내보내기 완료",
        description: `${dataToDownload.length}명의 사용자 데이터를 내보냈습니다.`,
      });
    } catch (error) {
      console.error("사용자 CSV 내보내기 오류:", error);
      await logError(
        "USER_CSV_EXPORT_ERROR",
        error,
        "사용자 데이터 CSV 내보내기"
      );
      toast({
        title: "내보내기 실패",
        description: "사용자 데이터 내보내기 중 오류가 발생했습니다.",
        variant: "destructive",
      });
    }
  };

  const handleExportFarms = async () => {
    if (!validateDateRange()) return;

    try {
      let dataToDownload = [...filteredFarms];

      // 기간 필터 적용 (등록일 기준)
      if (csvStartDate || csvEndDate) {
        dataToDownload = dataToDownload.filter((farm) => {
          const farmDate = new Date(farm.created_at)
            .toISOString()
            .split("T")[0];
          const afterStart = !csvStartDate || farmDate >= csvStartDate;
          const beforeEnd = !csvEndDate || farmDate <= csvEndDate;
          return afterStart && beforeEnd;
        });
      }

      // 헤더 구성
      const headers = [];
      if (csvFarmBasic) {
        headers.push("농장명", "설명", "상태");
      }
      if (csvFarmLocation) {
        headers.push("주소", "지역", "우편번호");
      }
      if (csvFarmOwner) {
        headers.push("소유자명", "소유자 연락처", "소유자 이메일");
      }
      if (csvFarmStats) {
        headers.push("등록일", "업데이트일", "방문자 수");
      }

      // 데이터 구성
      const csvData = dataToDownload.map((farm) => {
        const row = [];

        if (csvFarmBasic) {
          row.push(
            farm.farm_name || "농장명 없음",
            farm.description || "설명 없음",
            farm.is_active ? "활성" : "비활성"
          );
        }

        if (csvFarmLocation) {
          const addressParts = farm.farm_address
            ? farm.farm_address.split(" ")
            : [];
          row.push(
            farm.farm_address || "주소 없음",
            addressParts[0] || "지역 정보 없음",
            farm.farm_type || "농장 유형 없음"
          );
        }

        if (csvFarmOwner) {
          row.push(
            farm.owner_name || "소유자 정보 없음",
            farm.owner_phone || "연락처 없음",
            farm.owner_email || "이메일 없음"
          );
        }

        if (csvFarmStats) {
          row.push(
            formatDateTime(farm.created_at),
            farm.updated_at ? formatDateTime(farm.updated_at) : "업데이트 없음",
            "0" // 실제로는 방문자 테이블에서 조회해야 함
          );
        }

        return row;
      });

      const csvContent = [headers, ...csvData]
        .map((row) => row.map((field) => `"${field}"`).join(","))
        .join("\n");

      const BOM = "\uFEFF";
      const csvWithBOM = BOM + csvContent;
      const blob = new Blob([csvWithBOM], { type: "text/csv;charset=utf-8;" });

      const filterText =
        farmFilter === "all"
          ? "전체"
          : farmFilter === "recent"
          ? "최근30일"
          : farmFilter === "has_owner"
          ? "소유자있음"
          : "주소있음";

      const fileName = `농장_목록_${filterText}_${csvStartDate || "전체"}_${
        csvEndDate || "전체"
      }_${new Date().toISOString().split("T")[0]}.csv`;

      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", fileName);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      // 시스템 로그 생성
      await createSystemLog(
        "FARM_CSV_EXPORT",
        `농장 데이터 CSV 내보내기 (${dataToDownload.length}개)`,
        "info",
        profile?.id,
        "farm",
        undefined,
        {
          export_count: dataToDownload.length,
          farm_filter: farmFilter,
          start_date: csvStartDate,
          end_date: csvEndDate,
          include_basic: csvFarmBasic,
          include_location: csvFarmLocation,
          include_owner: csvFarmOwner,
          include_stats: csvFarmStats,
          admin_name: profile?.name,
          admin_email: profile?.email,
        }
      );

      toast({
        title: "내보내기 완료",
        description: `${dataToDownload.length}개의 농장 데이터를 내보냈습니다.`,
      });
    } catch (error) {
      console.error("농장 CSV 내보내기 오류:", error);
      await logError(
        "FARM_CSV_EXPORT_ERROR",
        error,
        "농장 데이터 CSV 내보내기"
      );
      toast({
        title: "내보내기 실패",
        description: "농장 데이터 내보내기 중 오류가 발생했습니다.",
        variant: "destructive",
      });
    }
  };

  const handleExportLogs = async () => {
    if (!validateDateRange()) return;

    try {
      let dataToDownload = [...filteredLogs];

      // 기간 필터 적용 (생성일 기준)
      if (csvStartDate || csvEndDate) {
        dataToDownload = dataToDownload.filter((log) => {
          const logDate = new Date(log.created_at).toISOString().split("T")[0];
          const afterStart = !csvStartDate || logDate >= csvStartDate;
          const beforeEnd = !csvEndDate || logDate <= csvEndDate;
          return afterStart && beforeEnd;
        });
      }

      // 헤더 구성
      const headers = [];
      if (csvLogBasic) {
        headers.push("액션", "레벨", "시간");
      }
      if (csvLogDetails) {
        headers.push("상세내용", "설명");
      }
      if (csvLogUser) {
        headers.push("사용자명", "IP주소");
      }
      if (csvLogSystem) {
        headers.push("브라우저", "디바이스");
      }

      // 데이터 구성
      const csvData = dataToDownload.map((log) => {
        const row = [];

        if (csvLogBasic) {
          row.push(
            log.action,
            log.level === "info"
              ? "정보"
              : log.level === "warning"
              ? "경고"
              : log.level === "error"
              ? "오류"
              : "알 수 없음",
            formatDateTime(log.created_at)
          );
        }

        if (csvLogDetails) {
          row.push(log.details, log.action + " 작업이 수행되었습니다");
        }

        if (csvLogUser) {
          row.push(log.user_name, log.ip_address);
        }

        if (csvLogSystem) {
          // User Agent에서 브라우저와 디바이스 정보 추출 (간단한 예시)
          const browser = log.user_agent.includes("Chrome")
            ? "Chrome"
            : log.user_agent.includes("Firefox")
            ? "Firefox"
            : log.user_agent.includes("Safari")
            ? "Safari"
            : "Unknown";
          const device = log.user_agent.includes("Mobile")
            ? "Mobile"
            : "Desktop";
          row.push(browser, device);
        }

        return row;
      });

      const csvContent = [headers, ...csvData]
        .map((row) => row.map((field) => `"${field}"`).join(","))
        .join("\n");

      const BOM = "\uFEFF";
      const csvWithBOM = BOM + csvContent;
      const blob = new Blob([csvWithBOM], { type: "text/csv;charset=utf-8;" });

      const filterText =
        logFilter === "all"
          ? "전체"
          : logFilter === "info"
          ? "정보"
          : logFilter === "warning"
          ? "경고"
          : "오류";

      const fileName = `시스템_로그_${filterText}_${csvStartDate || "전체"}_${
        csvEndDate || "전체"
      }_${new Date().toISOString().split("T")[0]}.csv`;

      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", fileName);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      // 시스템 로그 생성
      await createSystemLog(
        "LOG_CSV_EXPORT",
        `시스템 로그 CSV 내보내기 (${dataToDownload.length}개)`,
        "info",
        profile?.id,
        "system_log",
        undefined,
        {
          export_count: dataToDownload.length,
          log_filter: logFilter,
          start_date: csvStartDate,
          end_date: csvEndDate,
          include_basic: csvLogBasic,
          include_details: csvLogDetails,
          include_user: csvLogUser,
          include_system: csvLogSystem,
          admin_name: profile?.name,
          admin_email: profile?.email,
        }
      );

      toast({
        title: "내보내기 완료",
        description: `${dataToDownload.length}개의 로그 데이터를 내보냈습니다.`,
      });
    } catch (error) {
      console.error("로그 CSV 내보내기 오류:", error);
      await logError("LOG_CSV_EXPORT_ERROR", error, "시스템 로그 CSV 내보내기");
      toast({
        title: "내보내기 실패",
        description: "로그 데이터 내보내기 중 오류가 발생했습니다.",
        variant: "destructive",
      });
    }
  };

  const getRoleColor = (accountType: string | undefined) => {
    switch (accountType) {
      case "admin":
        return "bg-red-500 text-white";
      case "user":
        return "bg-blue-500 text-white";
      default:
        return "bg-gray-400 text-white";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "text-green-600 border-green-600";
      case "inactive":
        return "text-red-600 border-red-600";
      case "pending":
        return "text-yellow-600 border-yellow-600";
      default:
        return "text-gray-600 border-gray-600";
    }
  };

  // 사용자 필터링
  const filteredUsers = users.filter((user) => {
    // 검색어 필터링 (검색어가 없으면 모든 사용자 포함)
    const matchesSearch =
      !searchTerm ||
      user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email?.toLowerCase().includes(searchTerm.toLowerCase());

    // 사용자 필터 적용
    let matchesFilter = true;
    if (userFilter === "active") {
      // 활성 사용자: 로그인 기록이 있는 사용자
      matchesFilter = !!user.last_login_at;
    } else if (userFilter === "inactive") {
      // 비활성 사용자: 로그인 기록이 없는 사용자
      matchesFilter = !user.last_login_at;
    } else if (userFilter === "admin") {
      matchesFilter = user.account_type === "admin";
    } else if (userFilter === "owner") {
      // 농장 소유자 확인 (farms 테이블에서 owner_id로 확인)
      const farmOwnerIds = new Set(farms.map((f) => f.owner_id));
      matchesFilter = farmOwnerIds.has(user.id);
    } else if (userFilter === "manager") {
      // 현재는 farm_members 테이블 연동이 없으므로 false
      matchesFilter = false;
    } else if (userFilter === "viewer") {
      // 현재는 farm_members 테이블 연동이 없으므로 false
      matchesFilter = false;
    } else if (userFilter === "user") {
      matchesFilter = user.account_type === "user";
    }
    // userFilter === "all"인 경우 matchesFilter는 true 유지

    return matchesSearch && matchesFilter;
  });

  console.log("Users:", users.length);
  console.log("Filtered users:", filteredUsers.length);
  console.log("User filter:", userFilter);
  console.log("Search term:", searchTerm);

  // 농장 필터링
  const filteredFarms = farms.filter((farm) => {
    // 검색어 필터링
    const matchesSearch =
      !searchTerm ||
      farm.farm_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      farm.owner_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      farm.farm_address?.toLowerCase().includes(searchTerm.toLowerCase());

    // 농장 필터 적용
    let matchesFilter = true;
    if (farmFilter === "recent") {
      const farmDate = new Date(farm.created_at);
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      matchesFilter = farmDate > thirtyDaysAgo;
    } else if (farmFilter === "active") {
      matchesFilter = !!farm.is_active;
    } else if (farmFilter === "inactive") {
      matchesFilter = !farm.is_active;
    } else if (farmFilter.startsWith("type_")) {
      const farmType = farmFilter.replace("type_", "");
      matchesFilter = farm.farm_type === farmType;
    }

    return matchesSearch && matchesFilter;
  });

  console.log("Farms:", farms.length);
  console.log("Filtered farms:", filteredFarms.length);
  console.log("Search term:", searchTerm);
  console.log("Farm filter:", farmFilter);

  // 로그 필터링
  const filteredLogs = logs.filter((log) => {
    const matchesSearch =
      log.user_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.action?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.details?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = logFilter === "all" || log.level === logFilter;

    return matchesSearch && matchesFilter;
  });

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case "info":
        return "text-blue-600 border-blue-600";
      case "warning":
        return "text-yellow-600 border-yellow-600";
      case "error":
        return "text-red-600 border-red-600";
      default:
        return "text-gray-600 border-gray-600";
    }
  };

  const getLogLevelIcon = (level: string) => {
    switch (level) {
      case "info":
        return <Activity className="h-4 w-4 text-blue-500" />;
      case "warning":
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-6 pt-2 md:pt-4">
      {/* 브레드크럼 */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/admin/dashboard" className="flex items-center gap-1">
                <Home className="h-4 w-4" />
                대시보드
              </Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>시스템 관리</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <SidebarTrigger />
          <div>
            <h1 className="text-3xl font-bold tracking-tight mb-4 flex items-center gap-2">
              <BarChart3 className="h-8 w-8 text-primary" />
              시스템 관리
            </h1>
            <p className="text-muted-foreground mt-2">
              사용자, 농장, 시스템 로그 등을 관리합니다
            </p>
          </div>
        </div>
      </div>

      <div className="space-y-6">
        <Tabs defaultValue="users" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="users" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              사용자 관리
            </TabsTrigger>
            <TabsTrigger value="farms" className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              농장 관리
            </TabsTrigger>
            <TabsTrigger value="logs" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              시스템 로그
            </TabsTrigger>
            <TabsTrigger value="dashboard" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              통계 대시보드
            </TabsTrigger>
          </TabsList>

          {/* 사용자 관리 탭 */}
          <TabsContent value="users" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2 mb-4">
                        <Users className="h-5 w-5" />
                        사용자 관리
                      </CardTitle>
                      <CardDescription className="mt-2">
                        시스템에 등록된 모든 사용자를 관리합니다
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4 mr-2" />
                            CSV 다운로드
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-lg">
                          <DialogHeader>
                            <DialogTitle>사용자 CSV 다운로드</DialogTitle>
                            <DialogDescription>
                              다운로드할 사용자 데이터의 조건과 포함할 정보를
                              선택하세요.
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-6">
                            {/* 사용자 역할 선택 */}
                            <div>
                              <Label className="text-sm font-medium mb-2 block">
                                사용자 역할
                              </Label>
                              <Select
                                value={csvUserRole}
                                onValueChange={setCsvUserRole}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="역할 선택" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="all">
                                    전체 사용자
                                  </SelectItem>
                                  <SelectItem value="admin">
                                    시스템 관리자만
                                  </SelectItem>
                                  <SelectItem value="owner">
                                    농장 소유자만
                                  </SelectItem>
                                  <SelectItem value="user">
                                    일반 사용자만
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            {/* 활동 상태 선택 */}
                            <div>
                              <Label className="text-sm font-medium mb-2 block">
                                활동 상태
                              </Label>
                              <Select
                                value={csvUserActivity}
                                onValueChange={setCsvUserActivity}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="활동 상태 선택" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="all">
                                    전체 사용자
                                  </SelectItem>
                                  <SelectItem value="active">
                                    활성 사용자 (30일 내 로그인)
                                  </SelectItem>
                                  <SelectItem value="inactive">
                                    비활성 사용자 (30일 이상 미로그인)
                                  </SelectItem>
                                  <SelectItem value="new">
                                    신규 사용자 (7일 내 가입)
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            {/* 포함할 정보 선택 */}
                            <div>
                              <Label className="text-sm font-medium mb-3 block">
                                포함할 정보
                              </Label>
                              <div className="space-y-3">
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    id="basic"
                                    checked={csvIncludeBasic}
                                    onCheckedChange={(checked) =>
                                      setCsvIncludeBasic(!!checked)
                                    }
                                  />
                                  <Label htmlFor="basic" className="text-sm">
                                    기본 정보 (이름, 이메일, 역할)
                                  </Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    id="activity"
                                    checked={csvIncludeActivity}
                                    onCheckedChange={(checked) =>
                                      setCsvIncludeActivity(!!checked)
                                    }
                                  />
                                  <Label htmlFor="activity" className="text-sm">
                                    활동 정보 (마지막 로그인, 가입일)
                                  </Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    id="contact"
                                    checked={csvIncludeContact}
                                    onCheckedChange={(checked) =>
                                      setCsvIncludeContact(!!checked)
                                    }
                                  />
                                  <Label htmlFor="contact" className="text-sm">
                                    연락처 정보 (전화번호)
                                  </Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    id="farm"
                                    checked={csvIncludeFarm}
                                    onCheckedChange={(checked) =>
                                      setCsvIncludeFarm(!!checked)
                                    }
                                  />
                                  <Label htmlFor="farm" className="text-sm">
                                    농장 정보 (소유/관리 농장 수)
                                  </Label>
                                </div>
                              </div>
                            </div>

                            {/* 다운로드 미리보기 */}
                            <div className="p-3 bg-muted rounded-lg">
                              <h4 className="text-sm font-medium mb-2">
                                다운로드 미리보기
                              </h4>
                              <div className="text-xs text-muted-foreground space-y-1">
                                <div>
                                  • 역할:{" "}
                                  {csvUserRole === "all"
                                    ? "전체 사용자"
                                    : csvUserRole === "admin"
                                    ? "시스템 관리자"
                                    : csvUserRole === "owner"
                                    ? "농장 소유자"
                                    : "일반 사용자"}
                                </div>
                                <div>
                                  • 활동:{" "}
                                  {csvUserActivity === "all"
                                    ? "전체 사용자"
                                    : csvUserActivity === "active"
                                    ? "활성 사용자"
                                    : csvUserActivity === "inactive"
                                    ? "비활성 사용자"
                                    : "신규 사용자"}
                                </div>
                                <div>
                                  • 예상 사용자 수: {filteredUsers.length}명
                                </div>
                                <div>
                                  • 포함 정보:{" "}
                                  {[
                                    csvIncludeBasic && "기본정보",
                                    csvIncludeActivity && "활동정보",
                                    csvIncludeContact && "연락처",
                                    csvIncludeFarm && "농장정보",
                                  ]
                                    .filter(Boolean)
                                    .join(", ") || "없음"}
                                </div>
                              </div>
                            </div>
                          </div>
                          <DialogFooter>
                            <Button
                              variant="outline"
                              onClick={() => {
                                setCsvUserRole("all");
                                setCsvUserActivity("all");
                                setCsvIncludeBasic(true);
                                setCsvIncludeActivity(true);
                                setCsvIncludeContact(false);
                                setCsvIncludeFarm(false);
                              }}
                            >
                              초기화
                            </Button>
                            <Button
                              onClick={handleExportUsers}
                              disabled={
                                !csvIncludeBasic &&
                                !csvIncludeActivity &&
                                !csvIncludeContact &&
                                !csvIncludeFarm
                              }
                            >
                              <Download className="h-4 w-4 mr-2" />
                              다운로드
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleRefresh}
                        disabled={loading}
                      >
                        <RefreshCw
                          className={`h-4 w-4 mr-2 ${
                            loading ? "animate-spin" : ""
                          }`}
                        />
                        새로고침
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* 검색 */}
                  <div className="flex items-center gap-4">
                    <div className="flex-1">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="사용자 검색..."
                          value={searchTerm}
                          onChange={(e) => {
                            const value = e.target.value;
                            handleSearch(value, "user");
                          }}
                          className="pl-10"
                        />
                      </div>
                    </div>
                    <Select
                      value={userFilter}
                      onValueChange={handleUserFilterChange}
                    >
                      <SelectTrigger className="w-40">
                        <Filter className="h-4 w-4 mr-2" />
                        <SelectValue placeholder="필터 선택" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">
                          전체 사용자 ({getUserCountByFilter("all")}명)
                        </SelectItem>
                        <SelectItem value="active">
                          활성 사용자 ({getUserCountByFilter("active")}명)
                        </SelectItem>
                        <SelectItem value="inactive">
                          비활성 사용자 ({getUserCountByFilter("inactive")}명)
                        </SelectItem>
                        <SelectItem value="admin">
                          시스템 관리자 ({getUserCountByFilter("admin")}명)
                        </SelectItem>
                        <SelectItem value="owner">
                          농장 소유자 ({getUserCountByFilter("owner")}명)
                        </SelectItem>
                        <SelectItem value="user">
                          일반 사용자 ({getUserCountByFilter("user")}명)
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 통계 카드 */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                          <UserCheck className="h-4 w-4 text-green-500" />
                          <div>
                            <p className="text-sm font-medium">전체 사용자</p>
                            <p className="text-2xl font-bold">
                              {stats.totalUsers}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                          <Activity className="h-4 w-4 text-blue-500" />
                          <div>
                            <p className="text-sm font-medium">활성 사용자</p>
                            <p className="text-2xl font-bold">
                              {stats.activeUsers}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                          <Building2 className="h-4 w-4 text-purple-500" />
                          <div>
                            <p className="text-sm font-medium">농장 소유자</p>
                            <p className="text-2xl font-bold">
                              {stats.farmOwners}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-orange-500" />
                          <div>
                            <p className="text-sm font-medium">오늘 접속</p>
                            <p className="text-2xl font-bold">
                              {stats.todayLogins}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* 사용자 목록 */}
                  <div className="border rounded-lg">
                    <div className="p-4 border-b bg-muted/50">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium">
                          사용자 목록 ({filteredUsers.length}명)
                        </h3>
                        {userFilter !== "all" && (
                          <div className="flex items-center gap-2">
                            <Badge variant="secondary" className="text-xs">
                              {userFilter === "active" && "활성 사용자"}
                              {userFilter === "inactive" && "비활성 사용자"}
                              {userFilter === "admin" && "시스템 관리자"}
                              {userFilter === "owner" && "농장 소유자"}
                              {userFilter === "user" && "일반 사용자"}
                            </Badge>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={async () => {
                                setUserFilter("all");
                                await createSystemLog(
                                  "USER_FILTER_CLEARED",
                                  "관리자가 사용자 필터를 해제했습니다",
                                  "info"
                                );
                              }}
                              className="h-6 px-2 text-xs"
                            >
                              필터 해제
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="p-4">
                      <div className="space-y-4">
                        {filteredUsers.length > 0 ? (
                          filteredUsers.map((user) => (
                            <div
                              key={user.id}
                              className="flex items-center justify-between p-4 border rounded-lg"
                            >
                              <div className="flex items-center gap-4">
                                <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                                  <Users className="h-5 w-5 text-primary" />
                                </div>
                                <div>
                                  <p className="font-medium">
                                    {user.name || "이름 없음"}
                                  </p>
                                  <p className="text-sm text-muted-foreground">
                                    {user.email}
                                  </p>
                                  {user.last_login_at && (
                                    <p className="text-xs text-muted-foreground">
                                      마지막 로그인:{" "}
                                      {formatDateTime(user.last_login_at)}
                                    </p>
                                  )}
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Badge
                                  className={getRoleColor(user.account_type)}
                                >
                                  {user.account_type === "admin"
                                    ? "시스템 관리자"
                                    : "일반 사용자"}
                                </Badge>
                                <Badge
                                  variant="outline"
                                  className={getStatusColor(
                                    user.last_login_at ? "active" : "inactive"
                                  )}
                                >
                                  {user.last_login_at ? "활성" : "비활성"}
                                </Badge>
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleUserDetail(user)}
                                      >
                                        <Eye className="h-4 w-4" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>사용자 상세 정보 보기</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                            </div>
                          ))
                        ) : (
                          <div className="text-center py-8 text-muted-foreground">
                            <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <p>사용자가 없습니다.</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>

          {/* 농장 관리 탭 */}
          <TabsContent value="farms" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2 mb-4">
                        <Building2 className="h-5 w-5" />
                        농장 관리
                      </CardTitle>
                      <CardDescription className="mt-2">
                        등록된 농장들을 조회하고 관리합니다
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4 mr-2" />
                            CSV 다운로드
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-lg">
                          <DialogHeader>
                            <DialogTitle>농장 CSV 다운로드</DialogTitle>
                            <DialogDescription>
                              다운로드할 농장 데이터의 조건과 포함할 정보를
                              선택하세요.
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-6">
                            {/* 농장 필터 선택 */}
                            <div>
                              <Label className="text-sm font-medium mb-2 block">
                                농장 필터
                              </Label>
                              <Select
                                value={farmFilter}
                                onValueChange={setFarmFilter}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="필터 선택" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="all">전체 농장</SelectItem>
                                  <SelectItem value="recent">
                                    최근 등록 (30일)
                                  </SelectItem>
                                  <SelectItem value="has_owner">
                                    소유자 정보 있음
                                  </SelectItem>
                                  <SelectItem value="by_region">
                                    주소 정보 있음
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            {/* 기간 선택 */}
                            <div>
                              <Label className="text-sm font-medium mb-3 block">
                                등록일 기간 선택
                              </Label>
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <Label className="text-xs text-muted-foreground">
                                    시작일
                                  </Label>
                                  <Input
                                    type="date"
                                    value={csvStartDate}
                                    onChange={(e) =>
                                      setCsvStartDate(e.target.value)
                                    }
                                    className="w-full"
                                  />
                                </div>
                                <div>
                                  <Label className="text-xs text-muted-foreground">
                                    종료일
                                  </Label>
                                  <Input
                                    type="date"
                                    value={csvEndDate}
                                    onChange={(e) =>
                                      setCsvEndDate(e.target.value)
                                    }
                                    className="w-full"
                                  />
                                </div>
                              </div>
                              <p className="text-xs text-muted-foreground mt-2">
                                * 기간을 선택하지 않으면 전체 기간의 데이터를
                                다운로드합니다 (최대 5년)
                              </p>
                            </div>

                            {/* 포함할 정보 선택 */}
                            <div>
                              <Label className="text-sm font-medium mb-3 block">
                                포함할 정보
                              </Label>
                              <div className="space-y-3">
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    id="farm-basic"
                                    checked={csvFarmBasic}
                                    onCheckedChange={(checked) =>
                                      setCsvFarmBasic(!!checked)
                                    }
                                  />
                                  <Label
                                    htmlFor="farm-basic"
                                    className="text-sm"
                                  >
                                    기본 정보 (농장명, 설명)
                                  </Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    id="farm-location"
                                    checked={csvFarmLocation}
                                    onCheckedChange={(checked) =>
                                      setCsvFarmLocation(!!checked)
                                    }
                                  />
                                  <Label
                                    htmlFor="farm-location"
                                    className="text-sm"
                                  >
                                    위치 정보 (주소, 지역)
                                  </Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    id="farm-owner"
                                    checked={csvFarmOwner}
                                    onCheckedChange={(checked) =>
                                      setCsvFarmOwner(!!checked)
                                    }
                                  />
                                  <Label
                                    htmlFor="farm-owner"
                                    className="text-sm"
                                  >
                                    소유자 정보 (소유자명, 연락처)
                                  </Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    id="farm-stats"
                                    checked={csvFarmStats}
                                    onCheckedChange={(checked) =>
                                      setCsvFarmStats(!!checked)
                                    }
                                  />
                                  <Label
                                    htmlFor="farm-stats"
                                    className="text-sm"
                                  >
                                    통계 정보 (등록일, 업데이트일)
                                  </Label>
                                </div>
                              </div>
                            </div>

                            {/* 다운로드 미리보기 */}
                            <div className="p-3 bg-muted rounded-lg">
                              <h4 className="text-sm font-medium mb-2">
                                다운로드 미리보기
                              </h4>
                              <div className="text-xs text-muted-foreground space-y-1">
                                <div>
                                  • 필터:{" "}
                                  {farmFilter === "all"
                                    ? "전체 농장"
                                    : farmFilter === "recent"
                                    ? "최근 등록 (30일)"
                                    : farmFilter === "has_owner"
                                    ? "소유자 정보 있음"
                                    : "주소 정보 있음"}
                                </div>
                                <div>
                                  • 등록일 기간: {csvStartDate || "전체"} ~{" "}
                                  {csvEndDate || "전체"}
                                </div>
                                <div>
                                  • 예상 농장 수: {filteredFarms.length}개
                                </div>
                                <div>
                                  • 포함 정보:{" "}
                                  {[
                                    csvFarmBasic && "기본정보",
                                    csvFarmLocation && "위치정보",
                                    csvFarmOwner && "소유자정보",
                                    csvFarmStats && "통계정보",
                                  ]
                                    .filter(Boolean)
                                    .join(", ") || "없음"}
                                </div>
                              </div>
                            </div>
                          </div>
                          <DialogFooter>
                            <Button
                              variant="outline"
                              onClick={() => {
                                setFarmFilter("all");
                                setCsvStartDate("");
                                setCsvEndDate("");
                                setCsvFarmBasic(true);
                                setCsvFarmLocation(true);
                                setCsvFarmOwner(true);
                                setCsvFarmStats(false);
                              }}
                            >
                              초기화
                            </Button>
                            <Button
                              onClick={handleExportFarms}
                              disabled={
                                !csvFarmBasic &&
                                !csvFarmLocation &&
                                !csvFarmOwner &&
                                !csvFarmStats
                              }
                            >
                              <Download className="h-4 w-4 mr-2" />
                              다운로드
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleRefresh}
                        disabled={loading}
                      >
                        <RefreshCw
                          className={`h-4 w-4 mr-2 ${
                            loading ? "animate-spin" : ""
                          }`}
                        />
                        새로고침
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* 검색 */}
                  <div className="flex items-center gap-4">
                    <div className="flex-1">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="농장 검색..."
                          value={searchTerm}
                          onChange={(e) => {
                            const value = e.target.value;
                            handleSearch(value, "farm");
                          }}
                          className="pl-10"
                        />
                      </div>
                    </div>
                    <Select
                      value={farmFilter}
                      onValueChange={handleFarmFilterChange}
                    >
                      <SelectTrigger className="w-40">
                        <Filter className="h-4 w-4 mr-2" />
                        <SelectValue placeholder="필터 선택" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">
                          전체 농장 ({farms.length}개)
                        </SelectItem>
                        <SelectItem value="recent">
                          최근 등록 (30일) (
                          {
                            farms.filter((f) => {
                              const farmDate = new Date(f.created_at);
                              const thirtyDaysAgo = new Date(
                                Date.now() - 30 * 24 * 60 * 60 * 1000
                              );
                              return farmDate > thirtyDaysAgo;
                            }).length
                          }
                          개)
                        </SelectItem>
                        <SelectItem value="active">
                          활성 농장 ({farms.filter((f) => f.is_active).length}
                          개)
                        </SelectItem>
                        <SelectItem value="inactive">
                          비활성 농장 (
                          {farms.filter((f) => !f.is_active).length}개)
                        </SelectItem>
                        {/* 농장 유형별 필터 (동적 생성) */}
                        {Array.from(
                          new Set(farms.map((f) => f.farm_type).filter(Boolean))
                        ).map((type) => (
                          <SelectItem key={type} value={`type_${type}`}>
                            {getFarmTypeLabel(type)} (
                            {farms.filter((f) => f.farm_type === type).length}
                            개)
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 농장 통계 카드 */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                          <Building2 className="h-4 w-4 text-blue-500" />
                          <div>
                            <p className="text-sm font-medium">전체 농장</p>
                            <p className="text-2xl font-bold">
                              {stats.totalFarms}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-green-500" />
                          <div>
                            <p className="text-sm font-medium">농장 소유자</p>
                            <p className="text-2xl font-bold">
                              {(() => {
                                // 농장 소유자는 farms 테이블의 owner_id로 계산
                                const uniqueOwnerIds = new Set(
                                  farms.map((f) => f.owner_id)
                                );
                                const farmOwnerCount = uniqueOwnerIds.size;
                                console.log(
                                  "Farm owner count in farms tab:",
                                  farmOwnerCount
                                );
                                return farmOwnerCount;
                              })()}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-purple-500" />
                          <div>
                            <p className="text-sm font-medium">지역 수</p>
                            <p className="text-2xl font-bold">
                              {
                                new Set(
                                  farms
                                    .map((f) => f.farm_address?.split(" ")[0])
                                    .filter(Boolean)
                                ).size
                              }
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-orange-500" />
                          <div>
                            <p className="text-sm font-medium">이번 달 등록</p>
                            <p className="text-2xl font-bold">
                              {
                                farms.filter((f) => {
                                  const farmDate = new Date(f.created_at);
                                  const now = new Date();
                                  return (
                                    farmDate.getMonth() === now.getMonth() &&
                                    farmDate.getFullYear() === now.getFullYear()
                                  );
                                }).length
                              }
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* 농장 목록 */}
                  <div className="border rounded-lg">
                    <div className="p-4 border-b bg-muted/50">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium">
                          농장 목록 ({filteredFarms.length}개)
                        </h3>
                        {farmFilter !== "all" && (
                          <div className="flex items-center gap-2">
                            <Badge variant="secondary" className="text-xs">
                              {farmFilter === "recent" && "최근 등록 (30일)"}
                              {farmFilter === "active" && "활성 농장"}
                              {farmFilter === "inactive" && "비활성 농장"}
                              {farmFilter.startsWith("type_") &&
                                getFarmTypeLabel(
                                  farmFilter.replace("type_", "")
                                )}
                            </Badge>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={async () => {
                                setFarmFilter("all");
                                await createSystemLog(
                                  "FARM_FILTER_CLEARED",
                                  "관리자가 농장 필터를 해제했습니다",
                                  "info"
                                );
                              }}
                              className="h-6 px-2 text-xs"
                            >
                              필터 해제
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="p-4">
                      {loading ? (
                        <div className="text-center py-8">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                          <p className="text-muted-foreground">
                            농장 데이터를 불러오는 중...
                          </p>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          {filteredFarms.length > 0 ? (
                            filteredFarms.map((farm) => (
                              <div
                                key={farm.id}
                                className="flex items-center justify-between p-4 border rounded-lg"
                              >
                                <div className="flex items-center gap-4">
                                  <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center">
                                    <Building2 className="h-6 w-6 text-primary" />
                                  </div>
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-1">
                                      <p className="font-medium">
                                        {farm.farm_name || "농장명 없음"}
                                      </p>
                                      {farm.farm_type && (
                                        <Badge
                                          variant="outline"
                                          className={`text-xs ${getFarmTypeColor(
                                            farm.farm_type
                                          )}`}
                                        >
                                          <div className="flex items-center gap-1">
                                            {(() => {
                                              const Icon = getFarmTypeIcon(
                                                farm.farm_type
                                              );
                                              return (
                                                <Icon className="h-3 w-3" />
                                              );
                                            })()}
                                            {getFarmTypeLabel(farm.farm_type)}
                                          </div>
                                        </Badge>
                                      )}
                                    </div>
                                    <div className="flex items-center gap-2 text-sm text-muted-foreground mb-1">
                                      <MapPin className="h-3 w-3" />
                                      <span>
                                        {farm.farm_address || "주소 없음"}
                                      </span>
                                    </div>
                                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                      <div className="flex items-center gap-1">
                                        <Users className="h-3 w-3" />
                                        <span>
                                          {farm.owner_name || "정보 없음"}
                                        </span>
                                      </div>
                                      <div className="flex items-center gap-1">
                                        <Calendar className="h-3 w-3" />
                                        <span>
                                          {new Date(
                                            farm.created_at
                                          ).toLocaleDateString("ko-KR")}
                                        </span>
                                      </div>
                                      {farm.manager_name && (
                                        <div className="flex items-center gap-1">
                                          <UserCheck className="h-3 w-3" />
                                          <span>
                                            관리자: {farm.manager_name}
                                          </span>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Badge
                                    variant="outline"
                                    className={
                                      farm.is_active
                                        ? "text-green-600 border-green-600"
                                        : "text-red-600 border-red-600"
                                    }
                                  >
                                    {farm.is_active ? "활성" : "비활성"}
                                  </Badge>
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleFarmDetail(farm)}
                                        >
                                          <Eye className="h-4 w-4" />
                                        </Button>
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p>농장 상세 정보 보기</p>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                </div>
                              </div>
                            ))
                          ) : (
                            <div className="text-center py-8 text-muted-foreground">
                              <Building2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                              <p>농장이 없습니다.</p>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>

          {/* 시스템 로그 탭 */}
          <TabsContent value="logs" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2 mb-4">
                        <FileText className="h-5 w-5" />
                        시스템 로그
                      </CardTitle>
                      <CardDescription className="mt-2">
                        시스템 활동 로그와 오류 기록을 확인합니다
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4 mr-2" />
                            CSV 다운로드
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-lg">
                          <DialogHeader>
                            <DialogTitle>시스템 로그 CSV 다운로드</DialogTitle>
                            <DialogDescription>
                              다운로드할 로그 데이터의 조건과 포함할 정보를
                              선택하세요.
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-6">
                            {/* 로그 필터 선택 */}
                            <div>
                              <Label className="text-sm font-medium mb-2 block">
                                로그 필터
                              </Label>
                              <Select
                                value={logFilter}
                                onValueChange={setLogFilter}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="필터 선택" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="all">전체 로그</SelectItem>
                                  <SelectItem value="info">
                                    정보 로그
                                  </SelectItem>
                                  <SelectItem value="warning">
                                    경고 로그
                                  </SelectItem>
                                  <SelectItem value="error">
                                    오류 로그
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            {/* 기간 선택 */}
                            <div>
                              <Label className="text-sm font-medium mb-3 block">
                                기간 선택
                              </Label>
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <Label className="text-xs text-muted-foreground">
                                    시작일
                                  </Label>
                                  <Input
                                    type="date"
                                    value={csvStartDate}
                                    onChange={(e) =>
                                      setCsvStartDate(e.target.value)
                                    }
                                    className="w-full"
                                  />
                                </div>
                                <div>
                                  <Label className="text-xs text-muted-foreground">
                                    종료일
                                  </Label>
                                  <Input
                                    type="date"
                                    value={csvEndDate}
                                    onChange={(e) =>
                                      setCsvEndDate(e.target.value)
                                    }
                                    className="w-full"
                                  />
                                </div>
                              </div>
                              <p className="text-xs text-muted-foreground mt-2">
                                * 기간을 선택하지 않으면 전체 기간의 데이터를
                                다운로드합니다 (최대 5년)
                              </p>
                            </div>

                            {/* 포함할 정보 선택 */}
                            <div>
                              <Label className="text-sm font-medium mb-3 block">
                                포함할 정보
                              </Label>
                              <div className="space-y-3">
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    id="log-basic"
                                    checked={csvLogBasic}
                                    onCheckedChange={(checked) =>
                                      setCsvLogBasic(!!checked)
                                    }
                                  />
                                  <Label
                                    htmlFor="log-basic"
                                    className="text-sm"
                                  >
                                    기본 정보 (액션, 레벨, 시간)
                                  </Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    id="log-details"
                                    checked={csvLogDetails}
                                    onCheckedChange={(checked) =>
                                      setCsvLogDetails(!!checked)
                                    }
                                  />
                                  <Label
                                    htmlFor="log-details"
                                    className="text-sm"
                                  >
                                    상세 정보 (세부 내용, 설명)
                                  </Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    id="log-user"
                                    checked={csvLogUser}
                                    onCheckedChange={(checked) =>
                                      setCsvLogUser(!!checked)
                                    }
                                  />
                                  <Label htmlFor="log-user" className="text-sm">
                                    사용자 정보 (사용자명, IP 주소)
                                  </Label>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    id="log-system"
                                    checked={csvLogSystem}
                                    onCheckedChange={(checked) =>
                                      setCsvLogSystem(!!checked)
                                    }
                                  />
                                  <Label
                                    htmlFor="log-system"
                                    className="text-sm"
                                  >
                                    시스템 정보 (브라우저, 디바이스)
                                  </Label>
                                </div>
                              </div>
                            </div>

                            {/* 다운로드 미리보기 */}
                            <div className="p-3 bg-muted rounded-lg">
                              <h4 className="text-sm font-medium mb-2">
                                다운로드 미리보기
                              </h4>
                              <div className="text-xs text-muted-foreground space-y-1">
                                <div>
                                  • 필터:{" "}
                                  {logFilter === "all"
                                    ? "전체 로그"
                                    : logFilter === "info"
                                    ? "정보 로그"
                                    : logFilter === "warning"
                                    ? "경고 로그"
                                    : "오류 로그"}
                                </div>
                                <div>
                                  • 기간: {csvStartDate || "전체"} ~{" "}
                                  {csvEndDate || "전체"}
                                </div>
                                <div>
                                  • 예상 로그 수: {filteredLogs.length}개
                                </div>
                                <div>
                                  • 포함 정보:{" "}
                                  {[
                                    csvLogBasic && "기본정보",
                                    csvLogDetails && "상세정보",
                                    csvLogUser && "사용자정보",
                                    csvLogSystem && "시스템정보",
                                  ]
                                    .filter(Boolean)
                                    .join(", ") || "없음"}
                                </div>
                              </div>
                            </div>
                          </div>
                          <DialogFooter>
                            <Button
                              variant="outline"
                              onClick={() => {
                                setLogFilter("all");
                                setCsvStartDate("");
                                setCsvEndDate("");
                                setCsvLogBasic(true);
                                setCsvLogDetails(true);
                                setCsvLogUser(true);
                                setCsvLogSystem(false);
                              }}
                            >
                              초기화
                            </Button>
                            <Button
                              onClick={handleExportLogs}
                              disabled={
                                !csvLogBasic &&
                                !csvLogDetails &&
                                !csvLogUser &&
                                !csvLogSystem
                              }
                            >
                              <Download className="h-4 w-4 mr-2" />
                              다운로드
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleRefresh}
                        disabled={loading}
                      >
                        <RefreshCw
                          className={`h-4 w-4 mr-2 ${
                            loading ? "animate-spin" : ""
                          }`}
                        />
                        새로고침
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* 검색 */}
                  <div className="flex items-center gap-4">
                    <div className="flex-1">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="로그 검색..."
                          value={searchTerm}
                          onChange={(e) => {
                            const value = e.target.value;
                            handleSearch(value, "log");
                          }}
                          className="pl-10"
                        />
                      </div>
                    </div>
                    <Select
                      value={logFilter}
                      onValueChange={handleLogFilterChange}
                    >
                      <SelectTrigger className="w-40">
                        <Filter className="h-4 w-4 mr-2" />
                        <SelectValue placeholder="필터 선택" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">전체 로그</SelectItem>
                        <SelectItem value="info">정보 로그</SelectItem>
                        <SelectItem value="warning">경고 로그</SelectItem>
                        <SelectItem value="error">오류 로그</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 로그 통계 카드 */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-blue-500" />
                          <div>
                            <p className="text-sm font-medium">전체 로그</p>
                            <p className="text-2xl font-bold">{totalLogs}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                          <Activity className="h-4 w-4 text-blue-500" />
                          <div>
                            <p className="text-sm font-medium">정보 로그</p>
                            <p className="text-2xl font-bold">
                              {logs.filter((l) => l.level === "info").length}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                          <AlertCircle className="h-4 w-4 text-yellow-500" />
                          <div>
                            <p className="text-sm font-medium">경고 로그</p>
                            <p className="text-2xl font-bold">
                              {logs.filter((l) => l.level === "warning").length}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                          <XCircle className="h-4 w-4 text-red-500" />
                          <div>
                            <p className="text-sm font-medium">오류 로그</p>
                            <p className="text-2xl font-bold">
                              {logs.filter((l) => l.level === "error").length}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* 로그 목록 */}
                  <div className="border rounded-lg">
                    <div className="p-4 border-b bg-muted/50 flex items-center justify-between">
                      <h3 className="font-medium">
                        시스템 로그 ({filteredLogs.length}개 표시 / 총{" "}
                        {totalLogs}개)
                      </h3>
                      <div className="flex items-center gap-2">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={handleBulkDeleteOldLogs}
                                className="text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                30일 이전 로그 삭제
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>30일 이전의 모든 로그를 삭제합니다</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>

                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setIsDeleteAllConfirmOpen(true)}
                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                완전 삭제
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>
                                모든 시스템 로그를 완전히 삭제합니다 (복구
                                불가!)
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    </div>
                    <div className="p-4">
                      <div className="space-y-4">
                        {filteredLogs.length > 0 ? (
                          filteredLogs.map((log) => (
                            <div
                              key={log.id}
                              className="flex items-start justify-between p-4 border rounded-lg"
                            >
                              <div className="flex items-start gap-4">
                                <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                                  {getLogLevelIcon(log.level)}
                                </div>
                                <div className="flex-1">
                                  <div className="flex items-center gap-2 mb-1">
                                    <p className="font-medium">{log.action}</p>
                                    <Badge
                                      variant="outline"
                                      className={getLogLevelColor(log.level)}
                                    >
                                      {log.level === "info"
                                        ? "정보"
                                        : log.level === "warning"
                                        ? "경고"
                                        : log.level === "error"
                                        ? "오류"
                                        : log.level}
                                    </Badge>
                                  </div>
                                  <p className="text-sm text-muted-foreground mb-2">
                                    {log.details}
                                  </p>
                                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                    <div className="flex items-center gap-1">
                                      <Users className="h-3 w-3" />
                                      <span>{log.user_name}</span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                      <Clock className="h-3 w-3" />
                                      <span>
                                        {new Date(
                                          log.created_at
                                        ).toLocaleString("ko-KR")}
                                      </span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                      <MapPin className="h-3 w-3" />
                                      <span>{log.ip_address}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleLogDetail(log)}
                                      >
                                        <Eye className="h-4 w-4" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>로그 상세 정보 보기</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>

                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleDeleteLog(log.id)}
                                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                      >
                                        <Trash2 className="h-4 w-4" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>로그 삭제</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                            </div>
                          ))
                        ) : (
                          <div className="text-center py-8 text-muted-foreground">
                            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <p>로그가 없습니다.</p>
                          </div>
                        )}
                      </div>

                      {/* 페이지네이션 */}
                      {totalLogs > logsPerPage && (
                        <div className="mt-6 flex items-center justify-between">
                          <div className="text-sm text-muted-foreground">
                            {(logPage - 1) * logsPerPage + 1}-
                            {Math.min(logPage * logsPerPage, totalLogs)} /{" "}
                            {totalLogs}개 표시
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handlePageChange(logPage - 1)}
                              disabled={logPage === 1}
                            >
                              이전
                            </Button>

                            <div className="flex items-center gap-1">
                              {Array.from(
                                {
                                  length: Math.min(
                                    5,
                                    Math.ceil(totalLogs / logsPerPage)
                                  ),
                                },
                                (_, i) => {
                                  const pageNum = Math.max(1, logPage - 2) + i;
                                  const totalPages = Math.ceil(
                                    totalLogs / logsPerPage
                                  );

                                  if (pageNum > totalPages) return null;

                                  return (
                                    <Button
                                      key={pageNum}
                                      variant={
                                        pageNum === logPage
                                          ? "default"
                                          : "outline"
                                      }
                                      size="sm"
                                      onClick={() => handlePageChange(pageNum)}
                                      className="w-8 h-8 p-0"
                                    >
                                      {pageNum}
                                    </Button>
                                  );
                                }
                              )}
                            </div>

                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handlePageChange(logPage + 1)}
                              disabled={
                                logPage >= Math.ceil(totalLogs / logsPerPage)
                              }
                            >
                              다음
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>

          {/* 통계 대시보드 탭 */}
          <TabsContent value="dashboard" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              {/* 전체 통계 요약 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      전체 사용자
                    </CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.totalUsers}</div>
                    <p className="text-xs text-muted-foreground">
                      활성: {stats.activeUsers}명 | 비활성:{" "}
                      {stats.totalUsers - stats.activeUsers}명
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      전체 농장
                    </CardTitle>
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats.totalFarms}</div>
                    <p className="text-xs text-muted-foreground">
                      농장 소유자: {stats.farmOwners}명
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      총 방문자
                    </CardTitle>
                    <UserCheck className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{visitors.length}</div>
                    <p className="text-xs text-muted-foreground">
                      오늘:{" "}
                      {
                        visitors.filter(
                          (v) =>
                            new Date(v.visit_datetime).toDateString() ===
                            new Date().toDateString()
                        ).length
                      }
                      명
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      시스템 로그
                    </CardTitle>
                    <FileText className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{totalLogs}</div>
                    <p className="text-xs text-muted-foreground">
                      오류: {stats.errorLogs}개
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* 차트 및 상세 통계 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* 농장 유형별 통계 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="h-5 w-5" />
                      농장 유형별 분포
                    </CardTitle>
                    <CardDescription>등록된 농장의 유형별 현황</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {Array.from(
                        new Set(farms.map((f) => f.farm_type).filter(Boolean))
                      ).map((type) => {
                        const count = farms.filter(
                          (f) => f.farm_type === type
                        ).length;
                        const percentage = Math.round(
                          (count / farms.length) * 100
                        );
                        return (
                          <div
                            key={type}
                            className="flex items-center justify-between"
                          >
                            <span className="text-sm">
                              {getFarmTypeLabel(type)}
                            </span>
                            <div className="flex items-center gap-2">
                              <div className="w-24 h-2 bg-gray-200 rounded-full">
                                <div
                                  className="h-2 bg-blue-500 rounded-full"
                                  style={{ width: `${percentage}%` }}
                                ></div>
                              </div>
                              <span className="text-sm text-muted-foreground w-12 text-right">
                                {count}개
                              </span>
                            </div>
                          </div>
                        );
                      })}
                      {farms.length === 0 && (
                        <div className="text-center text-muted-foreground py-4">
                          등록된 농장이 없습니다
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* 사용자 역할별 통계 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5" />
                      사용자 역할별 분포
                    </CardTitle>
                    <CardDescription>
                      시스템 사용자의 역할별 현황
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        {
                          role: "admin",
                          label: "시스템 관리자",
                          color: "bg-red-500",
                        },
                        {
                          role: "owner",
                          label: "농장 소유자",
                          color: "bg-green-500",
                        },
                        {
                          role: "user",
                          label: "일반 사용자",
                          color: "bg-blue-500",
                        },
                      ].map(({ role, label, color }) => {
                        const count = users.filter((user) => {
                          switch (role) {
                            case "admin":
                              return isSystemAdmin(user);
                            case "owner":
                              return isFarmOwner(user);
                            case "user":
                              return isGeneralUser(user);
                            default:
                              return false;
                          }
                        }).length;
                        const percentage =
                          users.length > 0
                            ? Math.round((count / users.length) * 100)
                            : 0;
                        return (
                          <div
                            key={role}
                            className="flex items-center justify-between"
                          >
                            <span className="text-sm">{label}</span>
                            <div className="flex items-center gap-2">
                              <div className="w-24 h-2 bg-gray-200 rounded-full">
                                <div
                                  className={`h-2 rounded-full ${color}`}
                                  style={{ width: `${percentage}%` }}
                                ></div>
                              </div>
                              <span className="text-sm text-muted-foreground w-12 text-right">
                                {count}명
                              </span>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* 월별 트렌드 및 지역별 분포 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* 월별 등록 트렌드 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="h-5 w-5" />
                      월별 등록 트렌드
                    </CardTitle>
                    <CardDescription>
                      최근 6개월간 농장 및 방문자 등록 현황
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {Array.from({ length: 6 }, (_, i) => {
                        const date = new Date();
                        date.setMonth(date.getMonth() - i);
                        const monthStr = date.toISOString().slice(0, 7); // YYYY-MM
                        const monthName = date.toLocaleDateString("ko-KR", {
                          year: "numeric",
                          month: "short",
                        });

                        const farmCount = farms.filter((f) =>
                          f.created_at.startsWith(monthStr)
                        ).length;

                        const visitorCount = visitors.filter((v) =>
                          v.visit_datetime.startsWith(monthStr)
                        ).length;

                        const maxCount = Math.max(farmCount, visitorCount, 1);

                        return (
                          <div key={monthStr} className="space-y-2">
                            <div className="flex items-center justify-between text-sm">
                              <span>{monthName}</span>
                              <span className="text-muted-foreground">
                                농장 {farmCount}개 | 방문자 {visitorCount}명
                              </span>
                            </div>
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                <span className="text-xs text-blue-600 w-8">
                                  농장
                                </span>
                                <div className="flex-1 h-2 bg-gray-200 rounded-full">
                                  <div
                                    className="h-2 bg-blue-500 rounded-full"
                                    style={{
                                      width: `${
                                        (farmCount / Math.max(maxCount, 10)) *
                                        100
                                      }%`,
                                    }}
                                  ></div>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <span className="text-xs text-green-600 w-8">
                                  방문
                                </span>
                                <div className="flex-1 h-2 bg-gray-200 rounded-full">
                                  <div
                                    className="h-2 bg-green-500 rounded-full"
                                    style={{
                                      width: `${
                                        (visitorCount /
                                          Math.max(maxCount, 10)) *
                                        100
                                      }%`,
                                    }}
                                  ></div>
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      }).reverse()}
                    </div>
                  </CardContent>
                </Card>

                {/* 지역별 농장 분포 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MapPin className="h-5 w-5" />
                      지역별 농장 분포
                    </CardTitle>
                    <CardDescription>
                      농장 주소 기반 지역별 현황
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {Array.from(
                        new Set(
                          farms
                            .map((f) => getRegionFromAddress(f.farm_address))
                            .filter(Boolean)
                        )
                      ).map((region) => {
                        const count = farms.filter((f) => {
                          const farmRegion = getRegionFromAddress(
                            f.farm_address
                          );
                          return farmRegion === region;
                        }).length;

                        const percentage =
                          farms.length > 0
                            ? Math.round((count / farms.length) * 100)
                            : 0;

                        return (
                          <div
                            key={region}
                            className="flex items-center justify-between"
                          >
                            <span className="text-sm">{region}</span>
                            <div className="flex items-center gap-2">
                              <div className="w-24 h-2 bg-gray-200 rounded-full">
                                <div
                                  className="h-2 bg-purple-500 rounded-full"
                                  style={{ width: `${percentage}%` }}
                                ></div>
                              </div>
                              <span className="text-sm text-muted-foreground w-12 text-right">
                                {count}개
                              </span>
                            </div>
                          </div>
                        );
                      })}
                      {farms.length === 0 && (
                        <div className="text-center text-muted-foreground py-4">
                          등록된 농장이 없습니다
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* 최근 활동 및 시스템 상태 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* 최근 활동 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="h-5 w-5" />
                      최근 활동
                    </CardTitle>
                    <CardDescription>최근 시스템 활동 내역</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {logs.slice(0, 5).map((log) => (
                        <div
                          key={log.id}
                          className="flex items-center gap-3 p-2 border-l-2 border-l-blue-500 bg-blue-50/50"
                        >
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <div className="flex-1">
                            <p className="text-sm font-medium">{log.action}</p>
                            <p className="text-xs text-muted-foreground">
                              {log.user_name} •{" "}
                              {new Date(log.created_at).toLocaleString("ko-KR")}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* 시스템 활동 요약 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5" />
                      시스템 활동 요약
                    </CardTitle>
                    <CardDescription>오늘의 주요 활동 현황</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span className="text-sm font-medium">
                            오늘 로그인
                          </span>
                        </div>
                        <span className="text-sm font-bold">
                          {
                            logs.filter(
                              (log) =>
                                log.action === "USER_LOGIN" &&
                                new Date(log.created_at).toDateString() ===
                                  new Date().toDateString()
                            ).length
                          }
                          명
                        </span>
                      </div>

                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                          <span className="text-sm font-medium">
                            오늘 농장 등록
                          </span>
                        </div>
                        <span className="text-sm font-bold">
                          {
                            logs.filter(
                              (log) =>
                                log.action === "FARM_CREATED" &&
                                new Date(log.created_at).toDateString() ===
                                  new Date().toDateString()
                            ).length
                          }
                          개
                        </span>
                      </div>

                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                          <span className="text-sm font-medium">
                            오늘 방문자
                          </span>
                        </div>
                        <span className="text-sm font-bold">
                          {
                            logs.filter(
                              (log) =>
                                log.action === "VISITOR_CREATED" &&
                                new Date(log.created_at).toDateString() ===
                                  new Date().toDateString()
                            ).length
                          }
                          명
                        </span>
                      </div>

                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                          <span className="text-sm font-medium">
                            QR 스캔 등록
                          </span>
                        </div>
                        <span className="text-sm font-bold">
                          {
                            logs.filter(
                              (log) =>
                                log.action === "VISITOR_CREATED" &&
                                log.details?.includes("QR") &&
                                new Date(log.created_at).toDateString() ===
                                  new Date().toDateString()
                            ).length
                          }
                          건
                        </span>
                      </div>

                      <div className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div
                            className={`w-3 h-3 rounded-full ${
                              logs.filter((l) => l.level === "error").length > 0
                                ? "bg-red-500"
                                : "bg-green-500"
                            }`}
                          ></div>
                          <span className="text-sm font-medium">
                            시스템 상태
                          </span>
                        </div>
                        <Badge
                          variant="outline"
                          className={
                            logs.filter((l) => l.level === "error").length > 0
                              ? "text-red-600 border-red-600"
                              : "text-green-600 border-green-600"
                          }
                        >
                          {logs.filter((l) => l.level === "error").length > 0
                            ? "오류 있음"
                            : "정상"}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </motion.div>
          </TabsContent>
        </Tabs>
      </div>

      {/* 사용자 상세 정보 모달 */}
      <Dialog
        open={isUserDetailOpen}
        onOpenChange={(open) => {
          setIsUserDetailOpen(open);
          if (!open) {
            setSelectedUser(null);
          }
        }}
      >
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>사용자 상세 정보</DialogTitle>
            <DialogDescription>
              선택한 사용자의 상세 정보를 확인합니다.
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">이름</Label>
                  <p className="text-sm mt-1">
                    {selectedUser.name || "이름 없음"}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">이메일</Label>
                  <p className="text-sm mt-1">{selectedUser.email}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">계정 유형</Label>
                  <p className="text-sm mt-1">
                    {selectedUser.account_type === "admin"
                      ? "시스템 관리자"
                      : selectedUser.account_type === "user"
                      ? "일반 사용자"
                      : "사용자"}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">상태</Label>
                  <p className="text-sm mt-1">
                    {selectedUser.last_login_at ? "활성" : "비활성"}
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">가입일</Label>
                  <p className="text-sm mt-1">
                    {new Date(selectedUser.created_at).toLocaleString("ko-KR")}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">마지막 로그인</Label>
                  <p className="text-sm mt-1">
                    {selectedUser.last_login_at
                      ? new Date(selectedUser.last_login_at).toLocaleString(
                          "ko-KR"
                        )
                      : "로그인 기록 없음"}
                  </p>
                </div>
              </div>
              {/* 농장 정보 (농장 소유자인 경우) */}
              {farms.filter((f) => f.owner_id === selectedUser.id).length >
                0 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">
                    농장 정보
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">소유 농장</Label>
                      <p className="text-sm mt-1">
                        {
                          farms.filter((f) => f.owner_id === selectedUser.id)
                            .length
                        }
                        개
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">
                        최근 등록 농장
                      </Label>
                      <p className="text-sm mt-1">
                        {
                          farms.filter((f) => {
                            const farmDate = new Date(f.created_at);
                            const thirtyDaysAgo = new Date(
                              Date.now() - 30 * 24 * 60 * 60 * 1000
                            );
                            return (
                              f.owner_id === selectedUser.id &&
                              farmDate > thirtyDaysAgo
                            );
                          }).length
                        }
                        개 (30일 내)
                      </p>
                    </div>
                  </div>

                  {/* 농장 목록 */}
                  {farms.filter((f) => f.owner_id === selectedUser.id).length >
                    0 && (
                    <div>
                      <Label className="text-sm font-medium mb-2 block">
                        농장 목록
                      </Label>
                      <div className="space-y-2 max-h-32 overflow-y-auto">
                        {farms
                          .filter((f) => f.owner_id === selectedUser.id)
                          .slice(0, 5)
                          .map((farm) => (
                            <div
                              key={farm.id}
                              className="p-2 border rounded text-xs"
                            >
                              <p className="font-medium">{farm.farm_name}</p>
                              <p className="text-muted-foreground truncate">
                                {farm.farm_address}
                              </p>
                            </div>
                          ))}
                        {farms.filter((f) => f.owner_id === selectedUser.id)
                          .length > 5 && (
                          <p className="text-xs text-muted-foreground text-center">
                            +
                            {farms.filter((f) => f.owner_id === selectedUser.id)
                              .length - 5}
                            개 더
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* 농장 상세 정보 모달 */}
      <Dialog
        open={isFarmDetailOpen}
        onOpenChange={(open) => {
          setIsFarmDetailOpen(open);
          if (!open) {
            setSelectedFarm(null);
          }
        }}
      >
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>농장 상세 정보</DialogTitle>
            <DialogDescription>
              선택한 농장의 상세 정보를 확인합니다.
            </DialogDescription>
          </DialogHeader>
          {selectedFarm && (
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium">농장명</Label>
                <p className="text-sm mt-1">
                  {selectedFarm.farm_name || "농장명 없음"}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium">주소</Label>
                <p className="text-sm mt-1">
                  {selectedFarm.farm_address || "주소 없음"}
                </p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">소유자</Label>
                  <p className="text-sm mt-1">
                    {selectedFarm.owner_name || "소유자 정보 없음"}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">상태</Label>
                  <p className="text-sm mt-1">
                    {selectedFarm.is_active ? "활성" : "비활성"}
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">등록일</Label>
                  <p className="text-sm mt-1">
                    {new Date(selectedFarm.created_at).toLocaleString("ko-KR")}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">농장 ID</Label>
                  <p className="text-xs mt-1 font-mono">{selectedFarm.id}</p>
                </div>
              </div>
              {selectedFarm.description && (
                <div>
                  <Label className="text-sm font-medium">설명</Label>
                  <p className="text-sm mt-1">{selectedFarm.description}</p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* 로그 상세 정보 모달 */}
      <Dialog
        open={isLogDetailOpen}
        onOpenChange={(open) => {
          setIsLogDetailOpen(open);
          if (!open) {
            setSelectedLog(null);
          }
        }}
      >
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>로그 상세 정보</DialogTitle>
            <DialogDescription>
              선택한 로그의 상세 정보를 확인합니다.
            </DialogDescription>
          </DialogHeader>
          {selectedLog && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">액션</Label>
                  <p className="text-sm mt-1">{selectedLog.action}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">레벨</Label>
                  <p className="text-sm mt-1">
                    {selectedLog.level === "info"
                      ? "정보"
                      : selectedLog.level === "warning"
                      ? "경고"
                      : selectedLog.level === "error"
                      ? "오류"
                      : selectedLog.level}
                  </p>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium">상세 내용</Label>
                <p className="text-sm mt-1">{selectedLog.details}</p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">사용자</Label>
                  <p className="text-sm mt-1">{selectedLog.user_name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">IP 주소</Label>
                  <p className="text-xs mt-1 font-mono">
                    {selectedLog.ip_address}
                  </p>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium">시간</Label>
                <p className="text-sm mt-1">
                  {new Date(selectedLog.created_at).toLocaleString("ko-KR")}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium">User Agent</Label>
                <p className="text-xs mt-1 font-mono break-all">
                  {selectedLog.user_agent}
                </p>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* 전체 로그 삭제 확인 대화상자 */}
      <Dialog
        open={isDeleteAllConfirmOpen}
        onOpenChange={setIsDeleteAllConfirmOpen}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              완전 로그 삭제 확인
            </DialogTitle>
            <DialogDescription>
              <div className="space-y-3">
                <p className="text-sm">
                  <strong className="text-red-600">경고:</strong> 모든 시스템
                  로그가 완전히 삭제됩니다.
                </p>
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <div className="flex items-start gap-2">
                    <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                    <div className="text-sm text-red-700">
                      <p className="font-medium mb-1">삭제될 내용:</p>
                      <ul className="list-disc list-inside space-y-1 text-xs">
                        <li>모든 시스템 로그 ({totalLogs}개)</li>
                        <li>모든 사용자 활동 기록</li>
                        <li>모든 농장 관리 기록</li>
                        <li>모든 시스템 오류 기록</li>
                        <li className="text-red-600 font-medium">
                          ⚠️ 복구 불가능 - 완전 삭제
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">
                  정말로 모든 시스템 로그를 완전히 삭제하시겠습니까?
                  <strong className="text-red-600">
                    이 작업은 되돌릴 수 없습니다!
                  </strong>
                </p>
              </div>
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-3 mt-6">
            <Button
              variant="outline"
              onClick={() => setIsDeleteAllConfirmOpen(false)}
            >
              취소
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                handleTrueDeleteAllLogs(); // 완전 삭제 함수 사용
                setIsDeleteAllConfirmOpen(false);
              }}
              className="bg-red-600 hover:bg-red-700"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              완전 삭제
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
