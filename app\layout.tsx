import type React from "react";
import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import { AuthProvider } from "@/components/providers/auth-provider";
import { ToastPositionProvider } from "@/components/providers/toast-position-provider";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Farm Visitor Management System",
  description: "QR-based visitor management for agricultural facilities",
  manifest: "/manifest.json",
  themeColor: "#2D6A4F",
  viewport: "width=device-width, initial-scale=1, maximum-scale=1",
  generator: "v0.dev",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider>
          <ToastPositionProvider>
            {children}
            <Toaster />
          </ToastPositionProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
