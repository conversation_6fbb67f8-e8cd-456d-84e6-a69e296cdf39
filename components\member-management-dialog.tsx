import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import { UserSearch } from "@/components/user-search";
import { toast } from "sonner";

interface User {
  id: string;
  email: string;
  name: string | null;
  profile_image_url: string | null;
}

interface MemberManagementDialogProps {
  farmId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onMemberAdded: () => void;
}

export function MemberManagementDialog({
  farmId,
  open,
  onOpenChange,
  onMemberAdded,
}: MemberManagementDialogProps) {
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [role, setRole] = useState<"manager" | "viewer">("viewer");
  const [isLoading, setIsLoading] = useState(false);

  const handleUserSelect = (user: User) => {
    setSelectedUser(user);
  };

  const handleAddMember = async () => {
    if (!selectedUser) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/farms/${farmId}/members`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId: selectedUser.id,
          role: role,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to add member");
      }

      toast.success("구성원이 추가되었습니다");
      onMemberAdded();
      onOpenChange(false);
      setSelectedUser(null);
      setRole("viewer");
    } catch (error) {
      toast.error("구성원 추가에 실패했습니다");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>구성원 추가</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <UserSearch farmId={farmId} onSelect={handleUserSelect} />

          {selectedUser && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">
                    {selectedUser.name || "이름 없음"}
                  </div>
                  <div className="text-sm text-gray-500">
                    {selectedUser.email}
                  </div>
                </div>
                <Select
                  value={role}
                  onValueChange={(value: "manager" | "viewer") =>
                    setRole(value)
                  }
                >
                  <SelectTrigger className="w-[120px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="manager">관리자</SelectItem>
                    <SelectItem value="viewer">조회자</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button
                className="w-full"
                onClick={handleAddMember}
                disabled={isLoading}
              >
                {isLoading ? "추가 중..." : "구성원 추가"}
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
