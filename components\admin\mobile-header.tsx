"use client";

import { Leaf } from "lucide-react";
import { useAuth } from "@/components/providers/auth-provider";
import { useFarms } from "@/hooks/use-farms";

export function MobileHeader() {
  const { profile } = useAuth();
  const { farms } = useFarms();

  return (
    <header className="flex h-16 items-center justify-between border-b bg-background px-4 md:hidden">
      <div className="flex items-center gap-2">
        <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
          <Leaf className="h-5 w-5 text-primary-foreground" />
        </div>
        <div className="flex flex-col">
          <span className="text-sm font-semibold">농장 관리</span>
          <span className="text-xs text-muted-foreground">
            {profile?.role === "admin"
              ? "시스템 관리자"
              : farms.length > 0
              ? `${farms.length}개 농장`
              : "농장 등록 필요"}
          </span>
        </div>
      </div>

      {/* 오른쪽 공간 (필요시 추가 요소 배치 가능) */}
      <div className="flex items-center">
        {/* 향후 알림, 사용자 메뉴 등 추가 가능 */}
      </div>
    </header>
  );
}
