"use client";

import { useAuth } from "@/components/providers/auth-provider";
import { useFarms } from "@/hooks/use-farms";
import { getFarmTypeLabel, getFarmTypeIcon } from "@/lib/constants/farm-types";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  BarChart3,
  Building2,
  Users,
  Bell,
  Settings,
  User,
  LogOut,
  Leaf,
  Home,
  Shield,
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useMemo } from "react";

export function AdminSidebar() {
  const { profile, signOut } = useAuth();
  const { farms } = useFarms();
  const { isMobile, setOpenMobile } = useSidebar();

  // 모바일에서 메뉴 클릭 시 사이드바 닫기
  const handleMenuClick = () => {
    if (isMobile) {
      setOpenMobile(false);
    }
  };

  // 터치 제스처 핸들러
  const handleTouchStart = (e: React.TouchEvent) => {
    if (!isMobile) return;
    const touch = e.touches[0];
    const startX = touch.clientX;

    const handleTouchMove = (moveEvent: TouchEvent) => {
      const currentTouch = moveEvent.touches[0];
      const deltaX = currentTouch.clientX - startX;

      // 왼쪽으로 50px 이상 스와이프하면 닫기
      if (deltaX < -50) {
        setOpenMobile(false);
        document.removeEventListener("touchmove", handleTouchMove);
        document.removeEventListener("touchend", handleTouchEnd);
      }
    };

    const handleTouchEnd = () => {
      document.removeEventListener("touchmove", handleTouchMove);
      document.removeEventListener("touchend", handleTouchEnd);
    };

    document.addEventListener("touchmove", handleTouchMove);
    document.addEventListener("touchend", handleTouchEnd);
  };

  // 더블 탭으로 닫기
  const handleDoubleClick = () => {
    if (isMobile) {
      setOpenMobile(false);
    }
  };
  const pathname = usePathname();

  // 동적 메뉴 아이템 생성
  const menuItems = useMemo(() => {
    const isAdmin = profile?.account_type === "admin";

    // Admin인 경우 전체 방문자 기록, 일반 사용자인 경우 첫 번째 농장의 방문자 기록
    let visitorsUrl: string;
    let visitorsTitle: string;
    let visitorsBadge: string | null = null;

    if (isAdmin) {
      visitorsUrl = "/admin/all-visitors";
      visitorsTitle = "전체 방문자 기록";
    } else {
      // 일반 사용자도 통합 페이지 사용
      visitorsUrl = "/admin/visitors";
      visitorsTitle = "방문자 기록";
      visitorsBadge = farms.length === 0 ? "농장 필요" : null;
    }

    return [
      {
        title: "대시보드",
        url: "/admin/dashboard",
        icon: BarChart3,
        badge: null,
      },
      {
        title: "농장 관리",
        url: "/admin/farms",
        icon: Building2,
        badge: null,
      },
      {
        title: visitorsTitle,
        url: visitorsUrl,
        icon: Users,
        badge: visitorsBadge,
      },
      {
        title: "알림 설정",
        url: "/admin/notifications",
        icon: Bell,
        badge: null,
      },
      {
        title: "시스템 관리",
        url: "/admin/management",
        icon: Shield,
        badge: null,
      },
      {
        title: "시스템 설정",
        url: "/admin/settings",
        icon: Settings,
        badge: null,
      },
      {
        title: "계정 관리",
        url: "/admin/account",
        icon: User,
        badge: null,
      },
    ];
  }, [farms, profile?.account_type]);

  return (
    <Sidebar
      className="bg-background border-r"
      onTouchStart={handleTouchStart}
      onDoubleClick={handleDoubleClick}
    >
      <SidebarHeader className="bg-background border-b">
        <div className="flex items-center gap-2 px-2 py-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary">
            <Leaf className="h-6 w-6 text-primary-foreground" />
          </div>
          <div className="flex flex-col flex-1">
            <span className="text-base font-semibold">농장 관리 시스템</span>
            <span className="text-xs text-muted-foreground">
              {profile?.account_type === "admin"
                ? "시스템 관리자"
                : farms.length > 0
                ? `${farms.length}개 농장 관리`
                : "농장을 등록해주세요"}
            </span>
          </div>
        </div>

        {/* 대시보드로 돌아가기 버튼 - 모바일에서만 표시 */}
        <div className="px-2 pb-2 md:hidden">
          <Link href="/admin/dashboard" legacyBehavior>
            <Button
              variant="outline"
              size="sm"
              className="w-full justify-start"
              onClick={handleMenuClick}
            >
              <Home className="mr-2 h-4 w-4" />
              대시보드로 이동
            </Button>
          </Link>
        </div>

        {/* 모바일 사용 안내 */}
        {isMobile && (
          <div className="px-2 pb-2 md:hidden">
            <div className="text-xs text-muted-foreground text-center py-2 px-3 bg-muted/30 rounded-lg">
              💡 닫기: 외부 터치 · 왼쪽 스와이프 · 우하단 버튼
            </div>
          </div>
        )}
      </SidebarHeader>

      <SidebarContent className="bg-background">
        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-medium text-muted-foreground px-2 py-2">
            관리 메뉴
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <Link href={item.url} passHref legacyBehavior>
                    <Button
                      variant={pathname === item.url ? "secondary" : "ghost"}
                      className="w-full justify-start h-auto py-2.5 px-3"
                      onClick={handleMenuClick}
                    >
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center gap-3">
                          <item.icon className="h-5 w-5" />
                          <span className="font-medium">{item.title}</span>
                        </div>
                        {item.badge && (
                          <Badge
                            variant="secondary"
                            className="text-xs px-2 py-0.5"
                          >
                            {item.badge}
                          </Badge>
                        )}
                      </div>
                    </Button>
                  </Link>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* 농장별 바로가기 (모든 사용자, 농장이 2개 이상일 때) */}
        {farms.length > 1 && (
          <SidebarGroup>
            <SidebarGroupLabel className="text-xs font-medium text-muted-foreground px-2 py-2">
              농장별 바로가기
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {farms.map((farm) => (
                  <SidebarMenuItem key={farm.id}>
                    <Link
                      href={`/admin/farms/${farm.id}/visitors`}
                      passHref
                      legacyBehavior
                    >
                      <Button
                        variant={
                          pathname === `/admin/farms/${farm.id}/visitors`
                            ? "secondary"
                            : "ghost"
                        }
                        className="w-full justify-start h-auto py-2.5 px-3"
                        onClick={handleMenuClick}
                      >
                        <div className="flex items-center gap-3 w-full">
                          {(() => {
                            const Icon = getFarmTypeIcon(farm.farm_type);
                            return (
                              <Icon className="h-4 w-4 flex-shrink-0 text-muted-foreground" />
                            );
                          })()}
                          <div className="flex-1 min-w-0 text-left">
                            <div className="truncate font-medium text-sm">
                              {farm.farm_name}
                            </div>
                            <div className="text-xs text-muted-foreground truncate">
                              {getFarmTypeLabel(farm.farm_type)}
                            </div>
                          </div>
                        </div>
                      </Button>
                    </Link>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}

        {/* 빠른 액션 - 모바일에서만 표시 */}
        <SidebarGroup className="md:hidden">
          <SidebarGroupLabel className="text-xs font-medium text-muted-foreground px-2 py-2">
            빠른 액션
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <div className="px-2 space-y-2">
              <Link href="/admin/farms" legacyBehavior>
                <Button
                  variant="default"
                  size="sm"
                  className="w-full justify-start"
                  onClick={handleMenuClick}
                >
                  <Building2 className="mr-2 h-4 w-4" />새 농장 추가
                </Button>
              </Link>
              <Link
                href={
                  profile?.account_type === "admin"
                    ? "/admin/all-visitors"
                    : farms.length > 0
                    ? "/admin/visitors"
                    : "/admin/farms"
                }
                legacyBehavior
              >
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  disabled={
                    profile?.account_type !== "admin" && farms.length === 0
                  }
                  onClick={handleMenuClick}
                >
                  <Users className="mr-2 h-4 w-4" />
                  {profile?.account_type === "admin"
                    ? "전체 방문자 현황"
                    : farms.length > 0
                    ? "방문자 현황"
                    : "농장 등록 필요"}
                </Button>
              </Link>
            </div>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="bg-background border-t">
        <SidebarMenu>
          <SidebarMenuItem>
            <div className="px-3 py-2 bg-muted/50 rounded-lg mx-2 mb-2">
              <div className="text-xs text-muted-foreground mb-1">
                현재 로그인
              </div>
              <div className="text-sm font-medium truncate">
                {profile?.name || "로그인 필요"}
              </div>
              <div className="text-xs text-muted-foreground truncate">
                {profile?.email || "로그인이 필요합니다"}
              </div>
            </div>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <Button
              variant="ghost"
              className="w-full justify-start mx-2 mb-2 text-red-600 hover:text-red-700 hover:bg-red-50"
              onClick={async () => {
                console.log("🔄 Logout button clicked");

                // 즉시 리다이렉트 시작 (signOut 완료를 기다리지 않음)
                const redirectTimer = setTimeout(() => {
                  console.log("🔄 Button timeout redirect...");
                  window.location.href = "/login";
                }, 1000); // 1초 후 강제 리다이렉트

                try {
                  // signOut 시작하지만 완료를 기다리지 않음
                  signOut()
                    .then(() => {
                      console.log("✅ SignOut completed");
                      clearTimeout(redirectTimer);
                      // signOut이 빨리 완료되면 즉시 리다이렉트
                      if (window.location.pathname !== "/login") {
                        console.log("🔄 Button immediate redirect...");
                        window.location.href = "/login";
                      }
                    })
                    .catch((error) => {
                      console.error("❌ SignOut failed:", error);
                      clearTimeout(redirectTimer);
                      console.log("🔄 Button error redirect...");
                      window.location.href = "/login";
                    });
                } catch (error) {
                  console.error("❌ SignOut sync error:", error);
                  clearTimeout(redirectTimer);
                  console.log("🔄 Button sync error redirect...");
                  window.location.href = "/login";
                }
              }}
            >
              <LogOut className="mr-2 h-4 w-4" />
              로그아웃
            </Button>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
