-- 농장 관리 시스템 테스트 데이터
-- 개발 및 테스트용 초기 데이터 생성

-- 1. 테스트 사용자 생성 (auth.users 테이블에 직접 삽입)
-- 주의: 실제 프로덕션에서는 Supabase Auth를 통해 사용자를 생성해야 합니다.

-- 관리자 계정
INSERT INTO auth.users (
    id, 
    instance_id, 
    aud, 
    role, 
    email, 
    encrypted_password, 
    email_confirmed_at, 
    created_at, 
    updated_at,
    raw_user_meta_data,
    is_super_admin
) VALUES (
    '11111111-1111-1111-1111-111111111111',
    '00000000-0000-0000-0000-000000000000',
    'authenticated',
    'authenticated',
    '<EMAIL>',
    crypt('password123', gen_salt('bf')),
    NOW(),
    NOW(),
    NOW(),
    '{"name": "시스템 관리자", "phone": "010-1234-5678"}',
    false
) ON CONFLICT (id) DO NOTHING;

-- 농장주 계정
INSERT INTO auth.users (
    id, 
    instance_id, 
    aud, 
    role, 
    email, 
    encrypted_password, 
    email_confirmed_at, 
    created_at, 
    updated_at,
    raw_user_meta_data,
    is_super_admin
) VALUES (
    '22222222-2222-2222-2222-222222222222',
    '00000000-0000-0000-0000-000000000000',
    'authenticated',
    'authenticated',
    '<EMAIL>',
    crypt('password123', gen_salt('bf')),
    NOW(),
    NOW(),
    NOW(),
    '{"name": "김농장주", "phone": "010-2345-6789"}',
    false
) ON CONFLICT (id) DO NOTHING;

-- 2. 프로필 데이터 생성
INSERT INTO public.profiles (id, email, name, phone_number, role) VALUES
    ('11111111-1111-1111-1111-111111111111', '<EMAIL>', '시스템 관리자', '010-1234-5678', 'admin'),
    ('22222222-2222-2222-2222-222222222222', '<EMAIL>', '김농장주', '010-2345-6789', 'owner')
ON CONFLICT (id) DO NOTHING;

-- 3. 테스트 농장 데이터 생성
INSERT INTO public.farms (id, farm_name, farm_address, farm_phone_number, animal_type, notes, owner_id) VALUES
    ('33333333-3333-3333-3333-333333333333', '그린팜 1농장', '경기도 화성시 농장로 123', '************', '돼지', '친환경 돼지 농장', '22222222-2222-2222-2222-222222222222'),
    ('44444444-4444-4444-4444-444444444444', '그린팜 2농장', '경기도 화성시 농장로 456', '************', '닭', '방사 닭 농장', '22222222-2222-2222-2222-222222222222')
ON CONFLICT (id) DO NOTHING;

-- 4. 농장 구성원 데이터 (농장주를 구성원으로 추가)
INSERT INTO public.farm_members (farm_id, user_id, role) VALUES
    ('33333333-3333-3333-3333-333333333333', '22222222-2222-2222-2222-222222222222', 'owner'),
    ('44444444-4444-4444-4444-444444444444', '22222222-2222-2222-2222-222222222222', 'owner')
ON CONFLICT (farm_id, user_id) DO NOTHING;

-- 5. 샘플 방문자 기록 데이터
INSERT INTO public.visitor_entries (farm_id, visitor_name, visitor_phone, visitor_company, visit_purpose, vehicle_number, entry_time, notes) VALUES
    ('33333333-3333-3333-3333-333333333333', '박방문자', '010-3456-7890', '사료회사', '사료 배송', '12가3456', NOW() - INTERVAL '2 hours', '정기 사료 배송'),
    ('33333333-3333-3333-3333-333333333333', '이수의사', '010-4567-8901', '동물병원', '정기 검진', '23나4567', NOW() - INTERVAL '1 hour', '월례 건강 검진'),
    ('44444444-4444-4444-4444-444444444444', '최검사관', '010-5678-9012', '방역청', '방역 점검', '34다5678', NOW() - INTERVAL '30 minutes', '정기 방역 점검');
