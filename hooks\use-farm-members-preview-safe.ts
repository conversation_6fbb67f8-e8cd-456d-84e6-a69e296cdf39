"use client";

import { useState, useEffect, useRef } from "react";
import { supabase } from "@/lib/supabase";

/**
 * 농장 구성원 미리보기 데이터 인터페이스
 */
export interface FarmMemberPreview {
  id: string;
  name: string;
  role: "owner" | "manager" | "viewer";
  email: string;
}

/**
 * 농장별 구성원 데이터 인터페이스
 */
export interface FarmMembersData {
  [farmId: string]: {
    count: number;
    members: FarmMemberPreview[];
    loading: boolean;
  };
}

export function useFarmMembersPreview(farmIds: string[]) {
  const [membersData, setMembersData] = useState<FarmMembersData>({});
  const prevFarmIdsRef = useRef<string>("");

  useEffect(() => {
    // farmIds가 변경되었는지 확인 (문자열 비교로 안전하게)
    const currentFarmIds = farmIds.join(",");
    if (currentFarmIds === prevFarmIdsRef.current) return;

    prevFarmIdsRef.current = currentFarmIds;

    if (farmIds.length === 0) {
      setMembersData({});
      return;
    }

    const fetchFarmMembers = async () => {
      try {
        // 모든 농장의 구성원 정보를 한 번에 가져오기
        const { data: members, error } = await supabase
          .from("farm_members")
          .select(
            `
            id,
            farm_id,
            role,
            created_at,
            profiles:user_id (
              id,
              email,
              name
            )
          `
          )
          .in("farm_id", farmIds);

        if (error) throw error;

        // 농장별로 구성원 데이터 그룹화
        const groupedMembers: FarmMembersData = {};

        farmIds.forEach((farmId) => {
          const farmMembers =
            members?.filter((member: any) => member.farm_id === farmId) || [];

          // 권한별 우선순위로 정렬
          const sortedFarmMembers = farmMembers.sort((a: any, b: any) => {
            const roleOrder = { owner: 1, manager: 2, viewer: 3 };
            const aOrder = roleOrder[a.role as keyof typeof roleOrder] || 999;
            const bOrder = roleOrder[b.role as keyof typeof roleOrder] || 999;

            if (aOrder !== bOrder) {
              return aOrder - bOrder; // 권한 순서로 정렬
            }

            // 같은 권한이면 생성일 순서로 정렬
            return (
              new Date(a.created_at).getTime() -
              new Date(b.created_at).getTime()
            );
          });

          groupedMembers[farmId] = {
            count: farmMembers.length,
            members: sortedFarmMembers.slice(0, 3).map((member: any) => ({
              id: member.id,
              name: member.profiles?.name || "Unknown",
              role: member.role as "owner" | "manager" | "viewer",
              email: member.profiles?.email || "",
            })),
            loading: false,
          };
        });

        setMembersData(groupedMembers);
      } catch (error) {
        console.error("Error fetching farm members:", error);

        // 에러 발생 시 빈 데이터로 설정
        const errorData: FarmMembersData = {};
        farmIds.forEach((farmId) => {
          errorData[farmId] = {
            count: 0,
            members: [],
            loading: false,
          };
        });
        setMembersData(errorData);
      }
    };

    fetchFarmMembers();
  }, [farmIds]);

  const getMembersForFarm = (farmId: string) => {
    return (
      membersData[farmId] || {
        count: 0,
        members: [],
        loading: false,
      }
    );
  };

  return {
    membersData,
    getMembersForFarm,
  };
}
