"use client"

import type React from "react"

import { createContext, useContext, useState, useEffect } from "react"
import type { Session, SupabaseClient, User } from "@supabase/supabase-js"
import { supabase } from "../utils/supabase"

type AuthContextType = {
  supabaseClient: SupabaseClient | null
  session: Session | null
  user: User | null
  loading: boolean
}

const AuthContext = createContext<AuthContextType>({
  supabaseClient: null,
  session: null,
  user: null,
  loading: true,
})

type Props = {
  children: React.ReactNode
}

const AuthProvider = ({ children }: Props) => {
  const [session, setSession] = useState<Session | null>(null)
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState<boolean>(true)

  useEffect(() => {
    // Handle mock client
    if (typeof supabase.auth.onAuthStateChange !== "function") {
      setLoading(false)
      return
    }

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setUser(session?.user ?? null)
      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  const value = {
    supabaseClient: supabase,
    session,
    user,
    loading,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

export { AuthProvider, useAuth }
