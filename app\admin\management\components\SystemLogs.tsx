import { useCallback, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { useSystemLog } from "../hooks/useSystemLog";
import { useDataManagement } from "../hooks/useDataManagement";
import { DataTable } from "./DataTable";
import {
  formatDateTime,
  getLogLevelColor,
  getLogLevelIcon,
} from "../utils/formatters";
import { PaginationParams } from "../utils/query-utils";

interface SystemLog {
  id: string;
  user_id: string;
  action: string;
  details: string;
  level: "info" | "warning" | "error";
  resource_type?: string;
  resource_id?: string;
  metadata?: Record<string, any>;
  ip_address: string;
  user_agent: string;
  created_at: string;
  profiles?: {
    name: string;
    email: string;
  };
}

const FILTER_OPTIONS = [
  { value: "all", label: "전체" },
  { value: "info", label: "정보" },
  { value: "warning", label: "경고" },
  { value: "error", label: "오류" },
];

export function SystemLogs() {
  const { toast } = useToast();
  const { logAction, logError } = useSystemLog();
  const [selectedLog, setSelectedLog] = useState<SystemLog | null>(null);
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);

  const fetchLogs = useCallback(
    async (params: PaginationParams) => {
      try {
        const response = await fetch(
          `/api/admin/logs?page=${params.page}&limit=${params.limit}`
        );
        if (!response.ok) throw new Error("Failed to fetch logs");
        return await response.json();
      } catch (error) {
        logError("FETCH_LOGS_ERROR", error);
        toast({
          title: "로그 목록 로딩 실패",
          description: "로그 목록을 불러오는 중 오류가 발생했습니다.",
          variant: "destructive",
        });
        return {
          data: [],
          total: 0,
          currentPage: 1,
          totalPages: 1,
        };
      }
    },
    [logError, toast]
  );

  const {
    data: logs,
    isLoading,
    searchTerm,
    currentFilter,
    handleSearch,
    handleFilter,
    currentPage,
    totalPages,
    handlePageChange,
    refetch,
  } = useDataManagement<SystemLog>({
    fetchData: fetchLogs,
    searchFields: ["action", "details", "level"],
    resourceName: "시스템 로그",
    defaultLimit: 20,
  });

  const handleLogDetail = async (log: SystemLog) => {
    setSelectedLog(log);
    setIsDetailOpen(true);
    await logAction("VIEW_LOG_DETAIL", `로그 ID: ${log.id} 상세 조회`);
  };

  const handleDeleteLog = async (logId: string) => {
    try {
      const response = await fetch(`/api/admin/logs/${logId}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete log");

      refetch();
      toast({
        title: "로그 삭제 완료",
        description: "선택한 로그가 삭제되었습니다.",
      });
    } catch (error) {
      logError("DELETE_LOG_ERROR", error);
      toast({
        title: "로그 삭제 실패",
        description: "로그를 삭제하는 중 오류가 발생했습니다.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteOldLogs = async () => {
    try {
      const response = await fetch("/api/admin/logs/cleanup", {
        method: "POST",
      });

      if (!response.ok) throw new Error("Failed to cleanup old logs");

      refetch();
      setIsDeleteConfirmOpen(false);
      toast({
        title: "오래된 로그 삭제 완료",
        description: "30일 이상 지난 로그가 삭제되었습니다.",
      });
    } catch (error) {
      logError("CLEANUP_LOGS_ERROR", error);
      toast({
        title: "로그 삭제 실패",
        description: "오래된 로그를 삭제하는 중 오류가 발생했습니다.",
        variant: "destructive",
      });
    }
  };

  const handleExport = async () => {
    try {
      const response = await fetch("/api/admin/logs/export");
      if (!response.ok) throw new Error("Failed to export logs");

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `system-logs-${formatDateTime(new Date())}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "내보내기 완료",
        description: "시스템 로그가 CSV 파일로 저장되었습니다.",
      });
    } catch (error) {
      logError("EXPORT_LOGS_ERROR", error);
      toast({
        title: "내보내기 실패",
        description: "시스템 로그를 내보내는 중 오류가 발생했습니다.",
        variant: "destructive",
      });
    }
  };

  const columns = [
    {
      header: "시간",
      accessor: (log: SystemLog) => formatDateTime(log.created_at),
    },
    {
      header: "레벨",
      accessor: (log: SystemLog) => {
        const Icon = getLogLevelIcon(log.level);
        return (
          <Badge className={getLogLevelColor(log.level)}>
            <Icon className="w-4 h-4 mr-1" />
            {log.level}
          </Badge>
        );
      },
    },
    {
      header: "작업",
      accessor: (log: SystemLog) => (
        <Button
          variant="link"
          className="p-0 h-auto font-normal"
          onClick={() => handleLogDetail(log)}
        >
          {log.action}
        </Button>
      ),
    },
    { header: "상세", accessor: "details" as keyof SystemLog },
    {
      header: "사용자",
      accessor: (log: SystemLog) =>
        log.profiles ? `${log.profiles.name} (${log.profiles.email})` : "-",
    },
    {
      header: "IP",
      accessor: "ip_address" as keyof SystemLog,
    },
    {
      header: "작업",
      accessor: (log: SystemLog) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleDeleteLog(log.id)}
        >
          삭제
        </Button>
      ),
    },
  ];

  return (
    <>
      <div className="flex justify-end mb-4 space-x-2">
        <Button variant="outline" onClick={() => setIsDeleteConfirmOpen(true)}>
          오래된 로그 삭제
        </Button>
      </div>

      <DataTable
        data={logs}
        columns={columns}
        searchTerm={searchTerm}
        onSearchChange={handleSearch}
        filterOptions={FILTER_OPTIONS}
        currentFilter={currentFilter}
        onFilterChange={handleFilter}
        onRefresh={refetch}
        onExport={handleExport}
        isLoading={isLoading}
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
      />

      <Dialog open={isDetailOpen} onOpenChange={setIsDetailOpen}>
        {selectedLog && (
          <DialogContent>
            <DialogHeader>
              <DialogTitle>로그 상세 정보</DialogTitle>
              <DialogDescription>
                {formatDateTime(selectedLog.created_at)}의 로그 정보입니다.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium">기본 정보</h4>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  <div>
                    <p className="text-sm text-muted-foreground">레벨</p>
                    <Badge className={getLogLevelColor(selectedLog.level)}>
                      {selectedLog.level}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">작업</p>
                    <p className="text-sm">{selectedLog.action}</p>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium">상세 정보</h4>
                <p className="text-sm mt-2">{selectedLog.details}</p>
              </div>

              {selectedLog.profiles && (
                <div>
                  <h4 className="text-sm font-medium">사용자 정보</h4>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    <div>
                      <p className="text-sm text-muted-foreground">이름</p>
                      <p className="text-sm">{selectedLog.profiles.name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">이메일</p>
                      <p className="text-sm">{selectedLog.profiles.email}</p>
                    </div>
                  </div>
                </div>
              )}

              <div>
                <h4 className="text-sm font-medium">시스템 정보</h4>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  <div>
                    <p className="text-sm text-muted-foreground">IP 주소</p>
                    <p className="text-sm">{selectedLog.ip_address}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">User Agent</p>
                    <p className="text-sm">{selectedLog.user_agent}</p>
                  </div>
                </div>
              </div>

              {selectedLog.metadata && (
                <div>
                  <h4 className="text-sm font-medium">추가 정보</h4>
                  <pre className="text-sm mt-2 whitespace-pre-wrap">
                    {JSON.stringify(selectedLog.metadata, null, 2)}
                  </pre>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDetailOpen(false)}>
                닫기
              </Button>
            </DialogFooter>
          </DialogContent>
        )}
      </Dialog>

      <Dialog open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>오래된 로그 삭제</DialogTitle>
            <DialogDescription>
              30일이 지난 로그를 모두 삭제하시겠습니까? 이 작업은 되돌릴 수
              없습니다.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteConfirmOpen(false)}
            >
              취소
            </Button>
            <Button variant="destructive" onClick={handleDeleteOldLogs}>
              삭제
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
