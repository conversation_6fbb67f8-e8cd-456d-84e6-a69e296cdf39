# 변경 이력

모든 주요 변경 사항이 이 파일에 기록됩니다.

형식은 [Keep a Changelog](https://keepachangelog.com/ko/1.0.0/)를 따르며,
이 프로젝트는 [Semantic Versioning](https://semver.org/lang/ko/)을 준수합니다.

## [0.1.0] - 2024-03-19

### 추가

- 프로젝트 초기 설정

  - Next.js 14 프로젝트 생성
  - TypeScript 설정
  - Tailwind CSS 설정
  - shadcn/ui 컴포넌트 설정

- 회원가입 기능

  - 이메일 기반 회원가입 폼 구현
  - 비밀번호 복잡성 검증 추가
  - 전화번호 형식 검증 추가
  - 폼 유효성 검사 구현
  - 에러 메시지 표시 기능

- 문서화
  - README.md 작성
  - API.md 작성
  - COMPONENTS.md 작성
  - CHANGELOG.md 작성

### 변경

- 없음

### 수정

- 없음

### 보안

- 없음

### 제거

- 없음

## [Unreleased]

### 계획된 기능

- [ ] 방문자 통계 대시보드
- [ ] 이메일 알림 시스템
- [ ] 모바일 앱 개발
- [ ] 다국어 지원 (영어)
- [ ] 방문자 사진 촬영 기능
- [ ] 체온 측정 기록
- [ ] 방문 예약 시스템

## [1.2.0] - 2024-01-15

### 추가됨

- 🎉 **방문자 등록 완료 후 회사 홍보 기능**

  - 등록 완료 시 "회사 소개 보기" 버튼 추가
  - SWK KOREA 웹사이트 (http://www.swkukorea.com/) 연결
  - 브랜딩 강화를 위한 시각적 요소 추가

- 📱 **수동 창 닫기 시스템**

  - 브라우저 호환성 문제 해결을 위해 자동 닫기 제거
  - 사용자가 직접 제어하는 "창 닫기" 버튼
  - 모바일 환경을 위한 안내 메시지 추가

- 🔍 **디버깅 시스템 강화**

  - 방문자 등록 과정의 상세 로그 추가
  - 방문자 기록 조회 과정의 디버깅 로그
  - `/debug/visitor-entries` 디버깅 페이지 생성

- 📚 **문서화 대폭 개선**
  - 상세한 API 문서 작성 (`docs/api.md`)
  - 데이터베이스 스키마 문서화 (`docs/database.md`)
  - 컴포넌트 가이드 작성 (`docs/components.md`)
  - 코드 주석 대폭 추가 (모든 주요 함수와 인터페이스)

### 변경됨

- 🔧 **RLS 정책 개선**

  - 농장 소유자도 방문자 기록 조회 가능하도록 정책 수정
  - 권한 확인 로직 강화

- 🎨 **UI/UX 개선**
  - 등록 완료 화면 디자인 개선
  - 브랜딩 요소 강화 (SWK KOREA 로고 및 색상)
  - 사용자 안내 메시지 개선

### 수정됨

- 🐛 **방문자 기록 조회 문제 해결**

  - RLS 정책으로 인한 조회 실패 문제 진단 및 해결
  - 농장 소유자 권한 확인 로직 수정
  - 디버깅 도구를 통한 문제 추적 시스템 구축

- 🔒 **보안 강화**
  - 입력 데이터 검증 강화
  - SQL 인젝션 방지 개선
  - 개인정보 보호 정책 준수

### 제거됨

- ❌ **자동 창 닫기 기능 제거**
  - 브라우저 호환성 문제로 인한 제거
  - 카운트다운 타이머 관련 코드 정리
  - "다른 방문자 등록하기" 버튼 제거 (사용자 요청)
