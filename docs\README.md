# 농장 방문자 관리 시스템

## 📋 프로젝트 개요

농장 방문자 관리 시스템은 QR 코드 기반의 현대적인 방문자 등록 및 관리 솔루션입니다. 농장 보안과 방역을 강화하기 위해 개발되었습니다.

### 주요 기능

- 🏢 **농장 관리**: 다중 농장 등록 및 관리
- 👥 **구성원 관리**: 농장별 소유자, 관리자, 조회자 권한 관리
- 📱 **QR 코드 방문 등록**: 모바일 친화적 방문자 등록 시스템
- 📊 **방문자 기록 조회**: 실시간 방문자 현황 및 통계
- 🔒 **보안 및 권한**: Row Level Security(RLS) 기반 데이터 보안
- 🌐 **주소 검색**: 카카오 주소 API 연동

### 기술 스택

- **Frontend**: Next.js 14, React, TypeScript
- **Backend**: Supabase (PostgreSQL, Auth, RLS)
- **UI**: Tailwind CSS, shadcn/ui
- **외부 API**: 카카오 주소 검색 API
- **배포**: Vercel (예정)

## 🚀 빠른 시작

### 필수 요구사항

- Node.js 18.0 이상
- npm 또는 yarn
- Supabase 계정

### 설치 및 실행

1. **저장소 클론**

```bash
git clone <repository-url>
cd farm-visitor-management
```

2. **의존성 설치**

```bash
npm install
# 또는
yarn install
```

3. **환경 변수 설정**

```bash
cp .env.example .env.local
```

`.env.local` 파일에 다음 값들을 설정:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. **데이터베이스 설정**

```bash
# Supabase 테이블 생성
psql -f scripts/create-visitor-entries-table.sql
```

5. **개발 서버 실행**

```bash
npm run dev
# 또는
yarn dev
```

6. **브라우저에서 확인**

```
http://localhost:3000
```

## 📁 프로젝트 구조

```
farm-visitor-management/
├── app/                          # Next.js App Router
│   ├── admin/                    # 관리자 페이지
│   │   ├── dashboard/           # 대시보드
│   │   ├── farms/               # 농장 관리
│   │   │   ├── [farmId]/       # 개별 농장 관리
│   │   │   │   ├── members/    # 구성원 관리
│   │   │   │   └── visitors/   # 방문자 기록 조회
│   │   │   └── page.tsx        # 농장 목록
│   │   └── layout.tsx          # 관리자 레이아웃
│   ├── visit/                   # 방문자 등록
│   │   └── [farmId]/           # 농장별 방문자 등록 폼
│   ├── auth/                    # 인증 관련
│   ├── globals.css             # 전역 스타일
│   ├── layout.tsx              # 루트 레이아웃
│   └── page.tsx                # 홈페이지
├── components/                  # 재사용 가능한 컴포넌트
│   ├── ui/                     # shadcn/ui 컴포넌트
│   ├── address-search.tsx      # 주소 검색 컴포넌트
│   └── sidebar.tsx             # 사이드바 컴포넌트
├── hooks/                      # 커스텀 훅
├── lib/                        # 유틸리티 및 설정
│   ├── supabase.ts            # Supabase 클라이언트
│   └── utils.ts               # 공통 유틸리티
├── store/                      # 상태 관리
├── scripts/                    # 데이터베이스 스크립트
├── docs/                       # 문서
└── public/                     # 정적 파일
```

## 📚 문서 가이드

### 🚀 개발 환경 설정 (필수)

- **[Supabase 로컬 환경 설정 가이드](./supabase-local-setup.md)** - 완전한 로컬 개발 환경 구축 가이드
- **[빠른 설정 체크리스트](./quick-setup-checklist.md)** - 5분 빠른 환경 설정 및 문제 해결

### 🔗 기존 문서 링크

- [API 문서](./api.md)
- [데이터베이스 스키마](./database.md)
- [컴포넌트 가이드](./components.md)
- [배포 가이드](./deployment.md)
- [보안 가이드](./security.md)

### 📋 새로운 개발자를 위한 순서

1. **[빠른 설정 체크리스트](./quick-setup-checklist.md)** - 5분 환경 설정
2. **[Supabase 로컬 환경 설정](./supabase-local-setup.md)** - 상세 설정 가이드
3. 위의 기존 문서들 참조

## 🤝 기여하기

1. Fork the Project
2. Create your Feature Branch (`git checkout -b feature/AmazingFeature`)
3. Commit your Changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the Branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 라이선스

이 프로젝트는 MIT 라이선스 하에 배포됩니다. 자세한 내용은 `LICENSE` 파일을 참조하세요.

## 📞 지원

문의사항이 있으시면 다음으로 연락주세요:

- 이메일: <EMAIL>
- 웹사이트: http://www.swkukorea.com/

---

**Powered by SWK KOREA** 🌐
