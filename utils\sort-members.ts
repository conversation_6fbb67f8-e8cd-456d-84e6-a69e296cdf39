// 구성원 정렬 유틸리티 함수

export interface MemberWithRole {
  role: string;
  created_at: string;
  [key: string]: any;
}

/**
 * 구성원을 권한별 우선순위로 정렬하는 함수
 * @param members 정렬할 구성원 배열
 * @returns 정렬된 구성원 배열
 */
export function sortMembersByRole<T extends MemberWithRole>(members: T[]): T[] {
  return members.sort((a, b) => {
    // 권한별 우선순위 정의
    const roleOrder: Record<string, number> = {
      owner: 1,    // 🛡️ 농장 소유자 (최우선)
      manager: 2,  // 👨‍💼 관리자
      viewer: 3,   // 👁️ 조회자
    };

    const aOrder = roleOrder[a.role] || 999; // 알 수 없는 권한은 맨 뒤로
    const bOrder = roleOrder[b.role] || 999;

    // 권한이 다르면 권한 순서로 정렬
    if (aOrder !== bOrder) {
      return aOrder - bOrder;
    }

    // 같은 권한이면 생성일 순서로 정렬 (먼저 가입한 사람이 위로)
    return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
  });
}

/**
 * 권한별 우선순위 숫자 반환
 * @param role 권한 문자열
 * @returns 우선순위 숫자 (낮을수록 높은 우선순위)
 */
export function getRolePriority(role: string): number {
  const roleOrder: Record<string, number> = {
    owner: 1,
    manager: 2,
    viewer: 3,
  };
  return roleOrder[role] || 999;
}

/**
 * 권한별 표시 이름 반환
 * @param role 권한 문자열
 * @returns 한글 권한명
 */
export function getRoleDisplayName(role: string): string {
  const roleNames: Record<string, string> = {
    owner: "농장 소유자",
    manager: "관리자", 
    viewer: "조회자",
  };
  return roleNames[role] || role;
}

/**
 * 권한별 이모지 반환
 * @param role 권한 문자열
 * @returns 권한 이모지
 */
export function getRoleEmoji(role: string): string {
  const roleEmojis: Record<string, string> = {
    owner: "🛡️",
    manager: "👨‍💼",
    viewer: "👁️",
  };
  return roleEmojis[role] || "👤";
}
