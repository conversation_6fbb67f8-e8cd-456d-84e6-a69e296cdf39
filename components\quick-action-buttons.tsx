"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  ArrowUp,
  ArrowDown,
  UserMinus,
  MoreHorizontal,
  Shield,
  UserCheck,
  Eye,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { UserRole, getRoleLevel } from "@/components/role-badge";

interface QuickActionButtonsProps {
  memberRole: UserRole;
  memberId: string;
  memberName: string;
  onPromote?: (memberId: string, newRole: UserRole) => void;
  onDemote?: (memberId: string, newRole: UserRole) => void;
  onDelete?: (memberId: string) => void;
  disabled?: boolean;
  canManageMembers?: boolean; // 현재 사용자가 구성원을 관리할 권한이 있는지
}

export function QuickActionButtons({
  memberRole,
  memberId,
  memberName,
  onPromote,
  onDemote,
  onDelete,
  disabled = false,
  canManageMembers = false,
}: QuickActionButtonsProps) {
  // 권한 레벨에 따른 승격/강등 가능 여부
  const canPromote = memberRole === "viewer"; // viewer -> manager
  const canDemote = memberRole === "manager"; // manager -> viewer
  const canDelete = memberRole !== "owner"; // 소유자는 삭제 불가

  const handlePromote = () => {
    if (memberRole === "viewer" && onPromote) {
      onPromote(memberId, "manager");
    }
  };

  const handleDemote = () => {
    if (memberRole === "manager" && onDemote) {
      onDemote(memberId, "viewer");
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(memberId);
    }
  };

  // 소유자는 액션 버튼 표시하지 않음
  if (memberRole === "owner") {
    return null;
  }

  // 구성원 관리 권한이 없으면 버튼 표시하지 않음
  if (!canManageMembers) {
    return null;
  }

  return (
    <div className="flex items-center gap-1">
      {/* 승격 버튼 */}
      {canPromote && (
        <Button
          variant="outline"
          size="sm"
          onClick={handlePromote}
          disabled={disabled}
          className="h-8 px-2 text-xs"
          title="관리자로 승격"
        >
          <ArrowUp className="h-3 w-3 mr-1" />
          승격
        </Button>
      )}

      {/* 강등 버튼 */}
      {canDemote && (
        <Button
          variant="outline"
          size="sm"
          onClick={handleDemote}
          disabled={disabled}
          className="h-8 px-2 text-xs"
          title="조회자로 강등"
        >
          <ArrowDown className="h-3 w-3 mr-1" />
          강등
        </Button>
      )}

      {/* 더 많은 액션 드롭다운 */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            disabled={disabled}
            className="h-8 w-8 p-0"
            title="더 많은 액션"
          >
            <MoreHorizontal className="h-3 w-3" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          {/* 권한 직접 변경 */}
          {memberRole !== "manager" && (
            <DropdownMenuItem onClick={() => onPromote?.(memberId, "manager")}>
              <UserCheck className="h-4 w-4 mr-2" />
              관리자로 변경
            </DropdownMenuItem>
          )}

          {memberRole !== "viewer" && (
            <DropdownMenuItem onClick={() => onDemote?.(memberId, "viewer")}>
              <Eye className="h-4 w-4 mr-2" />
              조회자로 변경
            </DropdownMenuItem>
          )}

          {canDelete && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleDelete}
                className="text-destructive focus:text-destructive"
              >
                <UserMinus className="h-4 w-4 mr-2" />
                구성원 제거
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

// 일괄 액션 버튼 컴포넌트
interface BulkActionButtonsProps {
  selectedMembers: string[];
  onBulkPromote?: (memberIds: string[]) => void;
  onBulkDemote?: (memberIds: string[]) => void;
  onBulkDelete?: (memberIds: string[]) => void;
  onClearSelection?: () => void;
}

export function BulkActionButtons({
  selectedMembers,
  onBulkPromote,
  onBulkDemote,
  onBulkDelete,
  onClearSelection,
}: BulkActionButtonsProps) {
  if (selectedMembers.length === 0) {
    return null;
  }

  return (
    <div className="flex items-center gap-2 p-3 bg-accent rounded-lg border">
      <span className="text-sm font-medium">
        {selectedMembers.length}명 선택됨
      </span>

      <div className="flex items-center gap-1 ml-auto">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onBulkPromote?.(selectedMembers)}
          className="h-8 px-2 text-xs"
        >
          <ArrowUp className="h-3 w-3 mr-1" />
          일괄 승격
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={() => onBulkDemote?.(selectedMembers)}
          className="h-8 px-2 text-xs"
        >
          <ArrowDown className="h-3 w-3 mr-1" />
          일괄 강등
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={() => onBulkDelete?.(selectedMembers)}
          className="h-8 px-2 text-xs text-destructive hover:text-destructive"
        >
          <UserMinus className="h-3 w-3 mr-1" />
          일괄 삭제
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={onClearSelection}
          className="h-8 px-2 text-xs"
        >
          선택 해제
        </Button>
      </div>
    </div>
  );
}
