"use client"

import { useState, useEffect } from "react"
import { Menu, X, ChevronRight } from "lucide-react"
import { SidebarTrigger, useSidebar } from "@/components/ui/sidebar"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"

interface MenuButtonProps {
  className?: string
}

export function MenuButton({ className }: MenuButtonProps) {
  const [showPulse, setShowPulse] = useState(false)
  const [isFirstVisit, setIsFirstVisit] = useState(false)
  const [showTooltip, setShowTooltip] = useState(false)
  const { open, isMobile } = useSidebar()

  useEffect(() => {
    // 첫 방문 여부 확인 (로컬 스토리지 사용)
    const hasVisitedBefore = localStorage.getItem("hasVisitedDashboard")

    if (!hasVisitedBefore) {
      setIsFirstVisit(true)
      setShowPulse(true)
      setShowTooltip(true)
      localStorage.setItem("hasVisitedDashboard", "true")

      // 8초 후에 펄스 효과와 툴팁 제거
      const timer = setTimeout(() => {
        setShowPulse(false)
        setShowTooltip(false)
      }, 8000)

      return () => clearTimeout(timer)
    }
  }, [])

  // 사이드바 상태에 따른 스타일 결정
  const isOpen = isMobile ? false : open // 모바일에서는 항상 닫힌 상태로 표시
  const buttonVariant = isOpen ? "opened" : "closed"

  return (
    <div className="relative">
      {/* 첫 방문 시 툴팁 */}
      <AnimatePresence>
        {showTooltip && isMobile && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.8 }}
            className="absolute -top-16 left-1/2 transform -translate-x-1/2 z-50"
          >
            <div className="bg-primary text-primary-foreground px-3 py-2 rounded-lg text-sm font-medium shadow-lg">
              <div className="flex items-center gap-2">
                <Menu className="h-4 w-4" />
                <span>여기를 눌러 메뉴를 열어보세요!</span>
              </div>
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-primary"></div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <SidebarTrigger
        className={cn(
          "relative flex items-center justify-center gap-3 transition-all duration-300 shadow-lg hover:shadow-xl",
          // 모바일에서 더 큰 크기와 명확한 스타일
          isMobile
            ? "px-4 py-3 min-h-[52px] min-w-[120px] bg-primary text-primary-foreground hover:bg-primary/90 rounded-xl border-2 border-primary-foreground/20"
            : "px-3 py-2 rounded-lg",
          // 데스크톱에서의 상태별 스타일
          !isMobile && isOpen
            ? "bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-primary/20"
            : !isMobile && "bg-primary text-primary-foreground hover:bg-primary/90",
          showPulse && "menu-pulse-enhanced",
          className,
        )}
        aria-label={isOpen ? "메뉴 닫기" : "메뉴 열기"}
        data-state={buttonVariant}
      >
        {/* 아이콘 영역 */}
        <div className="relative flex items-center justify-center">
          <AnimatePresence mode="wait">
            {isOpen && !isMobile ? (
              <motion.div
                key="close"
                initial={{ rotate: -90, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                exit={{ rotate: 90, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <X className={cn("h-5 w-5", isMobile && "h-6 w-6")} />
              </motion.div>
            ) : (
              <motion.div
                key="menu"
                initial={{ rotate: 90, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                exit={{ rotate: -90, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="flex items-center"
              >
                <Menu className={cn("h-5 w-5", isMobile && "h-6 w-6")} />
                {isMobile && (
                  <motion.div
                    animate={{ x: [0, 4, 0] }}
                    transition={{ duration: 1.5, repeat: Number.POSITIVE_INFINITY }}
                  >
                    <ChevronRight className="h-4 w-4 ml-1 opacity-70" />
                  </motion.div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* 텍스트 라벨 */}
        <motion.div
          className="flex flex-col items-start"
          animate={{
            color: isOpen && !isMobile ? "var(--secondary-foreground)" : "var(--primary-foreground)",
          }}
          transition={{ duration: 0.2 }}
        >
          <span className={cn("font-semibold", isMobile ? "text-base" : "text-sm")}>
            {isOpen && !isMobile ? "닫기" : "메뉴"}
          </span>
          {isMobile && <span className="text-xs opacity-80 leading-tight">탭하여 열기</span>}
        </motion.div>

        {/* 상태 표시 인디케이터 (데스크톱만) */}
        {!isMobile && (
          <motion.div
            className={cn("absolute -top-1 -right-1 w-3 h-3 rounded-full", isOpen ? "bg-green-500" : "bg-blue-500")}
            animate={{
              scale: isOpen ? [1, 1.2, 1] : [1, 0.8, 1],
              opacity: [0.7, 1, 0.7],
            }}
            transition={{
              duration: 2,
              repeat: Number.POSITIVE_INFINITY,
              ease: "easeInOut",
            }}
          />
        )}

        {/* 첫 방문 시 알림 표시 */}
        {isFirstVisit && (
          <motion.div
            className="absolute -top-2 -right-2 flex h-6 w-6 items-center justify-center"
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY }}
          >
            <div className="absolute h-full w-full rounded-full bg-red-500 animate-ping opacity-75"></div>
            <div className="relative h-4 w-4 rounded-full bg-red-500 flex items-center justify-center">
              <span className="text-white text-xs font-bold">!</span>
            </div>
          </motion.div>
        )}
      </SidebarTrigger>

      {/* 모바일에서 추가 설명 텍스트 */}
      {isMobile && isFirstVisit && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 10 }}
          className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground text-center whitespace-nowrap"
        >
          농장관리, 방문자기록 등
        </motion.div>
      )}
    </div>
  )
}
