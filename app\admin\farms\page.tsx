"use client";

import type React from "react";
import { useState, useMemo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { farmFormSchema, type FarmFormValues } from "@/lib/validations/farm";
import {
  FARM_TYPE_OPTIONS,
  getFarmTypeLabel,
  getFarmTypeIcon,
  getFarmTypeColor,
} from "@/lib/constants/farm-types";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useFarms } from "@/hooks/use-farms";
import { useAuth } from "@/components/providers/auth-provider";
import { AddressSearch } from "@/components/address-search";
import { useFarmMembersPreview } from "@/hooks/use-farm-members-preview-safe";
import { FarmMembersPreview } from "@/components/farm-members-preview";
import {
  Plus,
  QrCode,
  Edit,
  Trash2,
  MapPin,
  Phone,
  Copy,
  CheckCircle,
  Users,
  Home,
  Building2,
} from "lucide-react";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { motion } from "framer-motion";
import { FarmQRCode } from "@/components/farm-qr-code";
import Link from "next/link";
import { Suspense } from "react";
import { MemberManagementDialog } from "@/components/member-management-dialog";

export default function FarmsPage() {
  const { farms, loading, addFarm, updateFarm, deleteFarm } = useFarms();
  const { profile, loading: authLoading } = useAuth();

  // 현재 사용자가 특정 농장의 소유자인지 확인하는 함수
  const isOwner = (farm: any) => {
    return profile?.id === farm.owner_id;
  };

  // 농장 ID 목록 추출 (메모이제이션으로 무한 루프 방지)
  const farmIds = useMemo(() => farms.map((farm) => farm.id), [farms]);
  const { getMembersForFarm } = useFarmMembersPreview(farmIds);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [farmToDelete, setFarmToDelete] = useState<string | null>(null);
  const [editingFarm, setEditingFarm] = useState<any>(null);
  const [selectedFarmId, setSelectedFarmId] = useState<string | null>(null);
  const [memberDialogOpen, setMemberDialogOpen] = useState(false);

  const form = useForm<FarmFormValues>({
    resolver: zodResolver(farmFormSchema),
    defaultValues: {
      farm_name: "",
      farm_address: "",
      farm_detailed_address: "",
      farm_type: undefined,
      description: "",
      manager_name: "",
      manager_phone: "",
    },
  });

  const handleSubmit = async (values: FarmFormValues) => {
    if (editingFarm) {
      await updateFarm(editingFarm.id, values);
    } else {
      await addFarm(values);
    }

    setDialogOpen(false);
    setEditingFarm(null);
    form.reset({
      farm_name: "",
      farm_address: "",
      farm_detailed_address: "",
      farm_type: undefined,
      description: "",
      manager_name: "",
      manager_phone: "",
    });
  };

  const handleEdit = (farm: any) => {
    setEditingFarm(farm);
    form.reset({
      farm_name: farm.farm_name,
      farm_address: farm.farm_address,
      farm_detailed_address: farm.farm_detailed_address,
      farm_type: farm.farm_type || undefined,
      description: farm.description || "",
      manager_name: farm.manager_name || "",
      manager_phone: farm.manager_phone || "",
    });
    setDialogOpen(true);
  };

  const handleDelete = async (farmId: string) => {
    setFarmToDelete(farmId);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (farmToDelete) {
      await deleteFarm(farmToDelete);
      setDeleteDialogOpen(false);
      setFarmToDelete(null);
    }
  };

  const handleMemberManagement = (farmId: string) => {
    setSelectedFarmId(farmId);
    setMemberDialogOpen(true);
  };

  if (authLoading || loading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>데이터를 불러오는 중...</p>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">로그인이 필요합니다</h2>
          <p className="text-muted-foreground">
            이 페이지를 보려면 먼저 로그인해주세요.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-6 pt-2 md:pt-4">
      {/* 브레드크럼 네비게이션 */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/admin/dashboard" className="flex items-center gap-1">
                <Home className="h-4 w-4" />
                대시보드
              </Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>농장 관리</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <SidebarTrigger />
          <div>
            <h2 className="text-3xl font-bold tracking-tight mb-4 flex items-center gap-2">
              <Building2 className="h-8 w-8 text-primary" />
              농장 관리
            </h2>
            <p className="text-muted-foreground mt-2">
              등록된 농장을 관리하고 QR 코드를 생성하세요
            </p>
          </div>
        </div>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button
              onClick={() => {
                setEditingFarm(null);
                form.reset({
                  farm_name: "",
                  farm_address: "",
                  farm_detailed_address: "",
                  farm_type: undefined,
                  description: "",
                  manager_name: "",
                  manager_phone: "",
                });
              }}
            >
              <Plus className="mr-2 h-4 w-4" />
              농장 추가
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>
                {editingFarm ? "농장 수정" : "새 농장 등록"}
              </DialogTitle>
              <DialogDescription>
                {editingFarm
                  ? "농장 정보를 수정하세요"
                  : "새로운 농장을 등록하세요"}
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(handleSubmit)}
                className="space-y-4"
              >
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="farm_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>농장명 *</FormLabel>
                        <FormControl>
                          <Input placeholder="그린팜 1농장" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="farm_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>농장 유형 *</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="농장 유형을 선택하세요" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {FARM_TYPE_OPTIONS.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="farm_address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>농장 주소 *</FormLabel>
                      <FormControl>
                        <div className="space-y-2">
                          <AddressSearch
                            onSelect={(address, detailedAddress) => {
                              field.onChange(address);
                              form.setValue(
                                "farm_detailed_address",
                                detailedAddress
                              );
                            }}
                            defaultDetailedAddress={
                              form.getValues("farm_detailed_address") || ""
                            }
                          />
                          <div className="space-y-2">
                            <Textarea
                              placeholder="주소 검색 버튼을 클릭하여 주소를 입력하세요"
                              {...field}
                              readOnly
                            />
                            <FormField
                              control={form.control}
                              name="farm_detailed_address"
                              render={({ field: detailField }) => (
                                <FormItem>
                                  <FormControl>
                                    <Input
                                      placeholder="상세 주소를 입력하세요 (예: 101동 1234호)"
                                      {...detailField}
                                      value={detailField.value || ""}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="manager_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>관리자명 *</FormLabel>
                        <FormControl>
                          <Input placeholder="홍길동" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="manager_phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>관리자 연락처 *</FormLabel>
                        <FormControl>
                          <Input
                            type="tel"
                            placeholder="010-1234-5678"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>농장 설명</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="농장에 대한 설명을 입력하세요"
                          {...field}
                          value={field.value || ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setDialogOpen(false)}
                  >
                    취소
                  </Button>
                  <Button type="submit">{editingFarm ? "수정" : "등록"}</Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      {/* 삭제 확인 다이얼로그 */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>농장 삭제 확인</DialogTitle>
            <DialogDescription>
              정말로 이 농장을 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex space-x-2 justify-end">
            <Button
              type="button"
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
            >
              취소
            </Button>
            <Button type="button" variant="destructive" onClick={confirmDelete}>
              삭제
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 농장 목록 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {farms.map((farm, index) => (
          <motion.div
            key={farm.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <Card className="card-hover">
              <CardHeader className="space-y-3 pb-3">
                {/* 농장 정보 */}
                <div className="flex items-start justify-between">
                  <div className="space-y-1 flex-1 min-w-0">
                    <CardTitle className="text-lg font-bold truncate">
                      {farm.farm_name}
                    </CardTitle>
                    <Badge
                      variant="outline"
                      className={`text-xs ${getFarmTypeColor(farm.farm_type)}`}
                    >
                      <div className="flex items-center gap-1">
                        {(() => {
                          const Icon = getFarmTypeIcon(farm.farm_type);
                          return <Icon className="h-3 w-3" />;
                        })()}
                        {getFarmTypeLabel(farm.farm_type)}
                      </div>
                    </Badge>
                  </div>
                  {/* 소유자가 아닌 경우 안내 - 모바일에서 상단에 표시 */}
                  {!isOwner(farm) && (
                    <div className="text-xs text-muted-foreground px-2 py-1 bg-gray-50 rounded whitespace-nowrap ml-2">
                      💡 소유자만 수정/삭제 가능
                    </div>
                  )}
                </div>

                {/* 액션 버튼들 - 모바일에서 2줄로 배치 */}
                <div className="flex flex-col gap-2">
                  {/* 첫 번째 줄: 주요 액션 */}
                  <div className="flex gap-2">
                    <FarmQRCode farmId={farm.id} farmName={farm.farm_name} />
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 min-w-0"
                      asChild
                    >
                      <Link
                        href={`/admin/farms/${farm.id}/members`}
                        className="flex items-center justify-center gap-2"
                      >
                        <Users className="h-4 w-4" />
                        <span className="hidden sm:inline">구성원</span>
                      </Link>
                    </Button>
                  </div>

                  {/* 두 번째 줄: 관리 액션 (소유자만) */}
                  {isOwner(farm) && (
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(farm)}
                        className="flex-1 min-w-0"
                        title="농장 정보 수정"
                      >
                        <Edit className="h-4 w-4 sm:mr-2" />
                        <span className="hidden sm:inline">수정</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(farm.id)}
                        className="flex-1 min-w-0 text-red-600 hover:text-red-700"
                        title="농장 삭제"
                      >
                        <Trash2 className="h-4 w-4 sm:mr-2" />
                        <span className="hidden sm:inline">삭제</span>
                      </Button>
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex items-start space-x-2">
                    <MapPin className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                    <span className="text-muted-foreground">
                      {farm.farm_address}
                      {farm.farm_detailed_address && (
                        <>
                          <br />
                          <span className="text-xs">
                            {farm.farm_detailed_address}
                          </span>
                        </>
                      )}
                    </span>
                  </div>
                  {farm.manager_name && (
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                      <span className="text-muted-foreground">
                        관리자: {farm.manager_name}
                      </span>
                    </div>
                  )}
                  {farm.manager_phone && (
                    <div className="flex items-center space-x-2">
                      <Phone className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                      <span className="text-muted-foreground">
                        {farm.manager_phone}
                      </span>
                    </div>
                  )}
                  {farm.description && (
                    <div className="bg-accent p-2 rounded text-xs">
                      <p>{farm.description}</p>
                    </div>
                  )}

                  {/* 구성원 미리보기 */}
                  <FarmMembersPreview
                    memberCount={getMembersForFarm(farm.id).count}
                    members={getMembersForFarm(farm.id).members}
                    loading={getMembersForFarm(farm.id).loading}
                  />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {farms.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <div className="text-center space-y-4">
                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                  <Plus className="h-8 w-8 text-primary" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold">
                    등록된 농장이 없습니다
                  </h3>
                  <p className="text-muted-foreground">
                    첫 번째 농장을 등록하여 시작하세요
                  </p>
                </div>
                <Button onClick={() => setDialogOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />첫 농장 등록하기
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {selectedFarmId && (
        <MemberManagementDialog
          farmId={selectedFarmId}
          open={memberDialogOpen}
          onOpenChange={setMemberDialogOpen}
          onMemberAdded={() => {
            // 필요한 경우 농장 목록을 새로고침하는 로직 추가
          }}
        />
      )}
    </div>
  );
}
