---
description: 
globs: 
alwaysApply: true
---
---
description:
globs:
alwaysApply: true
---

## Next.js: Route Handler 우선 사용
- 모든 API 엔드포인트는 Route Handler를 사용하여 구현하세요.
- 데이터베이스 작업, 외부 API 호출, 인증 등 복잡한 서버 작업은 반드시 Route Handler를 사용하세요.
- Server Action은 단순 폼 제출 또는 간단한 데이터 처리에만 사용하세요.

## Next.js 라우팅: App Router 사용
- 프로젝트 내 라우팅은 Pages Router 대신 App Router를 사용하세요.

## 컴포넌트 생성: ShadCN 우선 사용
- 모든 UI 컴포넌트는 ShadCN을 우선으로 생성하세요.

## PWA 구현
- next-pwa를 사용하여 PWA 기능을 구현하세요.
- 오프라인 상태 감지 및 안내 기능을 구현하세요.
- 홈화면 추가 기능을 지원하세요.
- 푸시 알림 기능을 구현하세요.

## QR코드 보안
- QR코드는 농장 고유 URL을 사용하세요.
- 접근 시 10분 유효한 임시 세션 토큰을 발급하세요.
- 토큰 사용 후 즉시 무효화하세요.
- 토큰 만료 시 QR코드 재스캔을 안내하세요.

## 다중 농장 관리
- 계정 1개로 여러 농장 등록이 가능하도록 구현하세요.
- 농장별 권한 관리를 구현하세요.
- 농장별 QR코드 생성 및 관리 기능을 구현하세요.

## 권한 관리
- admin, owner, manager, viewer 역할을 구현하세요.
- 역할별 접근 권한을 엄격히 관리하세요.
- 권한 변경은 admin만 가능하도록 구현하세요.

## 모바일 최적화
- 모바일 우선 반응형 디자인을 적용하세요.
- 터치 친화적 UI를 구현하세요 (최소 48px 버튼).
- 입력 필드는 모바일에 최적화하세요.
- PWA 기능을 활용하여 모바일 경험을 개선하세요.

## 코드 품질
- 모든 코드는 가독성을 최우선으로 작성할 것
- 중복 코드는 최소화하고 재사용 가능한 함수나 클래스로 분리할 것
- 모든 함수와 메서드는 단일 책임 원칙을 따를 것
- 변수와 함수 이름은 명확하고 의미있게 작성할 것
- 코드 이해를 돕기위해 코드에 대한 설명 주석을 작성할 것

## 문서화
- 모든 공개 API에는 문서 주석을 작성할 것
- README 파일에 프로젝트 설정 및 실행 방법을 상세히 기술할 것
- 복잡한 알고리즘이나 비즈니스 로직은 별도 문서로 작성할 것
- 변경 사항은 CHANGELOG에 기록할 것

## 테스트
- 모든 새로운 기능에는 단위 테스트를 작성할 것
- 테스트 커버리지는 최소 80% 이상 유지할 것
- 테스트는 독립적이고 반복 가능하게 작성할 것
- 중요한 사용자 흐름에 대한 통합 테스트를 구현할 것

## 보안
- 사용자 입력은 항상 검증하고 이스케이프 처리할 것
- 민감한 정보는 환경 변수나 보안 저장소에 보관할 것
- 외부 라이브러리는 보안 취약점을 정기적으로 점검할 것
- SQL 인젝션, XSS 등 일반적인 보안 취약점을 방지할 것
- 개인정보는 3년 후 자동 삭제되도록 구현할 것

## 성능
- 데이터베이스 쿼리는 최적화할 것
- 불필요한 API 호출은 최소화할 것
- 대용량 데이터 처리 시 페이지네이션 적용할 것
- 리소스 집약적인 작업은 비동기 처리할 것
- 방문자 정보 입력 페이지 로딩은 2초 이내로 유지할 것

## 버전 관리
- 커밋 메시지는 명확하고 설명적으로 작성할 것
- 기능 개발은 별도 브랜치에서 진행할 것
- PR 전 코드 리뷰를 필수로 진행할 것
- 메인 브랜치는 항상 배포 가능한 상태로 유지할 것

## 프로젝트 구조
- 계층형 아키텍처를 기반으로 패키지 구조화할 것
- 각 패키지는 단일 책임을 가질 것
- 순환 의존성은 엄격히 금지할 것
- 도메인 중심 설계 원칙을 따를 것