@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 142 72% 29%;
    --primary-foreground: 210 40% 98%;

    --secondary: 142 55% 90%;
    --secondary-foreground: 142 76% 20%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 142 55% 96%;
    --accent-foreground: 142 76% 20%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 142 72% 29%;

    --radius: 1rem;
  }

  .dark {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 142 70% 50%;
    --primary-foreground: 222 47% 11%;

    --secondary: 142 30% 30%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 142 30% 20%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 142 70% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* 한글 폰트 최적화 */
  html {
    font-family: "Pretendard Variable", -apple-system, BlinkMacSystemFont,
      system-ui, Roboto, "Helvetica Neue", "Segoe UI", "Apple SD Gothic Neo",
      "Noto Sans KR", "Malgun Gothic", sans-serif;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* 애니메이션 및 전환 효과 */
.card-hover {
  @apply transition-all duration-200 hover:shadow-soft-lg hover:-translate-y-1;
}

.btn-hover {
  @apply transition-all duration-200 hover:shadow-md hover:-translate-y-0.5;
}

.input-focus {
  @apply transition-all duration-200 focus:shadow-inner-soft;
}

/* 그라데이션 배경 */
.bg-gradient-farm {
  @apply bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50;
}

/* 모바일 최적화 */
@media (max-width: 640px) {
  .mobile-padding {
    @apply px-4 py-3;
  }

  .mobile-text {
    @apply text-sm;
  }
}

/* 태블릿 최적화 */
@media (min-width: 768px) and (max-width: 1023px) {
  .tablet-padding {
    @apply px-6 py-4;
  }

  .tablet-text {
    @apply text-base;
  }

  /* 태블릿용 플로팅 버튼 최적화 */
  .tablet-fab {
    @apply w-16 h-16 shadow-xl;
  }

  .tablet-fab:hover {
    @apply shadow-2xl scale-105;
  }
}

/* 모바일 + 태블릿 공통 */
@media (max-width: 1023px) {
  .mobile-tablet-padding {
    @apply px-4 py-3;
  }

  .mobile-tablet-text {
    @apply text-sm;
  }
}

/* PWA 최적화 */
@media (display-mode: standalone) {
  body {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}

/* CSS 애니메이션 추가 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp 0.5s ease-out both;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.5s ease-out both;
}
