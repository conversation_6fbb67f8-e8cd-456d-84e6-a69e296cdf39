# API 및 기능 명세서

## 📋 **개요**

**문서 목적**: 농장 방문자 관리 시스템의 API 엔드포인트 및 주요 기능 명세  
**대상 독자**: 개발자, 시스템 관리자, API 사용자  
**최종 업데이트**: 2025-06-17  
**버전**: 2.0.0

## 🔄 주요 변경사항

- **보안 강화**: JWT 인증 개선, RLS 정책 최적화
- **성능 개선**: 데이터베이스 쿼리 최적화, 인덱스 추가
- **기능 확장**: 농장 관리, 구성원 관리, 방문자 관리 기능 강화
- **문서화 개선**: API 엔드포인트 상세 명세 추가

## 🔗 **API 엔드포인트**

### **1. 인증 관련 API**

#### **POST /api/auth/login**

```typescript
// 사용자 로그인
Request: {
  email: string;
  password: string;
}

Response: {
  user: User;
  session: Session;
  access_token: string;
}
```

#### **POST /api/auth/register**

```typescript
// 사용자 회원가입
Request: {
  email: string;          // 이메일 (중복 확인됨)
  password: string;       // 8자 이상, 대문자/소문자/숫자/특수문자 필수
  name: string;           // 2자 이상
  phone?: string;         // 010-XXXX-XXXX 형식
  company_name?: string;  // 선택사항
}

Response: {
  user: User;
  message: string;
  email_verified: boolean;
}
```

#### **GET /api/auth/check-email?email=<EMAIL>**

```typescript
// 이메일 중복 확인
Response: {
  available: boolean; // 사용 가능 여부
  message: string; // 상세 메시지
  exists: boolean; // 기존 사용자 존재 여부
}
```

#### **POST /api/auth/logout**

```typescript
// 사용자 로그아웃
Response: {
  message: string;
}
```

### **2. 사용자 관리 API**

#### **GET /api/users**

```typescript
// 사용자 목록 조회 (관리자 전용)
// 인증: Bearer 토큰 필요 (관리자 권한)
// 페이지네이션 및 필터링 지원

Query: {
  page?: number;          // 페이지 번호 (기본값: 1)
  limit?: number;         // 페이지당 항목 수 (기본값: 20, 최대: 100)
  role?: 'admin' | 'user' | 'farm_owner' | 'farm_member';
  search?: string;        // 이름, 이메일로 검색
  is_active?: boolean;     // 활성 사용자만 조회
  sort_by?: 'name' | 'email' | 'created_at';
  order?: 'asc' | 'desc';
}

Response: {
  success: boolean;
  data: {
    users: Array<{
      id: string;
      email: string;
      name: string;
      account_type: string;
      is_active: boolean;
      last_login_at: string | null;
      login_count: number;
      created_at: string;
      updated_at: string;
    }>;
    pagination: {
      total: number;
      page: number;
      limit: number;
      total_pages: number;
    };
  };
  timestamp: string;
}

// 오류 응답
Error Response (403): {
  success: false;
  error: {
    code: 'PERMISSION_DENIED';
    message: '접근 권한이 없습니다.';
  };
}
```

#### **GET /api/users/[id]**

```typescript
// 특정 사용자 조회
Response: {
  user: User;
  farms: Farm[];
  statistics: UserStatistics;
}
```

#### **PUT /api/users/[id]**

```typescript
// 사용자 정보 수정
Request: {
  name?: string;
  phone?: string;
  company_name?: string;
  account_type?: 'admin' | 'user';
}

Response: {
  user: User;
  message: string;
}
```

### **3. 농장 관리 API**

#### **GET /api/farms**

```typescript
// 농장 목록 조회
Query: {
  page?: number;
  limit?: number;
  owner_id?: string;
  farm_type?: string;
  search?: string;
}

Response: {
  farms: Farm[];
  total: number;
  page: number;
  limit: number;
}
```

#### **POST /api/farms**

```typescript
// 농장 생성
Request: {
  farm_name: string;
  farm_address: string;
  farm_detailed_address?: string;
  farm_type?: string;
  description?: string;
  manager_name: string;
  manager_phone: string;
}

Response: {
  farm: Farm;
  qr_code_url: string;
  message: string;
}
```

#### **PUT /api/farms/[id]**

```typescript
// 농장 정보 수정
Request: {
  farm_name?: string;
  farm_address?: string;
  farm_detailed_address?: string;
  farm_type?: string;
  description?: string;
  manager_name?: string;
  manager_phone?: string;
}

Response: {
  farm: Farm;
  message: string;
}
```

#### **DELETE /api/farms/[id]**

```typescript
// 농장 삭제
Response: {
  message: string;
}
```

### **4. 농장 구성원 API**

#### **GET /api/farms/[id]/members**

```typescript
// 농장 구성원 목록 조회
Response: {
  members: FarmMember[];
  total: number;
}
```

#### **POST /api/farms/[id]/members**

```typescript
// 농장 구성원 추가
Request: {
  user_email: string;
  role: 'manager' | 'viewer';
  position?: string;
  responsibilities?: string;
}

Response: {
  member: FarmMember;
  message: string;
}
```

#### **PUT /api/farms/[id]/members/[memberId]**

```typescript
// 구성원 권한 수정
Request: {
  role?: 'manager' | 'viewer';
  position?: string;
  responsibilities?: string;
  is_active?: boolean;
}

Response: {
  member: FarmMember;
  message: string;
}
```

### **5. 방문자 관리 API**

#### **GET /api/farms/[id]/visitors**

```typescript
// 농장 방문자 목록 조회
Query: {
  page?: number;
  limit?: number;
  start_date?: string;
  end_date?: string;
  search?: string;
}

Response: {
  visitors: VisitorEntry[];
  total: number;
  page: number;
  limit: number;
}
```

#### **POST /api/farms/[id]/visitors**

```typescript
// 방문자 등록
Request: {
  visitor_name: string;
  visitor_phone: string;
  visitor_address: string;
  visitor_purpose?: string;
  visit_datetime: string;
  disinfection_check: boolean;
  vehicle_number?: string;
  consent_given: boolean;
  session_token: string;
}

Response: {
  visitor: VisitorEntry;
  message: string;
}
```

#### **GET /api/visitors/export**

```typescript
// 방문자 데이터 CSV 내보내기
Query: {
  farm_id?: string;
  start_date?: string;
  end_date?: string;
  format: 'csv' | 'excel';
}

Response: File Download
```

## 🎯 **주요 기능 명세**

### **1. 사용자 인증 및 권한 관리**

#### **기능 개요**

- Supabase Auth 기반 인증 시스템
- 계층적 권한 구조 (Admin > Owner > Manager > Viewer)
- 세션 관리 및 자동 갱신

#### **세부 기능**

```typescript
✅ 이메일/비밀번호 로그인
✅ 회원가입 및 이메일 인증
✅ 비밀번호 재설정
✅ 자동 로그인 유지
✅ 권한별 페이지 접근 제어
✅ 로그인 통계 추적
✅ 비밀번호 정책 (최소 8자, 대문자/소문자/숫자/특수문자 필수 포함)
✅ 비밀번호 변경 (강력한 검증 적용)
✅ 회원 탈퇴
✅ 비밀번호 정책 (최소 8자, 대문자/소문자/숫자/특수문자 필수 포함)
✅ 비밀번호 변경 (강력한 검증 적용)
✅ 회원 탈퇴
✅ 비밀번호 정책 (최소 8자, 대문자/소문자/숫자/특수문자 필수 포함)
✅ 비밀번호 변경 (강력한 검증 적용)
✅ 회원 탈퇴
```

### **2. 농장 관리 시스템**

#### **기능 개요**

- 농장 정보 CRUD 관리
- QR 코드 생성 및 관리
- 농장 유형별 분류 및 권한 관리

#### **API 엔드포인트**

#### **POST /api/farms**

```typescript
// 새로운 농장 생성
// 인증: Bearer 토큰 필요 (관리자 또는 농장 생성 권한)


Request: {
  farm_name: string;          // 농장명 (필수)
  description?: string;        // 농장 설명
  farm_address: string;        // 농장 주소 (필수)
  farm_detailed_address?: string; // 상세 주소
  farm_type?: string;          // 농장 유형 (축산, 농업, 원예 등)
  manager_phone?: string;      // 관리자 연락처
  manager_name?: string;       // 관리자 이름
}

Response: {
  success: boolean;
  data: {
    farm: {
      id: string;
      farm_name: string;
      farm_address: string;
      farm_type: string | null;
      is_active: boolean;
      created_at: string;
      updated_at: string;
    };
    qr_code_url: string;  // 생성된 QR 코드 이미지 URL
  };
  message: string;
}
```

#### **GET /api/farms/:farmId**

```typescript
// 특정 농장 상세 정보 조회
// 인증: Bearer 토큰 필요 (해당 농장에 대한 조회 권한 필요)

Response: {
  success: boolean;
  data: {
    farm: {
      id: string;
      farm_name: string;
      description: string | null;
      farm_address: string;
      farm_detailed_address: string | null;
      farm_type: string | null;
      owner_id: string;
      manager_phone: string | null;
      manager_name: string | null;
      is_active: boolean;
      created_at: string;
      updated_at: string;
      owner: {
        id: string;
        name: string;
        email: string;
      }
      member_count: number;
      visitor_count: number;
      qr_code_url: string;
    }
  }
}
```

#### **세부 기능**

```typescript
✅ 농장 등록/수정/삭제 (권한 기반)
✅ 농장 유형 분류 (축산, 농업, 원예, 기타)
✅ 다음 우편번호 API 연동 주소 검색
✅ QR 코드 자동 생성 및 관리
✅ 농장별 방문자 통계 대시보드
✅ 농장 활성화/비활성화 토글
✅ 구성원 초대 및 권한 관리
✅ 방문자 등록 및 관리
✅ 실시간 방문자 현황 모니터링
✅ 방문 기록 조회 및 내보내기
```

### **3. 농장 구성원 관리**

#### **기능 개요**

- 농장별 구성원 관리
- 역할 기반 권한 할당
- 구성원 초대 시스템

#### **세부 기능**

```typescript
✅ 구성원 초대 (이메일 기반)
✅ 역할 할당 (Owner/Manager/Viewer)
✅ 권한별 기능 제한
✅ 구성원 활성화/비활성화
✅ 직책 및 담당업무 관리
✅ 직책 및 담당업무 관리
```

### **4. 방문자 등록 시스템**

#### **기능 개요**

- QR 코드 스캔을 통한 방문자 등록
- 개인정보 보호 및 동의 관리
- 실시간 방문자 추적 및 관리

#### **API 엔드포인트**

#### **POST /api/visitors**

```typescript
// 새로운 방문자 등록
// 인증: Bearer 토큰 또는 세션 토큰

Request: {
  farm_id: string;           // 방문할 농장 ID (필수)
  visitor_name: string;      // 방문자 이름 (필수)
  visitor_phone: string;     // 연락처 (필수)
  visitor_address: string;   // 주소 (필수)
  visitor_purpose?: string;  // 방문 목적
  vehicle_number?: string;   // 차량 번호
  disinfection_check: boolean; // 소독 여부
  consent_given: boolean;     // 개인정보 수집 동의 여부 (필수)
  notes?: string;            // 기타 참고사항
}

Response: {
  success: boolean;
  data: {
    visitor: {
      id: string;
      visitor_name: string;
      visitor_phone: string;
      visit_datetime: string;
      session_token: string;
      qr_code_url: string;  // 방문자용 QR 코드
    };
  };
  message: string;
}
```

#### **GET /api/visitors**

```typescript
// 방문자 목록 조회
// 인증: Bearer 토큰 (해당 농장에 대한 조회 권한 필요)

Query: {
  farm_id?: string;         // 특정 농장 필터링
  start_date?: string;      // YYYY-MM-DD
  end_date?: string;        // YYYY-MM-DD
  visitor_name?: string;    // 방문자 이름으로 검색
  visitor_phone?: string;   // 연락처로 검색
  purpose?: string;         // 방문 목적으로 필터링
  page?: number;            // 페이지 번호
  limit?: number;           // 페이지당 항목 수
  sort_by?: 'visit_datetime' | 'created_at' | 'visitor_name';
  order?: 'asc' | 'desc';
}

Response: {
  success: boolean;
  data: {
    visitors: Array<{
      id: string;
      visitor_name: string;
      visitor_phone: string;
      visitor_address: string;
      visitor_purpose: string | null;
      vehicle_number: string | null;
      disinfection_check: boolean;
      visit_datetime: string;
      created_at: string;
      farm: {
        id: string;
        farm_name: string;
      };
      registered_by?: {
        id: string;
        name: string;
        email: string;
      };
    }>;
    pagination: {
      total: number;
      page: number;
      limit: number;
      total_pages: number;
    };
  };
}
```

#### **세부 기능**

```typescript
✅ QR 코드 스캔을 통한 신속한 방문자 등록
✅ 반응형 방문자 정보 입력 폼
✅ 개인정보 수집 동의 관리 (GDPR 대응)
✅ 방문 목적 분류 (사전 정의된 목록 + 사용자 정의)
✅ 차량 정보 등록 (차량 번호, 차종 등)
✅ 실시간 방문자 현황 대시보드
✅ 방문 기록 검색 및 필터링
✅ 엑셀/PDF 형식으로 내보내기
✅ 방문자 통계 및 분석
✅ 모바일 최적화된 인터페이스
```

### **5. 관리자 대시보드**

#### **기능 개요**

- 시스템 전체 현황 모니터링
- 통계 및 분석 데이터 제공
- 빠른 액션 버튼

#### **세부 기능**

```typescript
✅ 실시간 통계 카드
✅ 방문자 추이 차트
✅ 농장별 활동 현황
✅ 사용자 증가 추이
✅ 지역별 분포 분석
✅ 빠른 농장 등록
✅ 최근 활동 로그
✅ 실시간 통계 카드
✅ 방문자 추이 차트
✅ 농장별 활동 현황
✅ 사용자 증가 추이
✅ 지역별 분포 분석
✅ 빠른 농장 등록
✅ 최근 활동 로그
```

### **6. 시스템 로그 관리**

#### **기능 개요**

- 모든 시스템 활동 추적
- 보안 이벤트 모니터링
- 로그 분석 및 관리

#### **세부 기능**

```typescript
✅ 사용자 활동 로깅
✅ 농장 관련 작업 추적
✅ 오류 및 경고 기록
✅ 로그 레벨별 필터링
✅ 로그 검색 및 정렬
✅ 개별 로그 삭제
✅ 30일 이전 로그 일괄 삭제
✅ 전체 로그 삭제 (관리자)
✅ 페이지네이션 (100개씩)
✅ 사용자 활동 로깅
✅ 농장 관련 작업 추적
✅ 오류 및 경고 기록
✅ 로그 레벨별 필터링
✅ 로그 검색 및 정렬
✅ 개별 로그 삭제
✅ 30일 이전 로그 일괄 삭제
✅ 전체 로그 삭제 (관리자)
✅ 페이지네이션 (100개씩)
```

### **7. 데이터 내보내기**

#### **기능 개요**

- 다양한 형식의 데이터 내보내기
- 기간별 필터링 지원
- 한글 인코딩 지원

#### **세부 기능**

```typescript
✅ 방문자 데이터 CSV 내보내기
✅ 사용자 목록 CSV 내보내기
✅ 농장 정보 CSV 내보내기
✅ 기간별 필터링 (최대 5년)
✅ 한글 인코딩 (EUC-KR)
✅ 파일명 자동 생성
✅ 방문자 데이터 CSV 내보내기
✅ 사용자 목록 CSV 내보내기
✅ 농장 정보 CSV 내보내기
✅ 기간별 필터링 (최대 5년)
✅ 한글 인코딩 (EUC-KR)
✅ 파일명 자동 생성
```

## 🔒 **보안 및 권한**

### **권한 매트릭스**

| 기능            | Admin | Owner | Manager | Viewer | Guest |
| --------------- | ----- | ----- | ------- | ------ | ----- |
| 시스템 관리     | ✅    | ❌    | ❌      | ❌     | ❌    |
| 모든 농장 조회  | ✅    | ❌    | ❌      | ❌     | ❌    |
| 농장 생성       | ✅    | ✅    | ❌      | ❌     | ❌    |
| 농장 수정       | ✅    | ✅    | ✅      | ❌     | ❌    |
| 구성원 관리     | ✅    | ✅    | ❌      | ❌     | ❌    |
| 방문자 조회     | ✅    | ✅    | ✅      | ✅     | ❌    |
| 방문자 등록     | ✅    | ✅    | ✅      | ❌     | ✅    |
| 데이터 내보내기 | ✅    | ✅    | ✅      | ❌     | ❌    |

### **보안 기능**

#### **데이터 보호**

```typescript
✅ Row Level Security (RLS) 적용
✅ 개인정보 암호화 저장
✅ CSRF 토큰 검증
✅ XSS 방지 필터링
✅ SQL 인젝션 방지
✅ Row Level Security (RLS) 적용
✅ 개인정보 암호화 저장
✅ CSRF 토큰 검증
✅ XSS 방지 필터링
✅ SQL 인젝션 방지
```

#### **접근 제어**

```typescript
✅ JWT 토큰 기반 인증
✅ 세션 만료 관리
✅ IP 기반 접근 제한 (선택)
✅ 브루트포스 공격 방지
✅ 권한별 API 접근 제어
✅ JWT 토큰 기반 인증
✅ 세션 만료 관리
✅ IP 기반 접근 제한 (선택)
✅ 브루트포스 공격 방지
✅ 권한별 API 접근 제어
```

## 📱 **사용자 인터페이스**

### **반응형 디자인**

```typescript
✅ 모바일 우선 설계
✅ 태블릿 최적화
✅ 데스크톱 지원
✅ 다크/라이트 테마
✅ 접근성 (a11y) 준수
```

### **사용자 경험**

```typescript
✅ 로딩 상태 표시
✅ 오류 메시지 안내
✅ 성공 알림 토스트
✅ 폼 유효성 검사
✅ 자동 저장 기능
✅ 키보드 네비게이션
```

## 🚀 **성능 및 최적화**

### **프론트엔드 최적화**

```typescript
✅ 컴포넌트 지연 로딩
✅ 이미지 최적화
✅ 번들 크기 최적화
✅ 캐싱 전략
✅ 메모이제이션
```

### **백엔드 최적화**

```typescript
✅ 데이터베이스 인덱싱
✅ 쿼리 최적화
✅ 페이지네이션
✅ 연결 풀링
✅ 캐시 레이어
```

## 📊 **모니터링 및 분석**

### **시스템 메트릭**

```typescript
✅ 응답 시간 추적
✅ 오류율 모니터링
✅ 사용자 활동 분석
✅ 성능 병목 식별
✅ 리소스 사용량 추적
```

### **비즈니스 인사이트**

```typescript
✅ 농장별 방문자 통계
✅ 시간대별 방문 패턴
✅ 지역별 분포 분석
✅ 사용자 증가 추이
✅ 기능 사용률 분석
```

## 📋 **타입 정의**

### **핵심 타입**

```typescript
// 사용자 타입
interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  account_type: "admin" | "user";
  company_name?: string;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
}

// 농장 타입
interface Farm {
  id: string;
  farm_name: string;
  farm_address: string;
  farm_detailed_address?: string;
  farm_type?: string;
  description?: string;
  owner_id: string;
  manager_name: string;
  manager_phone: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// 농장 구성원 타입
interface FarmMember {
  id: string;
  farm_id: string;
  user_id: string;
  role: "owner" | "manager" | "viewer";
  position?: string;
  responsibilities?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// 방문자 기록 타입
interface VisitorEntry {
  id: string;
  farm_id: string;
  visit_datetime: string;
  visitor_name: string;
  visitor_phone: string;
  visitor_address: string;
  visitor_purpose?: string;
  disinfection_check: boolean;
  vehicle_number?: string;
  registered_by?: string;
  session_token: string;
  consent_given: boolean;
  created_at: string;
  updated_at: string;
}

// 시스템 로그 타입
interface SystemLog {
  id: string;
  level: "error" | "warn" | "info" | "debug";
  action: string;
  message: string;
  user_id?: string;
  user_email?: string;
  resource_type?: "farm" | "user" | "visitor" | "system";
  resource_id?: string;
  metadata?: Record<string, any>;
  created_at: string;
}
```

### **API 응답 타입**

```typescript
// 표준 API 응답
interface ApiResponse<T> {
  data?: T;
  message: string;
  success: boolean;
  error?: string;
}

// 페이지네이션 응답
interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_prev: boolean;
}

// 통계 데이터 타입
interface Statistics {
  total_users: number;
  total_farms: number;
  total_visitors: number;
  active_users: number;
  recent_visitors: number;
  growth_rate: number;
}
```

## 📁 **파일 업로드 API (2025-06-14 추가)**

### **1. 프로필 사진 업로드**

#### **POST /api/users/profile/upload**

```typescript
// 프로필 사진 업로드
// 인증: Bearer 토큰 필요 (본인 계정만)

Request: FormData {
  file: File;  // 이미지 파일 (JPEG, JPG, PNG, WebP)
}

Response: {
  success: boolean;
  data: {
    profile_image_url: string;  // 업로드된 이미지의 공개 URL
    file_info: {
      fileName: string;
      fileSize: number;
      fileType: string;
    };
  };
  message: string;
}

// 제한사항
- 최대 파일 크기: 5MB
- 허용 형식: JPEG, JPG, PNG, WebP
- 파일 경로: profiles/{userId}/profile.{ext}
```

### **2. 시스템 파일 업로드 (관리자 전용)**

#### **POST /api/system/logo/upload**

```typescript
// 시스템 로고 업로드
// 인증: Bearer 토큰 필요 (관리자 권한)

Request: FormData {
  file: File;  // 로고 파일 (JPEG, PNG, WebP, SVG)
}

Response: {
  success: boolean;
  data: {
    logo_url: string;  // 업로드된 로고의 공개 URL
    file_info: {
      fileName: string;
      fileSize: number;
      fileType: string;
    };
  };
  message: string;
}

// 제한사항
- 최대 파일 크기: 2MB
- 허용 형식: JPEG, JPG, PNG, WebP, SVG
- 파일 경로: system/logo.{ext}
```

#### **POST /api/system/favicon/upload**

```typescript
// 파비콘 업로드
// 인증: Bearer 토큰 필요 (관리자 권한)

Request: FormData {
  file: File;  // 파비콘 파일 (ICO, PNG, SVG)
}

Response: {
  success: boolean;
  data: {
    favicon_url: string;  // 업로드된 파비콘의 공개 URL
    file_info: {
      fileName: string;
      fileSize: number;
      fileType: string;
    };
  };
  message: string;
}

// 제한사항
- 최대 파일 크기: 1MB
- 허용 형식: ICO, PNG, SVG
- 파일 경로: system/favicon.{ext}
```

### **3. 파일 업로드 공통 에러 응답**

```typescript
// 파일 크기 초과
Error Response (413): {
  success: false;
  error: {
    code: 'FILE_TOO_LARGE';
    message: '파일 크기가 너무 큽니다. {maxSize}MB 이하의 파일을 선택하세요.';
    details: {
      maxSize: number;
      actualSize: number;
    };
  };
}

// 지원하지 않는 파일 형식
Error Response (400): {
  success: false;
  error: {
    code: 'UNSUPPORTED_FILE_TYPE';
    message: '지원하지 않는 파일 형식입니다. {allowedTypes} 파일만 업로드 가능합니다.';
    details: {
      allowedTypes: string[];
      actualType: string;
    };
  };
}

// 권한 없음
Error Response (403): {
  success: false;
  error: {
    code: 'PERMISSION_DENIED';
    message: '파일 업로드 권한이 없습니다.';
  };
}
```

## 🎨 **UI 컴포넌트 명세 (2025-06-14 추가)**

### **1. 파일 업로드 컴포넌트**

#### **ProfileImageUpload**

```typescript
// 프로필 사진 업로드 컴포넌트
interface ProfileImageUploadProps {
  currentImageUrl?: string;
  onUploadSuccess: (imageUrl: string) => void;
  onUploadError: (error: string) => void;
  disabled?: boolean;
}

// 기능
✅ 드래그 앤 드롭 지원
✅ 파일 선택 버튼
✅ 실시간 미리보기
✅ 업로드 진행 상태 표시
✅ 파일 유효성 검사
✅ 에러 메시지 표시
```

#### **SystemFileUpload**

```typescript
// 시스템 파일 업로드 컴포넌트 (로고/파비콘)
interface SystemFileUploadProps {
  fileType: 'logo' | 'favicon';
  currentFileUrl?: string;
  onUploadSuccess: (fileUrl: string) => void;
  onUploadError: (error: string) => void;
  disabled?: boolean;
}

// 기능
✅ 파일 타입별 제한 설정
✅ 미리보기 영역 크기 조정
✅ 관리자 권한 확인
✅ 업로드 상태 관리
✅ 캐시 무효화 처리
```

### **2. 파일 관리 유틸리티**

#### **FileUploadUtils**

```typescript
// 공통 파일 업로드 유틸리티
interface FileUploadOptions {
  bucket: string;
  filePath: string;
  allowedTypes?: string[];
  maxSize?: number;
  upsert?: boolean;
  cacheControl?: string;
}

// 주요 함수
✅ validateFile(file, options): 파일 유효성 검사
✅ uploadFile(file, options): 파일 업로드
✅ uploadProfileImage(file, userId): 프로필 사진 업로드
✅ uploadSystemLogo(file): 시스템 로고 업로드
✅ uploadFavicon(file): 파비콘 업로드
✅ createFilePreview(file): 미리보기 URL 생성
✅ revokeFilePreview(url): 미리보기 URL 해제
✅ formatFileSize(bytes): 파일 크기 포맷팅
```

---

**문서 작성자**: AI Assistant
**최종 검토일**: 2025-06-14 (파일 업로드 API 추가)
**다음 업데이트**: 기능 추가 시

## 비밀번호 관련 유틸리티 및 API

### 타입 정의

```typescript
interface PasswordValidationRules {
  minLength?: number; // 최소 길이
  maxLength?: number; // 최대 길이
  requireNumbers?: boolean; // 숫자 포함
  requireUppercase?: boolean; // 대문자 포함
  requireLowercase?: boolean; // 소문자 포함
  requireSpecial?: boolean; // 특수문자 포함
  disallowSpaces?: boolean; // 공백 금지
}

interface PasswordValidationResult {
  isValid: boolean;
  errors: string[];
}

interface PasswordStrengthResult {
  score: number; // 0-4 강도 점수
  label: string; // 강도 레이블
  suggestions: string[]; // 개선 제안
}
```

### 유틸리티 함수

```typescript
// 비밀번호 유효성 검사
function validatePassword(
  password: string,
  rules?: PasswordValidationRules
): PasswordValidationResult;

// 비밀번호 일치 여부 검사
function validatePasswordMatch(
  password: string,
  confirmPassword: string
): PasswordValidationResult;

// 비밀번호 강도 측정
function getPasswordStrength(password: string): PasswordStrengthResult;
```

### API 엔드포인트

#### 회원가입

```typescript
POST /api/auth/register
Content-Type: application/json

Request:
{
  email: string;
  password: string;
  name: string;
  phone: string;
}

Response:
{
  success: boolean;
  user?: {
    id: string;
    email: string;
    role: string;
  };
  error?: string;
}
```

#### 비밀번호 변경

```typescript
PUT /api/auth/password
Content-Type: application/json
Authorization: Bearer {token}

Request:
{
  currentPassword: string;
  newPassword: string;
}

Response:
{
  success: boolean;
  error?: string;
}
```

#### 비밀번호 재설정 요청

```typescript
POST /api/auth/reset-password/request
Content-Type: application/json

Request:
{
  email: string;
}

Response:
{
  success: boolean;
  message?: string;
  error?: string;
}
```

#### 비밀번호 재설정 완료

```typescript
POST /api/auth/reset-password/confirm
Content-Type: application/json

Request:
{
  token: string;
  password: string;
}

Response:
{
  success: boolean;
  error?: string;
}
```

### 에러 코드

```typescript
enum PasswordErrorCode {
  INVALID_LENGTH = "INVALID_LENGTH",
  MISSING_UPPERCASE = "MISSING_UPPERCASE",
  MISSING_LOWERCASE = "MISSING_LOWERCASE",
  MISSING_NUMBER = "MISSING_NUMBER",
  MISSING_SPECIAL = "MISSING_SPECIAL",
  CONTAINS_SPACE = "CONTAINS_SPACE",
  RECENTLY_USED = "RECENTLY_USED",
  COMMON_PASSWORD = "COMMON_PASSWORD",
  PERSONAL_INFO = "PERSONAL_INFO",
}

interface PasswordError {
  code: PasswordErrorCode;
  message: string;
  suggestion?: string;
}
```
