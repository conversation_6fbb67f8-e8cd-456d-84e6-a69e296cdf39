import { use<PERSON><PERSON>back, useMemo } from "react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { useSystemLog } from "../hooks/useSystemLog";
import { useQuery } from "@tanstack/react-query";
import {
  Users,
  Building2,
  FileText,
  Activity,
  UserCheck,
  UserX,
  Clock,
  AlertCircle,
} from "lucide-react";

interface DashboardStats {
  today: {
    new_users: number;
    visitors: number;
    logs: number;
    logs_by_level: Record<string, number>;
  };
  monthly: {
    visitors: number;
  };
  total: {
    users: number;
    users_by_type: Record<string, number>;
    active_farms: number;
    error_logs_30d: number;
  };
}

export function Dashboard() {
  const { toast } = useToast();
  const { logError } = useSystemLog();

  const { data: stats, isLoading } = useQuery<DashboardStats>({
    queryKey: ["dashboard-stats"],
    queryFn: async () => {
      try {
        const response = await fetch("/api/admin/stats");
        if (!response.ok) throw new Error("Failed to fetch dashboard stats");
        return await response.json();
      } catch (error) {
        logError("FETCH_STATS_ERROR", error);
        toast({
          title: "통계 데이터 로딩 실패",
          description: "통계 데이터를 불러오는 중 오류가 발생했습니다.",
          variant: "destructive",
        });
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 5 * 60 * 1000, // 5 minutes
  });

  const statCards = useMemo(
    () => [
      {
        title: "오늘 신규 사용자",
        value: stats?.today.new_users || 0,
        icon: UserCheck,
        description: "24시간 내 가입한 사용자 수",
      },
      {
        title: "오늘 방문자",
        value: stats?.today.visitors || 0,
        icon: Users,
        description: "24시간 내 방문한 사용자 수",
      },
      {
        title: "활성 농장",
        value: stats?.total.active_farms || 0,
        icon: Building2,
        description: "현재 활성화된 농장 수",
      },
      {
        title: "오늘 시스템 로그",
        value: stats?.today.logs || 0,
        icon: FileText,
        description: "24시간 내 생성된 로그 수",
      },
      {
        title: "월간 방문자",
        value: stats?.monthly.visitors || 0,
        icon: Activity,
        description: "이번 달 총 방문자 수",
      },
      {
        title: "총 사용자",
        value: stats?.total.users || 0,
        icon: Users,
        description: "전체 등록된 사용자 수",
      },
      {
        title: "최근 오류",
        value: stats?.total.error_logs_30d || 0,
        icon: AlertCircle,
        description: "최근 30일 내 오류 발생 수",
        alert: true,
      },
      {
        title: "비활성 사용자",
        value: stats?.total.users_by_type?.inactive || 0,
        icon: UserX,
        description: "30일 이상 미접속 사용자 수",
      },
    ],
    [stats]
  );

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 8 }).map((_, index) => (
          <Card key={index} className="animate-pulse">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 w-24 bg-muted rounded" />
              <div className="h-4 w-4 bg-muted rounded" />
            </CardHeader>
            <CardContent>
              <div className="h-8 w-16 bg-muted rounded mb-2" />
              <div className="h-3 w-32 bg-muted rounded" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {statCards.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
            <stat.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stat.value.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">{stat.description}</p>
            {stat.alert && stat.value > 0 && (
              <p className="text-xs text-red-500 mt-1">
                주의: 오류가 발생했습니다
              </p>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
