"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/components/providers/auth-provider";
import { supabase } from "@/lib/supabase";
import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  Users,
  Search,
  Filter,
  Download,
  Calendar,
  Building2,
  Phone,
  MapPin,
  Car,
  FileText,
  CheckCircle,
  XCircle,
  Home,
  Shield,
} from "lucide-react";
import Link from "next/link";

/**
 * 방문자 기록 인터페이스 (농장 정보 포함)
 */
interface VisitorEntryWithFarm {
  id: string;
  farm_id: string;
  visit_datetime: string;
  visitor_name: string;
  visitor_phone: string;
  visitor_address: string;
  vehicle_number: string | null;
  visitor_purpose: string;
  disinfection_check: boolean;
  notes: string | null;
  consent_given: boolean;
  session_token: string;
  registered_by?: string;
  created_at: string;
  farms: {
    farm_name: string;
    farm_type?: string;
  };
}

/**
 * 농장 정보 인터페이스
 */
interface Farm {
  id: string;
  farm_name: string;
  farm_type?: string;
}

/**
 * Admin 전용 전체 방문자 기록 조회 페이지
 *
 * 모든 농장의 방문자 기록을 조회하고 농장별 필터링 기능을 제공합니다.
 * Admin 권한이 있는 사용자만 접근 가능합니다.
 */
export default function AllVisitorsPage() {
  const { profile } = useAuth();
  const [visitors, setVisitors] = useState<VisitorEntryWithFarm[]>([]);
  const [farms, setFarms] = useState<Farm[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedFarm, setSelectedFarm] = useState<string>("all");
  const [dateFilter, setDateFilter] = useState("");

  /**
   * 전체 방문자 기록과 농장 정보를 로드하는 함수
   */
  useEffect(() => {
    const fetchAllData = async () => {
      try {
        console.log("=== Admin 전체 방문자 기록 조회 시작 ===");

        // 현재 사용자가 admin인지 확인
        const {
          data: { user },
        } = await supabase.auth.getUser();
        console.log("현재 사용자:", user);
        console.log("사용자 계정 타입:", profile?.account_type);

        if (profile?.account_type !== "admin") {
          console.error("Admin 권한이 없습니다.");
          return;
        }

        // 모든 농장 정보 조회
        const { data: farmsData, error: farmsError } = await supabase
          .from("farms")
          .select("id, farm_name, farm_type")
          .order("farm_name");

        if (farmsError) {
          console.error("농장 조회 오류:", farmsError);
          throw farmsError;
        }

        console.log("조회된 농장 수:", farmsData?.length || 0);
        setFarms(farmsData || []);

        // 모든 방문자 기록 조회 (농장 정보 포함)
        const { data: visitorsData, error: visitorsError } = await supabase
          .from("visitor_entries")
          .select(
            `
            *,
            farms (
              farm_name,
              farm_type
            )
          `
          )
          .order("visit_datetime", { ascending: false });

        if (visitorsError) {
          console.error("방문자 기록 조회 오류:", visitorsError);
          throw visitorsError;
        }

        console.log("조회된 방문자 기록 수:", visitorsData?.length || 0);
        setVisitors(visitorsData || []);
      } catch (error) {
        console.error("데이터 조회 오류:", error);
      } finally {
        setLoading(false);
      }
    };

    if (profile) {
      fetchAllData();
    }
  }, [profile]);

  /**
   * 검색 및 필터링된 방문자 목록
   */
  const filteredVisitors = visitors.filter((visitor) => {
    const matchesSearch =
      visitor.visitor_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      visitor.visitor_phone.includes(searchTerm) ||
      visitor.visitor_purpose
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      visitor.farms.farm_name.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFarm =
      selectedFarm === "all" || visitor.farm_id === selectedFarm;

    const matchesDate =
      !dateFilter ||
      new Date(visitor.visit_datetime).toISOString().split("T")[0] ===
        dateFilter;

    return matchesSearch && matchesFarm && matchesDate;
  });

  /**
   * CSV 다운로드 함수
   */
  const downloadCSV = () => {
    const headers = [
      "농장명",
      "방문일시",
      "성명",
      "연락처",
      "주소",
      "차량번호",
      "방문목적",
      "소독여부",
      "비고",
    ];

    const csvContent = [
      headers.join(","),
      ...filteredVisitors.map((visitor) =>
        [
          `"${visitor.farms.farm_name}"`,
          new Date(visitor.visit_datetime).toLocaleString("ko-KR"),
          `"${visitor.visitor_name}"`,
          visitor.visitor_phone,
          `"${visitor.visitor_address}"`,
          visitor.vehicle_number || "",
          `"${visitor.visitor_purpose}"`,
          visitor.disinfection_check ? "완료" : "미완료",
          `"${visitor.notes || ""}"`,
        ].join(",")
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `전체_방문자기록_${new Date().toISOString().split("T")[0]}.csv`
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Admin 권한 확인
  if (profile?.account_type !== "admin") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-center text-red-600">
              접근 권한 없음
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-center">Admin 권한이 필요합니다.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>전체 방문자 기록을 불러오는 중...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      {/* 브레드크럼 */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/admin/dashboard" className="flex items-center gap-1">
                <Home className="h-4 w-4" />
                대시보드
              </Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>전체 방문자 기록</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* 헤더 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <SidebarTrigger />
          <div>
            <h2 className="text-3xl font-bold tracking-tight mb-2 flex items-center gap-2">
              <Shield className="h-8 w-8 text-red-600" />
              전체 방문자 기록 (Admin)
            </h2>
            <p className="text-muted-foreground">
              모든 농장의 방문자 출입 기록을 관리하세요
            </p>
          </div>
        </div>
        <Button onClick={downloadCSV} className="flex items-center gap-2">
          <Download className="h-4 w-4" />
          CSV 다운로드
        </Button>
      </div>

      {/* 검색 및 필터 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            검색 및 필터
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="이름, 전화번호, 방문목적, 농장명으로 검색..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="w-48">
              <Select value={selectedFarm} onValueChange={setSelectedFarm}>
                <SelectTrigger>
                  <SelectValue placeholder="농장 선택" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">모든 농장</SelectItem>
                  {farms.map((farm) => (
                    <SelectItem key={farm.id} value={farm.id}>
                      {farm.farm_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="w-48">
              <Input
                type="date"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                className="w-full"
              />
            </div>
            {(searchTerm || selectedFarm !== "all" || dateFilter) && (
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm("");
                  setSelectedFarm("all");
                  setDateFilter("");
                }}
              >
                초기화
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 통계 카드 */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">총 방문자</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{visitors.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">총 농장</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{farms.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">소독 완료</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {visitors.filter((v) => v.disinfection_check).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">검색 결과</CardTitle>
            <Search className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredVisitors.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* 방문자 목록 테이블 */}
      <Card>
        <CardHeader>
          <CardTitle>전체 방문자 목록</CardTitle>
          <CardDescription>
            모든 농장의 방문자 기록입니다. 총 {filteredVisitors.length}건의
            기록이 있습니다.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredVisitors.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {searchTerm || selectedFarm !== "all" || dateFilter
                  ? "검색 조건에 맞는 방문자가 없습니다."
                  : "아직 방문자가 없습니다."}
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>농장</TableHead>
                    <TableHead>방문일시</TableHead>
                    <TableHead>성명</TableHead>
                    <TableHead>연락처</TableHead>
                    <TableHead>방문목적</TableHead>
                    <TableHead>차량번호</TableHead>
                    <TableHead>소독여부</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredVisitors.map((visitor) => (
                    <TableRow key={visitor.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Building2 className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <div className="font-medium">
                              {visitor.farms.farm_name}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {visitor.farms.farm_type || "일반"}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <div className="font-medium">
                              {new Date(
                                visitor.visit_datetime
                              ).toLocaleDateString("ko-KR")}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {new Date(
                                visitor.visit_datetime
                              ).toLocaleTimeString("ko-KR")}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">
                            {visitor.visitor_name}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <span>{visitor.visitor_phone}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <span>{visitor.visitor_purpose}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {visitor.vehicle_number ? (
                          <div className="flex items-center gap-2">
                            <Car className="h-4 w-4 text-muted-foreground" />
                            <Badge variant="outline">
                              {visitor.vehicle_number}
                            </Badge>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {visitor.disinfection_check ? (
                          <Badge
                            variant="default"
                            className="bg-green-100 text-green-800"
                          >
                            <CheckCircle className="h-3 w-3 mr-1" />
                            완료
                          </Badge>
                        ) : (
                          <Badge
                            variant="secondary"
                            className="bg-red-100 text-red-800"
                          >
                            <XCircle className="h-3 w-3 mr-1" />
                            미완료
                          </Badge>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
