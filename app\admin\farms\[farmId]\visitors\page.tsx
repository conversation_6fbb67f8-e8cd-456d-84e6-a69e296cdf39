"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useFarms } from "@/hooks/use-farms";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Breadcrumb,
  BreadcrumbItem,
  <PERSON>readcrumbLink,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>readcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  Home,
  Users,
  Search,
  Calendar,
  Phone,
  MapPin,
  Car,
  FileText,
  CheckCircle,
  XCircle,
  Download,
  Filter,
  Building2,
  ChevronDown,
  Shield,
} from "lucide-react";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { supabase } from "@/lib/supabase";

interface Farm {
  id: string;
  farm_name: string;
  farm_address: string;
}

interface VisitorEntry {
  id: string;
  visit_datetime: string;
  visitor_name: string;
  visitor_phone: string;
  visitor_address: string;
  vehicle_number: string | null;
  visitor_purpose: string;
  disinfection_check: boolean;
  notes: string | null;
  consent_given: boolean;
  session_token: string;
  registered_by?: string;
  created_at: string;
}

export default function FarmVisitorsPage() {
  const params = useParams();
  const router = useRouter();
  const farmId = params.farmId as string;
  const { farms } = useFarms(); // 사용자의 모든 농장 가져오기

  const [farm, setFarm] = useState<Farm | null>(null);
  const [visitors, setVisitors] = useState<VisitorEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [dateFilter, setDateFilter] = useState("");

  // CSV 다운로드 옵션 상태
  const [includeAllFarms, setIncludeAllFarms] = useState(false);
  const [csvStartDate, setCsvStartDate] = useState("");
  const [csvEndDate, setCsvEndDate] = useState("");

  // 농장 정보 및 방문자 기록 로드
  useEffect(() => {
    const fetchData = async () => {
      try {
        // 농장 정보 가져오기
        const { data: farmData, error: farmError } = await supabase
          .from("farms")
          .select("id, farm_name, farm_address")
          .eq("id", farmId)
          .single();

        if (farmError) throw farmError;
        setFarm(farmData);

        // 방문자 기록 가져오기
        console.log("=== 방문자 기록 조회 시작 ===");
        console.log("farmId:", farmId);
        console.log("farmId 타입:", typeof farmId);

        // 현재 사용자 정보 확인
        const {
          data: { user },
        } = await supabase.auth.getUser();
        console.log("현재 로그인 사용자:", user);
        console.log("사용자 ID:", user?.id);

        // 농장 소유자 확인
        console.log("농장 정보:", farmData);
        console.log("농장 소유자 ID:", farmData?.owner_id);
        console.log("내가 농장 소유자인가?", farmData?.owner_id === user?.id);

        // 농장 구성원 확인
        console.log("=== 농장 구성원 확인 ===");
        const { data: memberData, error: memberError } = await supabase
          .from("farm_members")
          .select("*")
          .eq("farm_id", farmId)
          .eq("user_id", user?.id);

        console.log("구성원 조회 결과:", { memberData, memberError });

        // RLS 정책 테스트를 위한 직접 쿼리
        console.log("=== RLS 정책 테스트 ===");
        const { data: testData, error: testError } = await supabase.rpc(
          "auth.uid"
        );
        console.log("현재 auth.uid():", { testData, testError });

        // 방문자 기록 조회 시도
        console.log("=== 방문자 기록 조회 시도 ===");
        const { data: visitorsData, error: visitorsError } = await supabase
          .from("visitor_entries")
          .select("*")
          .eq("farm_id", farmId)
          .order("visit_datetime", { ascending: false });

        console.log("방문자 기록 조회 결과:", { visitorsData, visitorsError });
        console.log("조회된 방문자 수:", visitorsData?.length || 0);

        if (visitorsError) {
          console.error("=== 방문자 기록 조회 오류 ===");
          console.error("오류 코드:", visitorsError.code);
          console.error("오류 메시지:", visitorsError.message);
          console.error("오류 세부사항:", visitorsError.details);
          console.error("오류 힌트:", visitorsError.hint);

          // RLS 오류인 경우 추가 정보 출력
          if (visitorsError.code === "PGRST116") {
            console.error("RLS 정책으로 인한 접근 거부!");
            console.error("가능한 원인:");
            console.error("1. 농장 소유자가 아님");
            console.error("2. 농장 구성원이 아님");
            console.error("3. RLS 정책이 잘못 설정됨");
          }
          throw visitorsError;
        }

        console.log("=== 방문자 기록 조회 성공 ===");
        console.log("방문자 기록 개수:", visitorsData?.length || 0);
        if (visitorsData && visitorsData.length > 0) {
          console.log("첫 번째 방문자 기록:", visitorsData[0]);
        }
        setVisitors(visitorsData || []);
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    if (farmId) {
      fetchData();
    }
  }, [farmId]);

  /**
   * 농장 변경 핸들러
   * 다른 농장을 선택했을 때 해당 농장의 방문자 페이지로 이동
   */
  const handleFarmChange = (newFarmId: string) => {
    if (newFarmId !== farmId) {
      router.push(`/admin/farms/${newFarmId}/visitors`);
    }
  };

  // 검색 및 필터링된 방문자 목록
  const filteredVisitors = visitors.filter((visitor) => {
    const matchesSearch =
      visitor.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      visitor.phone_number.includes(searchTerm) ||
      visitor.visit_purpose.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesDate =
      !dateFilter ||
      new Date(visitor.entry_datetime).toISOString().split("T")[0] ===
        dateFilter;

    return matchesSearch && matchesDate;
  });

  /**
   * 기간 유효성 검사 함수
   */
  const validateDateRange = () => {
    if (!csvStartDate || !csvEndDate) return true;

    const start = new Date(csvStartDate);
    const end = new Date(csvEndDate);
    const diffYears =
      (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 365);

    if (diffYears > 5) {
      alert("기간 선택은 최대 5년까지 가능합니다.");
      return false;
    }

    if (start > end) {
      alert("시작일이 종료일보다 늦을 수 없습니다.");
      return false;
    }

    return true;
  };

  /**
   * 개선된 CSV 다운로드 함수
   * Phase 1 기능: 현재 농장만 / 모든 농장 포함 / 기간 선택 (최대 5년)
   * 한글 깨짐 해결: UTF-8 BOM 추가
   */
  const downloadCSV = async () => {
    try {
      // 기간 유효성 검사
      if (!validateDateRange()) {
        return;
      }

      let dataToDownload = [];
      let fileName = "";

      if (includeAllFarms) {
        // 모든 농장 데이터 가져오기
        console.log("모든 농장 데이터 다운로드 시작");

        let query = supabase
          .from("visitor_entries")
          .select(
            `
            *,
            farms (
              farm_name,
              farm_type
            )
          `
          )
          .order("visit_datetime", { ascending: false });

        // 기간 필터 적용
        if (csvStartDate) {
          query = query.gte("visit_datetime", csvStartDate + "T00:00:00");
        }
        if (csvEndDate) {
          query = query.lte("visit_datetime", csvEndDate + "T23:59:59");
        }

        const { data: allVisitors, error } = await query;

        if (error) {
          console.error("모든 농장 데이터 조회 오류:", error);
          alert("데이터 조회 중 오류가 발생했습니다.");
          return;
        }

        dataToDownload = allVisitors || [];
        fileName = `전체농장_방문자기록_${csvStartDate || "전체"}_${
          csvEndDate || "전체"
        }_${new Date().toISOString().split("T")[0]}.csv`;
      } else {
        // 현재 농장만 (필터링된 데이터 사용)
        let dataToFilter = [...filteredVisitors];

        // 기간 필터 적용 (추가 필터링)
        if (csvStartDate || csvEndDate) {
          dataToFilter = dataToFilter.filter((visitor) => {
            const visitDate = new Date(visitor.entry_datetime)
              .toISOString()
              .split("T")[0];
            const afterStart = !csvStartDate || visitDate >= csvStartDate;
            const beforeEnd = !csvEndDate || visitDate <= csvEndDate;
            return afterStart && beforeEnd;
          });
        }

        dataToDownload = dataToFilter;
        fileName = `방문자기록_${farm?.farm_name}_${csvStartDate || "전체"}_${
          csvEndDate || "전체"
        }_${new Date().toISOString().split("T")[0]}.csv`;
      }

      // CSV 헤더 설정
      const headers = includeAllFarms
        ? [
            "농장명",
            "농장유형",
            "방문일시",
            "성명",
            "연락처",
            "주소",
            "차량번호",
            "방문목적",
            "소독여부",
            "비고",
          ]
        : [
            "방문일시",
            "성명",
            "연락처",
            "주소",
            "차량번호",
            "방문목적",
            "소독여부",
            "비고",
          ];

      // CSV 데이터 생성
      const csvData = dataToDownload.map((visitor: any) => {
        const baseData = [
          new Date(visitor.entry_datetime).toLocaleString("ko-KR"),
          visitor.full_name,
          visitor.phone_number,
          visitor.address,
          visitor.car_plate_number || "",
          visitor.visit_purpose,
          visitor.disinfection_check ? "완료" : "미완료",
          visitor.notes || "",
        ];

        if (includeAllFarms) {
          return [
            visitor.farms?.farm_name || "알 수 없음",
            visitor.farms?.farm_type || "알 수 없음",
            ...baseData,
          ];
        }

        return baseData;
      });

      // CSV 파일 생성 및 다운로드 (한글 깨짐 해결: UTF-8 BOM 추가)
      const csvContent = [headers, ...csvData]
        .map((row) => row.map((field) => `"${field}"`).join(","))
        .join("\n");

      // UTF-8 BOM 추가로 한글 깨짐 해결
      const BOM = "\uFEFF";
      const csvWithBOM = BOM + csvContent;

      const blob = new Blob([csvWithBOM], {
        type: "text/csv;charset=utf-8;",
      });

      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", fileName);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log(`CSV 다운로드 완료: ${dataToDownload.length}건의 기록`);
    } catch (error) {
      console.error("CSV 다운로드 오류:", error);
      alert("CSV 다운로드 중 오류가 발생했습니다.");
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>방문자 기록을 불러오는 중...</p>
        </div>
      </div>
    );
  }

  if (!farm) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-center text-red-600">오류</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-center">농장 정보를 찾을 수 없습니다.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:p-6 pt-2 md:pt-4">
      {/* 브레드크럼 */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/admin/dashboard" className="flex items-center gap-1">
                <Home className="h-4 w-4" />
                대시보드
              </Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/admin/farms">농장 관리</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>방문자 기록</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* 헤더 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <SidebarTrigger />
          <div>
            <h2 className="text-3xl font-bold tracking-tight mb-4 flex items-center gap-2">
              <Users className="h-8 w-8 text-primary" />
              방문자 기록 관리
            </h2>
            <p className="text-muted-foreground mt-2">
              농장별 방문자 출입 기록을 조회하고 관리하세요
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                CSV 다운로드
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>CSV 다운로드 옵션</DialogTitle>
                <DialogDescription>
                  다운로드할 데이터 범위와 기간을 선택하세요.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                {/* 농장 범위 선택 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">데이터 범위</label>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeAllFarms"
                      checked={includeAllFarms}
                      onCheckedChange={setIncludeAllFarms}
                    />
                    <label
                      htmlFor="includeAllFarms"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      모든 농장 포함 (현재 농장: {farm?.farm_name})
                    </label>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {includeAllFarms
                      ? "접근 가능한 모든 농장의 방문자 기록을 다운로드합니다."
                      : "현재 선택된 농장의 방문자 기록만 다운로드합니다."}
                  </p>
                </div>

                {/* 기간 선택 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    기간 선택 (선택사항)
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="text-xs text-muted-foreground">
                        시작일
                      </label>
                      <Input
                        type="date"
                        value={csvStartDate}
                        onChange={(e) => setCsvStartDate(e.target.value)}
                        className="w-full"
                      />
                    </div>
                    <div>
                      <label className="text-xs text-muted-foreground">
                        종료일
                      </label>
                      <Input
                        type="date"
                        value={csvEndDate}
                        onChange={(e) => setCsvEndDate(e.target.value)}
                        className="w-full"
                      />
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    기간을 지정하지 않으면 모든 기록을 다운로드합니다. (최대 5년
                    범위)
                  </p>
                </div>

                {/* 미리보기 정보 */}
                <div className="bg-muted/50 p-3 rounded-lg">
                  <h4 className="text-sm font-medium mb-2">
                    다운로드 미리보기
                  </h4>
                  <div className="text-xs space-y-1">
                    <div>
                      • 범위: {includeAllFarms ? "모든 농장" : farm?.farm_name}
                    </div>
                    <div>
                      • 기간: {csvStartDate || "전체"} ~ {csvEndDate || "전체"}
                    </div>
                    <div>• 현재 표시된 기록: {filteredVisitors.length}건</div>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIncludeAllFarms(false);
                    setCsvStartDate("");
                    setCsvEndDate("");
                  }}
                >
                  초기화
                </Button>
                <Button onClick={downloadCSV}>
                  <Download className="h-4 w-4 mr-2" />
                  다운로드
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* 농장 선택 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            농장 선택
          </CardTitle>
          <CardDescription>
            방문자 기록을 조회할 농장을 선택하세요
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <Select value={farmId} onValueChange={handleFarmChange}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="농장을 선택하세요" />
                </SelectTrigger>
                <SelectContent>
                  {farms.map((farmOption) => (
                    <SelectItem key={farmOption.id} value={farmOption.id}>
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4" />
                        <div>
                          <div className="font-medium">
                            {farmOption.farm_name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {farmOption.farm_type || "일반"}
                          </div>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {farm && (
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  <span className="font-medium">{farm.farm_name}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  <span>총 {visitors.length}건의 방문 기록</span>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 검색 및 필터 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            검색 및 필터
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="이름, 전화번호, 방문목적으로 검색..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="w-48">
              <Input
                type="date"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                className="w-full"
              />
            </div>
            {(searchTerm || dateFilter) && (
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm("");
                  setDateFilter("");
                }}
              >
                초기화
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 통계 카드 */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">총 방문자</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{visitors.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">오늘 방문자</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {
                visitors.filter(
                  (v) =>
                    new Date(v.entry_datetime).toDateString() ===
                    new Date().toDateString()
                ).length
              }
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">소독 완료</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {visitors.filter((v) => v.disinfection_check).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">검색 결과</CardTitle>
            <Search className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredVisitors.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* 방문자 목록 테이블 */}
      <Card>
        <CardHeader>
          <CardTitle>방문자 목록</CardTitle>
          <CardDescription>
            최근 방문자부터 표시됩니다. 총 {filteredVisitors.length}건의 기록이
            있습니다.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredVisitors.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {searchTerm || dateFilter
                  ? "검색 조건에 맞는 방문자가 없습니다."
                  : "아직 방문자가 없습니다."}
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>방문일시</TableHead>
                    <TableHead>성명</TableHead>
                    <TableHead>연락처</TableHead>
                    <TableHead>방문목적</TableHead>
                    <TableHead>차량번호</TableHead>
                    <TableHead>소독여부</TableHead>
                    <TableHead>상세</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredVisitors.map((visitor) => (
                    <TableRow key={visitor.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <div className="font-medium">
                              {new Date(
                                visitor.entry_datetime
                              ).toLocaleDateString("ko-KR")}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {new Date(
                                visitor.entry_datetime
                              ).toLocaleTimeString("ko-KR")}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">
                            {visitor.full_name}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <span>{visitor.phone_number}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <span>{visitor.visit_purpose}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {visitor.car_plate_number ? (
                          <div className="flex items-center gap-2">
                            <Car className="h-4 w-4 text-muted-foreground" />
                            <Badge variant="outline">
                              {visitor.car_plate_number}
                            </Badge>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {visitor.disinfection_check ? (
                          <Badge
                            variant="default"
                            className="bg-green-100 text-green-800"
                          >
                            <CheckCircle className="h-3 w-3 mr-1" />
                            완료
                          </Badge>
                        ) : (
                          <Badge
                            variant="secondary"
                            className="bg-red-100 text-red-800"
                          >
                            <XCircle className="h-3 w-3 mr-1" />
                            미완료
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1 text-sm">
                          <div className="flex items-start gap-2">
                            <MapPin className="h-3 w-3 text-muted-foreground mt-0.5" />
                            <span className="text-xs text-muted-foreground">
                              {visitor.address}
                            </span>
                          </div>
                          {visitor.notes && (
                            <div className="flex items-start gap-2">
                              <FileText className="h-3 w-3 text-muted-foreground mt-0.5" />
                              <span className="text-xs text-muted-foreground">
                                {visitor.notes}
                              </span>
                            </div>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
