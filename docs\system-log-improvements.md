# 시스템 로그 시스템 개선사항

## 📅 업데이트 날짜: 2025-06-14

## 🎯 개선 목표
- 중복 코드 제거 및 코드 재사용성 향상
- 통합된 로그 시스템으로 일관성 확보
- RLS (Row Level Security) 정책 최적화
- 성능 향상 및 사용자 경험 개선

---

## ✅ 주요 개선사항

### 1. **공통 시스템 로그 유틸리티 구현**

#### 📍 위치: `lib/utils/system-log.ts`

#### 🔧 핵심 기능:

##### **통합 로그 생성 함수**
```typescript
export const createSystemLog = async (
  action: string,
  message: string,
  level: LogLevel = "info",
  userId?: string,
  resourceType?: ResourceType,
  resourceId?: string,
  metadata?: any,
  userEmail?: string,
  userIP?: string
): Promise<boolean>
```

##### **특화된 헬퍼 함수들**
- **`createAuthLog`**: 인증 관련 로그 (로그인, 로그아웃, 실패 등)
- **`createFarmLog`**: 농장 관련 로그 (생성, 수정, 삭제, 상태 변경)
- **`createVisitorLog`**: 방문자 관련 로그 (등록, 체크인, 체크아웃)
- **`createErrorLog`**: 에러 로그 (시스템 오류, 예외 상황)

#### 💡 사용 예시:
```typescript
// 기본 로그
await createSystemLog("USER_LOGIN", "사용자가 로그인했습니다");

// 농장 로그
await createFarmLog("CREATED", "새농장", farmId, userId);

// 방문자 로그
await createVisitorLog("CREATED", "홍길동", "새농장", visitorId);

// 에러 로그
await createErrorLog("DATABASE_ERROR", error, "사용자 등록 중");
```

### 2. **실제 IP 주소 수집 시스템**

#### 🔧 기능:
- **다중 IP 서비스**: 여러 외부 서비스를 통한 실제 IP 수집
- **자동 폴백**: 서비스 실패 시 다음 서비스로 자동 전환
- **에러 처리**: IP 수집 실패 시에도 로그 생성 계속 진행

#### 📋 지원 서비스:
```typescript
const ipServices = [
  "https://api.ipify.org?format=json",
  "https://ipapi.co/json/",
  "https://api.ip.sb/jsonip",
];
```

### 3. **자동 사용자 정보 수집**

#### 🔧 기능:
- **자동 이메일 조회**: userId 기반으로 사용자 이메일 자동 수집
- **User Agent 수집**: 브라우저 및 디바이스 정보 자동 수집
- **메타데이터 지원**: 추가 컨텍스트 정보 JSON 형태로 저장

### 4. **중복 코드 제거**

#### 📊 개선 효과:
- **이전**: 6개 파일에서 총 ~200줄의 중복된 `createSystemLog` 함수들
- **이후**: 1개 공통 유틸리티 파일에서 중앙 관리
- **절약**: 150줄 이상의 중복 코드 제거

#### 📍 수정된 파일들:
- `app/visit/[farmId]/page.tsx` - 방문자 등록 페이지
- `app/admin/management/page.tsx` - 관리 페이지
- `store/use-farm-members-store.ts` - 농장 구성원 스토어
- `components/providers/auth-provider.tsx` - 인증 프로바이더
- `app/login/page.tsx` - 로그인 페이지
- `app/register/page.tsx` - 회원가입 페이지

---

## 🔒 RLS (Row Level Security) 정책 개선

### **문제점 해결**

#### **이전 문제:**
```sql
-- 타입 오류 발생
user_id = auth.uid()::text  -- UUID vs TEXT 타입 불일치
```

#### **해결책:**
```sql
-- UUID 타입 준수
user_id = auth.uid()  -- 올바른 UUID 타입 사용
```

### **최적화된 RLS 정책**

#### 📍 위치: `scripts/database-reset-and-rebuild.sql`

#### 🔧 정책 구조:

##### **1. 사용자 조회 정책**
```sql
CREATE POLICY "Users can view own logs" ON public.system_logs
    FOR SELECT USING (user_id = auth.uid());
```

##### **2. 관리자 조회 정책**
```sql
CREATE POLICY "Admins can view all logs" ON public.system_logs
    FOR SELECT USING (public.is_system_admin(auth.uid()));
```

##### **3. 로그 생성 정책**
```sql
CREATE POLICY "Authenticated users can create logs" ON public.system_logs
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND (
            user_id = auth.uid() OR user_id IS NULL
        )
    );
```

##### **4. 관리자 전체 권한**
```sql
CREATE POLICY "Admins can manage all logs" ON public.system_logs
    FOR ALL USING (public.is_system_admin(auth.uid()));
```

---

## 🚀 성능 및 사용자 경험 개선

### **백그라운드 로그 처리**

#### **이전 방식:**
```typescript
// UI 블로킹 발생
await createSystemLog(...);
// 로그 완료까지 대기
```

#### **개선된 방식:**
```typescript
// 백그라운드 처리
createSystemLog(...).then(() => {
  console.log("✅ Log created");
}).catch((error) => {
  console.warn("⚠️ Log failed:", error);
});
// 즉시 다음 작업 진행
```

### **에러 복구 메커니즘**

#### 🔧 기능:
- **로그 생성 실패 시 재시도**: error 레벨로 실패 로그 생성
- **무한 루프 방지**: 재시도 시 레벨 체크로 무한 루프 방지
- **사용자 경험 보호**: 로그 실패해도 주요 기능 동작 계속

---

## 📊 로그 데이터 구조

### **완전한 스키마 지원**

#### 📋 필드 구조:
```sql
CREATE TABLE public.system_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- 로그 기본 정보
    level TEXT NOT NULL DEFAULT 'info',
    action TEXT NOT NULL,
    message TEXT NOT NULL,
    
    -- 사용자 정보
    user_id UUID REFERENCES public.profiles(id),
    user_email TEXT,
    user_ip TEXT,
    user_agent TEXT,
     
    -- 관련 리소스 정보
    resource_type TEXT,
    resource_id UUID,
    
    -- 추가 데이터 (JSON)
    metadata JSONB,
    
    -- 타임스탬프
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### **메타데이터 활용 예시**

#### **농장 로그:**
```json
{
  "farm_name": "새농장",
  "farm_type": "축산",
  "action_type": "CREATED",
  "owner_id": "user-uuid"
}
```

#### **방문자 로그:**
```json
{
  "visitor_name": "홍길동",
  "farm_name": "새농장",
  "visit_purpose": "업무",
  "registration_method": "qr_code"
}
```

#### **인증 로그:**
```json
{
  "action_type": "LOGIN",
  "user_info": "홍길동",
  "login_method": "email",
  "timestamp": "2025-06-14T10:30:00Z"
}
```

---

## 🔍 디버깅 및 모니터링

### **상세한 디버깅 로그**

#### 📋 로그 레벨:
```
🔄 Creating system log: USER_LOGIN for user: [UUID]
📍 Client IP: [실제IP주소]
📧 User email found: [이메일]
📝 Log data prepared: [로그데이터]
✅ System log created successfully: USER_LOGIN - [메시지]
```

### **에러 추적**

#### 📋 실패 시 로그:
```
❌ Failed to create system log: [에러메시지]
❌ Error details: [상세에러]
❌ Log data that failed: [실패한데이터]
⚠️ Retrying with error level...
```

---

## 📈 통계 및 분석 지원

### **로그 기반 통계**

#### 🔧 활용 가능한 분석:
- **사용자 활동 패턴**: 로그인/로그아웃 시간 분석
- **농장 운영 현황**: 농장별 활동 로그 분석
- **방문자 트렌드**: 방문자 등록 패턴 분석
- **시스템 안정성**: 에러 로그 기반 시스템 모니터링

### **실시간 모니터링**

#### 📋 지원 기능:
- **실시간 로그 스트림**: 시스템 활동 실시간 추적
- **알림 시스템**: 중요 이벤트 발생 시 알림
- **대시보드 연동**: 통계 대시보드에서 로그 데이터 활용

---

## 🔧 유지보수성 향상

### **코드 재사용성**

#### **Before (이전):**
- 각 파일마다 개별 로그 함수 구현
- 일관성 없는 로그 형식
- 중복된 에러 처리 로직

#### **After (개선 후):**
- 중앙 집중식 로그 관리
- 일관된 로그 형식 및 구조
- 통합된 에러 처리 및 복구

### **확장성**

#### 🔧 새로운 로그 타입 추가:
```typescript
// 새로운 특화 함수 쉽게 추가 가능
export const createPaymentLog = async (
  action: "PAYMENT_SUCCESS" | "PAYMENT_FAILED",
  amount: number,
  userId?: string
) => {
  return await createSystemLog(
    `PAYMENT_${action}`,
    `결제 ${action === "PAYMENT_SUCCESS" ? "성공" : "실패"}: ${amount}원`,
    action === "PAYMENT_SUCCESS" ? "info" : "error",
    userId,
    "payment",
    undefined,
    { amount, currency: "KRW" }
  );
};
```

---

## 📚 관련 문서
- [인증 시스템 개선사항](./authentication-improvements.md)
- [데이터베이스 스키마](./database-schema.md)
- [RLS 정책 가이드](./rls-policy-guide.md)
- [API 문서](./api-documentation.md)
