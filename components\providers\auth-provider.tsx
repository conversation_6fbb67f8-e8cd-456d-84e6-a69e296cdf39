"use client";

import type React from "react";
import {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import { useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";
import { updateLoginTime } from "@/lib/auth-helpers";
import { createSystemLog } from "@/lib/utils/system-log";

interface Profile {
  id: string;
  email: string;
  name: string;
  account_type: "admin" | "user";
  phone?: string;
  position?: string;
  department?: string;
  bio?: string;
  profile_image_url?: string;
  company_name?: string;
  company_address?: string;
  business_type?: string;
  company_description?: string;
  establishment_date?: string;
  employee_count?: string;
  company_website?: string;
  last_login_at?: string;
  password_changed_at?: string;
}

interface AuthContextType {
  profile: Profile | null;
  loading: boolean;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const { toast } = useToast();

  // 프로필 정보를 가져오는 함수
  const fetchProfile = useCallback(
    async (userId: string, isNewLogin = false) => {
      console.log("📥 Fetching profile for user:", userId);
      console.log("🆕 Is new login:", isNewLogin);

      // 이미 프로필이 있고 ID가 같다면 다시 가져오지 않음 (새 로그인이 아닌 경우)
      if (profile?.id === userId && !isNewLogin) {
        console.log("⏭️ Skipping profile fetch - already loaded");
        return;
      }

      try {
        // 새 로그인인 경우 로그인 시간 업데이트
        if (isNewLogin) {
          console.log("⏰ Updating login time...");
          await updateLoginTime(userId);
          console.log("✅ Login time updated for user:", userId);
        }

        console.log("🔍 Fetching profile data from Supabase...");
        const { data: profileData, error: profileError } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", userId)
          .maybeSingle();

        if (profileError) {
          console.error("❌ Profile fetch error:", profileError);
          throw profileError;
        }

        if (profileData) {
          console.log("✅ Profile data received:", profileData);
          setProfile(profileData as Profile);
        } else {
          console.warn("⚠️ No profile data found for user:", userId);
        }
      } catch (error) {
        console.error("❌ Error fetching profile:", error);
        toast({
          title: "오류",
          description: "프로필 정보를 가져오는데 실패했습니다.",
          variant: "destructive",
        });
      }
    },
    [profile?.id, toast]
  );

  // 세션 상태 처리 함수
  const handleSessionChange = useCallback(
    async (session: any | null, isNewLogin = false) => {
      console.log("🔄 Session change detected");
      console.log("📡 Session data:", session);
      console.log("🆕 Is new login:", isNewLogin);

      // 세션이 없는 경우 이미지 업로드 상태 초기화
      if (!session) {
        console.log("🧹 Cleaning up session storage...");
        sessionStorage.removeItem("profile_image_uploading");
      }

      // 이미지 업로드 중인지 확인
      const isUploading = sessionStorage.getItem("profile_image_uploading");
      console.log(
        "📤 Image upload status:",
        isUploading ? "uploading" : "not uploading"
      );

      if (session?.user) {
        console.log("👤 User found in session, fetching profile...");
        await fetchProfile(session.user.id, isNewLogin);
        // 프로필 로드 완료 후 업로드 상태 초기화
        sessionStorage.removeItem("profile_image_uploading");
      } else {
        console.log("🔄 No user in session, clearing profile state...");
        setProfile(null);
      }
      setLoading(false);
      console.log("✅ Session change handling completed");
    },
    [fetchProfile]
  );

  // 초기 세션 확인
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const {
          data: { session },
          error,
        } = await supabase.auth.getSession();

        if (error) {
          throw error;
        }

        // 세션이 있는 경우 세션 검증 로그 생성
        if (session?.user) {
          await createSystemLog(
            "SESSION_VALIDATED",
            `사용자 세션이 검증되었습니다 (페이지 새로고침)`,
            "info",
            session.user.id,
            "user",
            session.user.id,
            {
              user_id: session.user.id,
              user_email: session.user.email,
              validation_type: "page_refresh",
              timestamp: new Date().toISOString(),
            }
          );
        }

        await handleSessionChange(session);
      } catch (error) {
        console.error("Error initializing auth:", error);

        // 세션 초기화 실패 로그
        await createSystemLog(
          "SESSION_INIT_FAILED",
          `세션 초기화 실패: ${
            error instanceof Error ? error.message : "알 수 없는 오류"
          }`,
          "error",
          undefined,
          "system",
          undefined,
          {
            error_message:
              error instanceof Error ? error.message : "알 수 없는 오류",
            context: "auth_initialization",
          }
        );

        // 새로고침 시에는 토스트를 표시하지 않고 조용히 처리
        console.warn("⚠️ Auth initialization failed, continuing without toast");
        setLoading(false);
      }
    };

    initializeAuth();

    // 세션 변경 이벤트 구독
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log("🔄 Auth state change:", event);

      // 세션 변경 로그 생성
      if (session?.user) {
        await createSystemLog(
          "AUTH_STATE_CHANGED",
          `인증 상태가 변경되었습니다: ${event}`,
          "info",
          session.user.id,
          "user",
          session.user.id,
          {
            event_type: event,
            user_id: session.user.id,
            user_email: session.user.email,
            timestamp: new Date().toISOString(),
          }
        );
      }

      // 새 로그인인 경우에만 isNewLogin을 true로 설정
      const isNewLogin = event === "SIGNED_IN";
      await handleSessionChange(session, isNewLogin);
    });

    // 클린업 함수
    return () => {
      subscription.unsubscribe();
    };
  }, [handleSessionChange, toast]);

  // 세션 변경 감지
  useEffect(() => {
    // 개발용: 세션 만료 테스트 함수를 전역에 추가
    if (typeof window !== "undefined") {
      (window as any).testSessionExpiry = async () => {
        console.log("🧪 Testing session expiry...");
        await supabase.auth.signOut();
        console.log("🧪 Session expiry test completed");
      };

      // 개발용: 인증 상태 강제 복구 함수 추가
      (window as any).resetAuthState = () => {
        console.log("🔧 Resetting auth state...");
        setLoading(false);
        setProfile(null);
        console.log("✅ Auth state reset completed");
      };

      // 개발용: 현재 인증 상태 확인 함수 추가
      (window as any).checkAuthState = () => {
        console.log("🔍 Current auth state:", {
          loading,
          profile: profile
            ? {
                id: profile.id,
                email: profile.email,
                account_type: profile.account_type,
              }
            : null,
        });
        return { loading, profile };
      };

      // 개발용: 세션 강제 새로고침 함수 추가
      (window as any).refreshSession = async () => {
        console.log("🔄 Refreshing session...");
        try {
          const { data, error } = await supabase.auth.refreshSession();
          if (error) throw error;
          console.log("✅ Session refreshed successfully");
          return data;
        } catch (error) {
          console.error("❌ Session refresh failed:", error);
          return null;
        }
      };
    }

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log("Auth state change:", event, session?.user?.id);

      switch (event) {
        case "SIGNED_IN":
          // 프로필 업로드 중이 아닐 때만 로그인 시간 업데이트
          const isUploading = sessionStorage.getItem("profile_image_uploading");
          await handleSessionChange(session, !isUploading);
          break;
        case "SIGNED_OUT":
          console.log("🔄 SIGNED_OUT event triggered");

          // 수동 로그아웃이 아닌 경우에만 세션 만료 로그 생성
          // (수동 로그아웃은 이미 signOut 함수에서 로그 생성됨)
          const isManualLogout = sessionStorage.getItem("manual_logout");
          if (profile && !isManualLogout) {
            console.log("🔄 Creating session expired log...");

            // 세션 만료 로그를 await로 처리하여 확실하게 생성
            try {
              await createSystemLog(
                "SESSION_EXPIRED",
                `사용자 "${
                  profile.name || profile.email
                }"의 세션이 만료되었습니다`,
                "info",
                profile.id,
                "user",
                profile.id,
                {
                  user_name: profile.name,
                  user_email: profile.email,
                  account_type: profile.account_type,
                  logout_type: "session_expired",
                  expired_at: new Date().toISOString(),
                }
              );
              console.log("✅ Session expired log created successfully");
            } catch (logError) {
              console.warn(
                "⚠️ Failed to create session expired log:",
                logError
              );

              // RLS 정책 문제일 경우 대안 방법 시도
              try {
                console.log("🔄 Trying alternative session expired log...");
                await createSystemLog(
                  "SESSION_EXPIRED",
                  `사용자 세션이 만료되었습니다 (사용자: ${
                    profile.name || profile.email
                  })`,
                  "info",
                  undefined, // userId를 undefined로 설정
                  "user",
                  undefined,
                  {
                    user_name: profile.name,
                    user_email: profile.email,
                    account_type: profile.account_type,
                    logout_type: "session_expired_fallback",
                    expired_at: new Date().toISOString(),
                    original_user_id: profile.id,
                  }
                );
                console.log("✅ Alternative session expired log created");
              } catch (fallbackError) {
                console.error(
                  "❌ All session expired log attempts failed:",
                  fallbackError
                );
              }
            }
          } else if (isManualLogout) {
            console.log(
              "🔄 Manual logout detected, skipping session expired log"
            );
            // 수동 로그아웃 플래그 제거
            sessionStorage.removeItem("manual_logout");
          } else if (!profile) {
            console.log("🔄 No profile available for session expired log");
          }

          // 즉시 상태 변경 및 리다이렉트
          await handleSessionChange(null, false);
          console.log("🔄 Forcing redirect after SIGNED_OUT...");
          window.location.href = "/login";
          break;
        case "TOKEN_REFRESHED":
          // 토큰 갱신은 새 로그인이 아님
          await handleSessionChange(session, false);
          break;
      }
    });

    return () => subscription.unsubscribe();
  }, [handleSessionChange, router]);

  const signOut = async () => {
    try {
      console.log("🔄 Starting logout process...");

      // 로그아웃 전에 사용자 정보 저장 (로그용)
      const currentUser = profile;

      // 1. 수동 로그아웃 플래그 설정 (중복 로그 방지)
      sessionStorage.setItem("manual_logout", "true");

      // 2. 로그아웃 로그 먼저 생성 (Supabase 로그아웃 전에)
      console.log("🔄 Creating logout log before signOut...");
      try {
        await createSystemLog(
          "USER_LOGOUT",
          `사용자 "${
            currentUser?.name || currentUser?.email || "알 수 없음"
          }"가 로그아웃했습니다`,
          "info",
          currentUser?.id,
          "user",
          currentUser?.id,
          {
            user_name: currentUser?.name,
            user_email: currentUser?.email,
            account_type: currentUser?.account_type,
            logout_type: "manual",
          }
        );
        console.log("✅ Logout log created successfully");
      } catch (logError) {
        console.warn("⚠️ Failed to create logout log:", logError);
        // 로그 생성 실패해도 로그아웃은 계속 진행
      }

      // 2. Supabase 로그아웃
      const { error } = await supabase.auth.signOut();

      if (error) {
        throw error;
      }

      console.log("✅ Supabase logout successful");

      // 3. 로컬 상태 초기화
      await handleSessionChange(null);
      console.log("✅ Local state cleared");

      // 4. 즉시 강제 리다이렉트 (Next.js 라우터 우회)
      console.log("🔄 Current path before redirect:", window.location.pathname);
      console.log("🔄 Force redirecting to login page...");

      // 즉시 강제 리다이렉트 (가장 확실한 방법)
      window.location.href = "/login";

      console.log("🔄 Redirect command executed");

      // 5. 성공 메시지 표시
      toast({
        title: "로그아웃",
        description: "성공적으로 로그아웃되었습니다.",
      });

      console.log("✅ Logout process completed");
    } catch (error) {
      console.error("❌ Logout error:", error);

      // 로그아웃 실패 로그 생성
      try {
        await createSystemLog(
          "USER_LOGOUT_FAILED",
          `로그아웃 실패: ${
            error instanceof Error ? error.message : "알 수 없는 오류"
          }`,
          "error",
          profile?.id,
          "user",
          profile?.id,
          {
            user_name: profile?.name,
            user_email: profile?.email,
            error_message:
              error instanceof Error ? error.message : "알 수 없는 오류",
            logout_type: "manual_failed",
          }
        );
      } catch (logError) {
        console.warn("⚠️ Failed to create logout error log:", logError);
      }

      // 에러가 발생해도 강제로 로그아웃 처리
      console.log("🔄 Force logout due to error...");
      // 수동 로그아웃 플래그 제거 (에러 발생 시에도)
      sessionStorage.removeItem("manual_logout");
      await handleSessionChange(null);

      // 즉시 강제 리다이렉트
      console.log("🔄 Error force redirect to login...");
      window.location.href = "/login";

      toast({
        title: "오류",
        description: "로그아웃 처리 중 오류가 발생했지만 로그아웃되었습니다.",
        variant: "destructive",
      });
    }
  };

  return (
    <AuthContext.Provider value={{ profile, loading, signOut }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
