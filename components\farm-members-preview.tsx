"use client";

import { Users } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { FarmMemberPreview } from "@/hooks/use-farm-members-preview-safe";
import { getRoleInfo } from "@/components/role-badge";

interface FarmMembersPreviewProps {
  memberCount: number;
  members: FarmMemberPreview[];
  loading?: boolean;
}

const getRoleColor = (role: string) => {
  const roleInfo = getRoleInfo(role as any);
  if (!roleInfo) return "bg-gray-500 text-white";

  switch (role) {
    case "owner":
      return "bg-purple-500 text-white border-purple-600";
    case "manager":
      return "bg-blue-500 text-white border-blue-600";
    case "viewer":
      return "bg-green-500 text-white border-green-600";
    default:
      return "bg-gray-500 text-white border-gray-600";
  }
};

const getRoleIcon = (role: string) => {
  const roleInfo = getRoleInfo(role as any);
  if (!roleInfo) return "👤";

  switch (role) {
    case "admin":
      return "👑";
    case "owner":
      return "🛡️";
    case "manager":
      return "👨‍💼";
    case "viewer":
      return "👁️";
    default:
      return "👤";
  }
};

export function FarmMembersPreview({
  memberCount,
  members,
  loading = false,
}: FarmMembersPreviewProps) {
  if (loading) {
    return (
      <div className="flex items-center justify-between pt-3 border-t border-border/50">
        <div className="flex items-center space-x-2">
          <Users className="h-4 w-4 text-muted-foreground" />
          <span className="text-xs text-muted-foreground">
            구성원 로딩 중...
          </span>
        </div>
        <div className="flex -space-x-2">
          {[1, 2, 3].map((i) => (
            <div
              key={i}
              className="w-6 h-6 rounded-full bg-muted border-2 border-background animate-pulse"
            />
          ))}
        </div>
      </div>
    );
  }

  if (memberCount === 0) {
    return (
      <div className="flex items-center justify-between pt-3 border-t border-border/50">
        <div className="flex items-center space-x-2">
          <Users className="h-4 w-4 text-muted-foreground" />
          <span className="text-xs text-muted-foreground">구성원 없음</span>
        </div>
        <Badge variant="outline" className="text-xs">
          구성원 추가 필요
        </Badge>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-between pt-3 border-t border-border/50">
      <div className="flex items-center space-x-2">
        <Users className="h-4 w-4 text-muted-foreground" />
        <span className="text-xs text-muted-foreground">
          구성원 {memberCount}명
        </span>
      </div>

      <div className="flex items-center space-x-1">
        {/* 구성원 아바타 */}
        <div className="flex -space-x-2">
          {members.slice(0, 3).map((member, index) => (
            <div
              key={member.id}
              className={`w-6 h-6 rounded-full border-2 border-background flex items-center justify-center text-xs font-medium ${getRoleColor(
                member.role
              )}`}
              title={`${member.name} (${member.role})`}
            >
              {member.name.charAt(0).toUpperCase()}
            </div>
          ))}

          {/* 더 많은 구성원이 있을 때 +N 표시 */}
          {memberCount > 3 && (
            <div className="w-6 h-6 rounded-full bg-muted border-2 border-background flex items-center justify-center text-xs font-medium text-muted-foreground">
              +{memberCount - 3}
            </div>
          )}
        </div>

        {/* 역할 요약 배지 */}
        {memberCount > 0 && (
          <div className="flex space-x-1 ml-2">
            {members.some((m) => m.role === "owner") && (
              <span className="text-xs" title="소유자">
                🛡️
              </span>
            )}
            {members.some((m) => m.role === "manager") && (
              <span className="text-xs" title="관리자">
                👨‍💼
              </span>
            )}
            {members.some((m) => m.role === "viewer") && (
              <span className="text-xs" title="조회자">
                👁️
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
