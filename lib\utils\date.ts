/**
 * 날짜/시간 포맷팅 유틸리티
 * 
 * 여러 페이지에서 사용되는 공통 날짜/시간 처리 로직을 모아둔 유틸리티입니다.
 */

/**
 * 한국 시간대로 날짜/시간 포맷팅
 * @param date 포맷팅할 날짜
 * @param options 포맷 옵션
 * @returns 포맷된 날짜/시간 문자열
 */
export const formatDateTime = (
  date: string | Date,
  options?: Intl.DateTimeFormatOptions
): string => {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    timeZone: "Asia/Seoul",
  };
  
  return dateObj.toLocaleString("ko-KR", { ...defaultOptions, ...options });
};

/**
 * 날짜만 포맷팅 (시간 제외)
 * @param date 포맷팅할 날짜
 * @returns 포맷된 날짜 문자열 (YYYY-MM-DD)
 */
export const formatDate = (date: string | Date): string => {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  return dateObj.toLocaleDateString("ko-KR", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    timeZone: "Asia/Seoul",
  });
};

/**
 * 시간만 포맷팅 (날짜 제외)
 * @param date 포맷팅할 날짜
 * @returns 포맷된 시간 문자열 (HH:MM:SS)
 */
export const formatTime = (date: string | Date): string => {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  return dateObj.toLocaleTimeString("ko-KR", {
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    timeZone: "Asia/Seoul",
  });
};

/**
 * 상대적 시간 표시 (몇 분 전, 몇 시간 전 등)
 * @param date 기준 날짜
 * @returns 상대적 시간 문자열
 */
export const formatTimeAgo = (date: string | Date): string => {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  const now = new Date();
  const diffInMinutes = Math.floor(
    (now.getTime() - dateObj.getTime()) / (1000 * 60)
  );

  if (diffInMinutes < 1) {
    return "방금 전";
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes}분 전`;
  } else if (diffInMinutes < 1440) {
    return `${Math.floor(diffInMinutes / 60)}시간 전`;
  } else if (diffInMinutes < 10080) {
    return `${Math.floor(diffInMinutes / 1440)}일 전`;
  } else {
    return formatDate(dateObj);
  }
};

/**
 * 오늘 날짜 문자열 반환 (YYYY-MM-DD)
 * @returns 오늘 날짜 문자열
 */
export const getTodayString = (): string => {
  return new Date().toISOString().split("T")[0];
};

/**
 * 현재 날짜/시간 문자열 반환 (한국 시간대)
 * @returns 현재 날짜/시간 문자열
 */
export const getCurrentDateTime = (): string => {
  return formatDateTime(new Date());
};

/**
 * 날짜 범위 내 데이터 필터링
 * @param data 필터링할 데이터 배열
 * @param dateField 날짜 필드명
 * @param startDate 시작 날짜 (YYYY-MM-DD)
 * @param endDate 종료 날짜 (YYYY-MM-DD)
 * @returns 필터링된 데이터
 */
export const filterByDateRange = <T extends Record<string, any>>(
  data: T[],
  dateField: keyof T,
  startDate?: string,
  endDate?: string
): T[] => {
  if (!startDate && !endDate) return data;
  
  return data.filter((item) => {
    const itemDate = new Date(item[dateField]);
    const itemDateString = itemDate.toISOString().split("T")[0];
    
    if (startDate && itemDateString < startDate) return false;
    if (endDate && itemDateString > endDate) return false;
    
    return true;
  });
};

/**
 * 오늘 데이터 필터링
 * @param data 필터링할 데이터 배열
 * @param dateField 날짜 필드명
 * @returns 오늘 데이터만 필터링된 배열
 */
export const getTodayData = <T extends Record<string, any>>(
  data: T[],
  dateField: keyof T
): T[] => {
  const today = getTodayString();
  return data.filter((item) => {
    const itemDate = new Date(item[dateField]);
    const itemDateString = itemDate.toISOString().split("T")[0];
    return itemDateString === today;
  });
};

/**
 * 이번 주 데이터 필터링
 * @param data 필터링할 데이터 배열
 * @param dateField 날짜 필드명
 * @returns 이번 주 데이터만 필터링된 배열
 */
export const getThisWeekData = <T extends Record<string, any>>(
  data: T[],
  dateField: keyof T
): T[] => {
  const weekAgo = new Date();
  weekAgo.setDate(weekAgo.getDate() - 7);
  
  return data.filter((item) => {
    const itemDate = new Date(item[dateField]);
    return itemDate >= weekAgo;
  });
};

/**
 * 이번 달 데이터 필터링
 * @param data 필터링할 데이터 배열
 * @param dateField 날짜 필드명
 * @returns 이번 달 데이터만 필터링된 배열
 */
export const getThisMonthData = <T extends Record<string, any>>(
  data: T[],
  dateField: keyof T
): T[] => {
  const now = new Date();
  const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  
  return data.filter((item) => {
    const itemDate = new Date(item[dateField]);
    return itemDate >= firstDayOfMonth;
  });
};

/**
 * 날짜 범위 계산 (시작일부터 종료일까지의 일수)
 * @param startDate 시작 날짜
 * @param endDate 종료 날짜
 * @returns 일수
 */
export const calculateDateRangeDays = (
  startDate: string | Date,
  endDate: string | Date
): number => {
  const start = typeof startDate === "string" ? new Date(startDate) : startDate;
  const end = typeof endDate === "string" ? new Date(endDate) : endDate;
  
  const diffTime = end.getTime() - start.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

/**
 * 날짜가 유효한지 확인
 * @param date 확인할 날짜
 * @returns 유효한 날짜인지 여부
 */
export const isValidDate = (date: string | Date): boolean => {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  return !isNaN(dateObj.getTime());
};

/**
 * 파일명용 날짜 문자열 생성 (YYYYMMDD_HHMMSS)
 * @param date 날짜 (기본값: 현재 시간)
 * @returns 파일명용 날짜 문자열
 */
export const getFileNameDate = (date?: Date): string => {
  const dateObj = date || new Date();
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, "0");
  const day = String(dateObj.getDate()).padStart(2, "0");
  const hours = String(dateObj.getHours()).padStart(2, "0");
  const minutes = String(dateObj.getMinutes()).padStart(2, "0");
  const seconds = String(dateObj.getSeconds()).padStart(2, "0");
  
  return `${year}${month}${day}_${hours}${minutes}${seconds}`;
};

/**
 * 월별 데이터 그룹화
 * @param data 그룹화할 데이터 배열
 * @param dateField 날짜 필드명
 * @returns 월별로 그룹화된 데이터
 */
export const groupByMonth = <T extends Record<string, any>>(
  data: T[],
  dateField: keyof T
): Record<string, T[]> => {
  return data.reduce((groups, item) => {
    const date = new Date(item[dateField]);
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}`;
    
    if (!groups[monthKey]) {
      groups[monthKey] = [];
    }
    groups[monthKey].push(item);
    
    return groups;
  }, {} as Record<string, T[]>);
};

/**
 * 주별 데이터 그룹화
 * @param data 그룹화할 데이터 배열
 * @param dateField 날짜 필드명
 * @returns 주별로 그룹화된 데이터
 */
export const groupByWeek = <T extends Record<string, any>>(
  data: T[],
  dateField: keyof T
): Record<string, T[]> => {
  return data.reduce((groups, item) => {
    const date = new Date(item[dateField]);
    const startOfWeek = new Date(date);
    startOfWeek.setDate(date.getDate() - date.getDay());
    const weekKey = startOfWeek.toISOString().split("T")[0];
    
    if (!groups[weekKey]) {
      groups[weekKey] = [];
    }
    groups[weekKey].push(item);
    
    return groups;
  }, {} as Record<string, T[]>);
};
