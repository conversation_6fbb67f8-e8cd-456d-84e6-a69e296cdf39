"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { useFarmMembersStore } from "@/store/use-farm-members-store";
import { useFarms } from "@/hooks/use-farms";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { UserPlus, Shield, Home } from "lucide-react";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import Link from "next/link";
import { RoleBadge, UserRole } from "@/components/role-badge";
import {
  QuickActionButtons,
  BulkActionButtons,
} from "@/components/quick-action-buttons";
import { supabase } from "@/lib/supabase";

export default function FarmMembersPage() {
  const params = useParams();
  const farmId = params.farmId as string;
  const { farms } = useFarms();
  const {
    members,
    loading,
    fetchMembers,
    addMember,
    updateMemberRole,
    removeMember,
  } = useFarmMembersStore();
  const { toast } = useToast();

  const [dialogOpen, setDialogOpen] = useState(false);
  const [email, setEmail] = useState("");
  const [role, setRole] = useState<"manager" | "viewer">("manager");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [memberToDelete, setMemberToDelete] = useState<string | null>(null);
  const [selectedMembers, setSelectedMembers] = useState<string[]>([]);
  const [isAddingMember, setIsAddingMember] = useState(false);
  const [availableUsers, setAvailableUsers] = useState<any[]>([]);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [canManageMembers, setCanManageMembers] = useState(false);

  const farm = farms.find((f) => f.id === farmId);

  // 구성원 데이터 로드
  useEffect(() => {
    if (farmId) {
      fetchMembers(farmId);
    }
  }, [farmId, fetchMembers]);

  // 사용 가능한 사용자 목록 로드 (디버깅용)
  useEffect(() => {
    const fetchAvailableUsers = async () => {
      try {
        // 현재 사용자 정보 확인
        const {
          data: { user },
          error: userError,
        } = await supabase.auth.getUser();
        console.log("🔐 Current user:", user);
        console.log("🔐 User error:", userError);

        if (user) {
          setCurrentUser(user);

          // 현재 사용자가 이 농장의 소유자인지 확인
          const isOwner = farm?.owner_id === user.id;
          setCanManageMembers(isOwner);
          console.log(
            "🏢 Can manage members:",
            isOwner,
            "Farm owner:",
            farm?.owner_id,
            "User ID:",
            user.id
          );
        }

        // 현재 세션 확인
        const {
          data: { session },
          error: sessionError,
        } = await supabase.auth.getSession();
        console.log("🔐 Current session:", session);
        console.log("🔐 Session error:", sessionError);

        const { data: users, error } = await supabase
          .from("profiles")
          .select("id, email, name")
          .limit(10);

        if (error) throw error;
        setAvailableUsers(users || []);
        console.log("👥 Available users:", users);
      } catch (error) {
        console.error("❌ Error fetching users:", error);
      }
    };

    fetchAvailableUsers();
  }, []);

  const handleAddMember = async () => {
    // 입력값 검증
    if (!email.trim()) {
      toast({
        title: "입력 오류",
        description: "이메일을 입력해주세요.",
        variant: "destructive",
      });
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      toast({
        title: "입력 오류",
        description: "올바른 이메일 형식을 입력해주세요.",
        variant: "destructive",
      });
      return;
    }

    setIsAddingMember(true);
    try {
      await addMember(farmId, email.trim(), role);
      toast({
        title: "구성원 추가 완료",
        description: `${email}이 ${
          role === "manager" ? "관리자" : "조회자"
        }로 추가되었습니다.`,
      });
      setDialogOpen(false);
      setEmail("");
      setRole("manager");
    } catch (error: any) {
      console.error("Add member error:", error);

      let errorMessage = "구성원 추가에 실패했습니다.";

      if (error.message) {
        errorMessage = error.message;
      } else if (error.code) {
        switch (error.code) {
          case "PGRST116":
            errorMessage = "해당 이메일로 가입된 사용자를 찾을 수 없습니다.";
            break;
          case "23505":
            errorMessage = "이미 이 농장의 구성원입니다.";
            break;
          default:
            errorMessage = `오류 코드: ${error.code}`;
        }
      }

      toast({
        title: "구성원 추가 실패",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsAddingMember(false);
    }
  };

  const handleUpdateRole = async (
    memberId: string,
    newRole: "manager" | "viewer"
  ) => {
    try {
      await updateMemberRole(memberId, newRole);
      toast({
        title: "권한 변경 완료",
        description: `구성원이 ${
          newRole === "manager" ? "관리자" : "조회자"
        }로 변경되었습니다.`,
      });
    } catch (error: any) {
      console.error("Update role error:", error);
      toast({
        title: "권한 변경 실패",
        description: error.message || "권한 변경에 실패했습니다.",
        variant: "destructive",
      });
    }
  };

  const handleDelete = (memberId: string) => {
    setMemberToDelete(memberId);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!memberToDelete) return;

    try {
      const memberName =
        members.find((m) => m.id === memberToDelete)?.representative_name ||
        "구성원";
      await removeMember(memberToDelete);
      toast({
        title: "구성원 제거 완료",
        description: `${memberName}이 농장에서 제거되었습니다.`,
      });
      setDeleteDialogOpen(false);
      setMemberToDelete(null);
    } catch (error: any) {
      console.error("Remove member error:", error);
      toast({
        title: "구성원 제거 실패",
        description: error.message || "구성원 제거에 실패했습니다.",
        variant: "destructive",
      });
    }
  };

  // 빠른 액션 핸들러들
  const handleQuickPromote = async (memberId: string, newRole: UserRole) => {
    try {
      await updateMemberRole(memberId, newRole as "manager" | "viewer");
      toast({
        title: "권한 변경 완료",
        description: `구성원이 ${
          newRole === "manager" ? "관리자" : "조회자"
        }로 변경되었습니다.`,
      });
    } catch (error) {
      toast({
        title: "권한 변경 실패",
        description: "권한 변경에 실패했습니다.",
        variant: "destructive",
      });
    }
  };

  const handleQuickDemote = async (memberId: string, newRole: UserRole) => {
    try {
      await updateMemberRole(memberId, newRole as "manager" | "viewer");
      toast({
        title: "권한 변경 완료",
        description: `구성원이 ${
          newRole === "manager" ? "관리자" : "조회자"
        }로 변경되었습니다.`,
      });
    } catch (error) {
      toast({
        title: "권한 변경 실패",
        description: "권한 변경에 실패했습니다.",
        variant: "destructive",
      });
    }
  };

  const handleQuickDelete = (memberId: string) => {
    setMemberToDelete(memberId);
    setDeleteDialogOpen(true);
  };

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      {/* 브레드크럼 네비게이션 */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/admin/dashboard" className="flex items-center gap-1">
                <Home className="h-4 w-4" />
                대시보드
              </Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/admin/farms">농장 관리</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{farm?.farm_name} 구성원</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight mb-2">
            {farm?.farm_name} 구성원 관리
          </h2>
          <p className="text-muted-foreground">
            농장 구성원을 관리하고 권한을 설정하세요
          </p>
        </div>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button disabled={!canManageMembers}>
              <UserPlus className="mr-2 h-4 w-4" />
              구성원 추가
              {!canManageMembers && " (권한 없음)"}
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>새 구성원 추가</DialogTitle>
              <DialogDescription>
                추가할 구성원의 이메일과 권한을 입력하세요
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label>이메일</Label>
                <Input
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isAddingMember}
                />
                <div className="text-xs text-muted-foreground space-y-1">
                  <p>💡 가입된 사용자의 이메일만 추가할 수 있습니다.</p>
                  <p>
                    🏢 다른 농장의 소유자도 이 농장의 구성원으로 추가할 수
                    있습니다.
                  </p>
                </div>
                {availableUsers.length > 0 && (
                  <details className="text-xs">
                    <summary className="cursor-pointer text-blue-600 hover:text-blue-800">
                      사용 가능한 사용자 목록 보기 ({availableUsers.length}명)
                    </summary>
                    <div className="mt-2 p-2 bg-gray-50 rounded border max-h-32 overflow-y-auto">
                      {availableUsers.map((user) => {
                        const currentMember = members.find(
                          (m) => m.email === user.email
                        );
                        const isCurrentMember = !!currentMember;

                        return (
                          <div
                            key={user.id}
                            className={`flex justify-between items-center py-1 px-1 rounded ${
                              isCurrentMember
                                ? "bg-yellow-100 text-yellow-800"
                                : "cursor-pointer hover:bg-gray-100"
                            }`}
                            onClick={() =>
                              !isCurrentMember && setEmail(user.email)
                            }
                          >
                            <span>{user.email}</span>
                            <span className="text-gray-500">
                              ({user.name})
                              {isCurrentMember && (
                                <span className="ml-1">
                                  ✓ 이미{" "}
                                  {currentMember.role === "owner"
                                    ? "소유자"
                                    : currentMember.role === "manager"
                                    ? "관리자"
                                    : "조회자"}
                                </span>
                              )}
                            </span>
                          </div>
                        );
                      })}
                    </div>
                  </details>
                )}
              </div>
              <div className="space-y-2">
                <Label>권한</Label>
                <Select
                  value={role}
                  onValueChange={(value: any) => setRole(value)}
                  disabled={isAddingMember}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="manager">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                        관리자
                      </div>
                    </SelectItem>
                    <SelectItem value="viewer">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                        조회자
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setDialogOpen(false)}
                disabled={isAddingMember}
              >
                취소
              </Button>
              <Button onClick={handleAddMember} disabled={isAddingMember}>
                {isAddingMember ? "추가 중..." : "추가"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* 구성원이 없을 때 안내 메시지 */}
      {members.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <Shield className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p>아직 구성원이 없습니다</p>
          <p className="text-sm">
            구성원 추가 버튼을 클릭해서 새로운 구성원을 추가하세요
          </p>
        </div>
      )}

      <div className="grid gap-4">
        {members.map((member) => (
          <Card
            key={member.id}
            className={
              member.role === "owner" ? "border-purple-200 bg-purple-50/30" : ""
            }
          >
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <CardTitle
                      className={
                        member.role === "owner" ? "text-purple-900" : ""
                      }
                    >
                      {member.representative_name}
                      {member.role === "owner" && " (농장 소유자)"}
                    </CardTitle>
                    <RoleBadge role={member.role as UserRole} size="sm" />
                  </div>
                  <CardDescription>{member.email}</CardDescription>
                </div>

                {/* 액션 버튼들 */}
                <div className="flex items-center gap-2">
                  {/* 빠른 액션 버튼 */}
                  <QuickActionButtons
                    memberRole={member.role as UserRole}
                    memberId={member.id}
                    memberName={member.representative_name}
                    onPromote={handleQuickPromote}
                    onDemote={handleQuickDemote}
                    onDelete={handleQuickDelete}
                    canManageMembers={canManageMembers}
                  />
                </div>
              </div>

              {/* 권한 변경 드롭다운 (소유자가 아니고 관리 권한이 있는 경우만) */}
              {member.role !== "owner" && canManageMembers && (
                <div className="flex items-center gap-2 pt-3 border-t">
                  <span className="text-sm text-muted-foreground">권한:</span>
                  <Select
                    value={member.role}
                    onValueChange={(value: any) =>
                      handleUpdateRole(member.id, value)
                    }
                  >
                    <SelectTrigger className="w-[130px] h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="manager">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                          관리자
                        </div>
                      </SelectItem>
                      <SelectItem value="viewer">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-green-500"></div>
                          조회자
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* 권한 없음 안내 (소유자가 아니지만 관리 권한이 없는 경우) */}
              {member.role !== "owner" && !canManageMembers && (
                <div className="pt-3 border-t">
                  <span className="text-xs text-muted-foreground">
                    💡 농장 소유자만 구성원 권한을 변경할 수 있습니다.
                  </span>
                </div>
              )}
            </CardHeader>
          </Card>
        ))}
      </div>

      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>구성원 제거</DialogTitle>
            <DialogDescription>
              정말로 이 구성원을 제거하시겠습니까?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
            >
              취소
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              제거
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
