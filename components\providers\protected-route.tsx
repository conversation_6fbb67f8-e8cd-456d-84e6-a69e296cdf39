"use client";

import { useAuth } from "@/components/providers/auth-provider";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Loader2 } from "lucide-react";
import { AccessDenied } from "@/components/error/access-denied";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAdmin?: boolean;
  redirectTo?: string;
}

export function ProtectedRoute({
  children,
  requireAdmin = false,
  redirectTo = "/login",
}: ProtectedRouteProps) {
  const { profile, loading } = useAuth();
  const router = useRouter();
  const [forceLoaded, setForceLoaded] = useState(false);
  const [countdown, setCountdown] = useState(20);

  useEffect(() => {
    // 로딩 중이면 대기
    if (loading && !forceLoaded) return;

    // 로그인하지 않은 경우
    if (!profile) {
      console.log("🔒 No profile found, redirecting to:", redirectTo);
      // 즉시 강제 리다이렉트
      window.location.href = redirectTo;
      return;
    }

    // 관리자 권한이 필요한데 관리자가 아닌 경우
    if (requireAdmin && profile.account_type !== "admin") {
      console.log(
        "🔒 Admin required but user is not admin, showing access denied"
      );
      // 리다이렉트 대신 AccessDenied 컴포넌트 표시
      return;
    }

    console.log("✅ Access granted for user:", profile.email);
  }, [profile, loading, forceLoaded, requireAdmin, router, redirectTo]);

  // 카운트다운 타이머 (로딩 중일 때만 실행)
  useEffect(() => {
    if (!loading || forceLoaded) {
      setCountdown(20); // 카운트다운 리셋
      return;
    }

    const intervalId = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(intervalId);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(intervalId);
  }, [loading, forceLoaded]);

  // 안전장치: 20초 후에도 로딩 중이면 강제로 로딩 해제
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (loading && !forceLoaded) {
        console.warn(
          "⚠️ ProtectedRoute loading timeout after 20 seconds, forcing loaded state"
        );
        setForceLoaded(true);
      }
    }, 20000);

    return () => clearTimeout(timeoutId);
  }, [loading, forceLoaded]);

  // 로딩 중일 때 로딩 스피너 표시 (강제 로딩 해제되지 않은 경우에만)
  if (loading && !forceLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">인증 확인 중...</p>
          <p className="text-xs text-muted-foreground">
            {countdown > 0
              ? `${countdown}초 후 자동으로 진행됩니다...`
              : "곧 진행됩니다..."}
          </p>
          {countdown <= 5 && countdown > 0 && (
            <p className="text-xs text-orange-600 font-medium">
              잠시만 기다려주세요...
            </p>
          )}
        </div>
      </div>
    );
  }

  // 로그인하지 않은 경우 빈 화면 (리다이렉트 진행 중)
  if (!profile) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">
            로그인 페이지로 이동 중...
          </p>
        </div>
      </div>
    );
  }

  // 관리자 권한이 필요한데 권한이 없는 경우
  if (requireAdmin && profile.account_type !== "admin") {
    return (
      <AccessDenied
        title="관리자 권한 필요"
        description="이 페이지는 관리자만 접근할 수 있습니다."
        requiredRole="관리자"
        currentRole={
          profile?.account_type === "admin" ? "관리자" : "일반 사용자"
        }
        showNavigation={true}
      />
    );
  }

  // 모든 조건을 만족하면 자식 컴포넌트 렌더링
  return <>{children}</>;
}
