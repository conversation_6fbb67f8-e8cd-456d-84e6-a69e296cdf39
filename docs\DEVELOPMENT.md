# 개발 환경 설정 가이드

## 필수 요구사항

### 시스템 요구사항

- Node.js 18.0.0 이상
- npm 9.0.0 이상 또는 yarn 1.22.0 이상
- Git 2.0.0 이상
- PostgreSQL 14.0 이상

### 권장 개발 도구

- VS Code
- DBeaver (데이터베이스 관리)
- Postman (API 테스트)

## 초기 설정

### 1. 저장소 클론

```bash
git clone [repository-url]
cd farm-dev
```

### 2. 의존성 설치

```bash
# npm 사용 시
npm install

# yarn 사용 시
yarn install
```

### 3. 환경 변수 설정

`.env.local` 파일을 생성하고 다음 환경 변수를 설정합니다:

```env
# 데이터베이스
DATABASE_URL=postgresql://username:password@localhost:5432/farm_dev

# 인증
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d

# API
NEXT_PUBLIC_API_URL=http://localhost:3000/api
```

### 4. 데이터베이스 설정

```bash
# PostgreSQL 데이터베이스 생성
createdb farm_dev

# 마이그레이션 실행
npm run migrate
# 또는
yarn migrate
```

### 5. 개발 서버 실행

```bash
npm run dev
# 또는
yarn dev
```

## 개발 워크플로우

### 1. 브랜치 전략

- `main`: 프로덕션 브랜치
- `develop`: 개발 브랜치
- `feature/*`: 기능 개발 브랜치
- `bugfix/*`: 버그 수정 브랜치
- `release/*`: 릴리스 브랜치

### 2. 커밋 메시지 규칙

```
<type>(<scope>): <subject>

<body>

<footer>
```

타입:

- feat: 새로운 기능
- fix: 버그 수정
- docs: 문서 수정
- style: 코드 포맷팅
- refactor: 코드 리팩토링
- test: 테스트 코드
- chore: 빌드 업무 수정

### 3. 코드 리뷰 프로세스

1. 기능 개발 완료
2. PR 생성
3. 코드 리뷰 요청
4. 리뷰어의 승인
5. develop 브랜치로 머지

## 테스트

### 단위 테스트

```bash
npm run test
# 또는
yarn test
```

### E2E 테스트

```bash
npm run test:e2e
# 또는
yarn test:e2e
```

## 디버깅

### VS Code 디버깅 설정

`.vscode/launch.json`:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Next.js: debug server-side",
      "type": "node-terminal",
      "request": "launch",
      "command": "npm run dev"
    },
    {
      "name": "Next.js: debug client-side",
      "type": "chrome",
      "request": "launch",
      "url": "http://localhost:3000"
    }
  ]
}
```

## 성능 최적화

### 번들 분석

```bash
npm run analyze
# 또는
yarn analyze
```

### 성능 모니터링

- Chrome DevTools Performance 탭
- Lighthouse
- Next.js Analytics

## 배포

### 스테이징 배포

```bash
npm run deploy:staging
# 또는
yarn deploy:staging
```

### 프로덕션 배포

```bash
npm run deploy:prod
# 또는
yarn deploy:prod
```

## 문제 해결

### 일반적인 문제

1. 의존성 문제

   ```bash
   rm -rf node_modules
   npm install
   ```

2. 캐시 문제

   ```bash
   npm run clean
   # 또는
   yarn clean
   ```

3. 데이터베이스 연결 문제
   - PostgreSQL 서비스 실행 확인
   - 환경 변수 확인
   - 방화벽 설정 확인

### 지원

문제가 지속되면 다음 채널로 문의:

- GitHub Issues
- 팀 채팅
- 이메일
