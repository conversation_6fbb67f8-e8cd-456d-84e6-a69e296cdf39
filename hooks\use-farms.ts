"use client";

import { useAuth } from "@/components/providers/auth-provider";
import { useToast } from "@/hooks/use-toast";
import { useFarmsStore, Farm } from "@/store/use-farms-store";
import { useFarmsContext } from "@/components/providers/farms-provider";
import { useState, useCallback } from "react";
import { toast } from "sonner";
import type { FarmFormValues } from "@/lib/validations/farm";

export type { Farm };

export function useFarms() {
  const { profile } = useAuth();
  const { toast } = useToast();
  const {
    farms,
    loading,
    addFarm: storeFarmAdd,
    updateFarm: storeFarmUpdate,
    deleteFarm: storeFarmDelete,
  } = useFarmsStore();
  const { initialized } = useFarmsContext();

  const addFarm = useCallback(
    async (farmData: FarmFormValues) => {
      if (!profile?.id) return null;

      try {
        const newFarm = {
          ...farmData,
        };
        await storeFarmAdd(profile.id, newFarm);
        toast({
          title: "농장 등록 완료",
          description: "농장이 성공적으로 등록되었습니다.",
        });
      } catch (error) {
        console.error("Failed to add farm:", error);
        toast({
          title: "농장 등록 실패",
          description: "농장 등록에 실패했습니다. 잠시 후 다시 시도해주세요.",
          variant: "destructive",
        });
        return null;
      }
    },
    [profile, storeFarmAdd]
  );

  const updateFarm = useCallback(
    async (farmId: string, farmData: FarmFormValues) => {
      try {
        const updatedFarm = {
          ...farmData,
        };
        await storeFarmUpdate(farmId, updatedFarm);
        toast({
          title: "농장 정보 수정 완료",
          description: "농장 정보가 성공적으로 수정되었습니다.",
        });
      } catch (error) {
        console.error("Failed to update farm:", error);
        toast({
          title: "농장 정보 수정 실패",
          description:
            "농장 정보 수정에 실패했습니다. 잠시 후 다시 시도해주세요.",
          variant: "destructive",
        });
      }
    },
    [storeFarmUpdate]
  );

  const deleteFarm = useCallback(
    async (farmId: string) => {
      try {
        await storeFarmDelete(farmId);
        toast({
          title: "농장 삭제 완료",
          description: "농장이 성공적으로 삭제되었습니다.",
        });
      } catch (error) {
        console.error("Failed to delete farm:", error);
        toast({
          title: "농장 삭제 실패",
          description: "농장 삭제에 실패했습니다. 잠시 후 다시 시도해주세요.",
          variant: "destructive",
        });
      }
    },
    [storeFarmDelete]
  );

  const generateQRCodeUrl = useCallback((farmId: string) => {
    return `${window.location.origin}/visit/${farmId}`;
  }, []);

  return {
    farms,
    loading: loading || !initialized,
    addFarm,
    updateFarm,
    deleteFarm,
    generateQRCodeUrl,
  };
}
