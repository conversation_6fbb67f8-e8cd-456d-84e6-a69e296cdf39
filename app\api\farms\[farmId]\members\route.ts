import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

// 구성원 추가 후 사용자 role 업데이트 함수
async function updateUserRoleAfterMemberAdd(
  supabase: any,
  userId: string,
  newMemberRole: string
) {
  try {
    console.log(
      `👤 구성원 추가 후 사용자 role 업데이트: ${userId}, 새 역할: ${newMemberRole}`
    );

    // 1. 현재 사용자의 role 조회
    const { data: currentUser, error: userError } = await supabase
      .from("profiles")
      .select("role")
      .eq("id", userId)
      .single();

    if (userError) {
      console.error("사용자 정보 조회 오류:", userError);
      return;
    }

    // 2. 사용자가 소유한 농장 확인
    const { data: ownedFarms, error: ownedError } = await supabase
      .from("farms")
      .select("id")
      .eq("owner_id", userId);

    if (ownedError) {
      console.error("소유 농장 조회 오류:", ownedError);
      return;
    }

    // 3. 농장 구성원 추가 시 profiles.role 업데이트는 더 이상 필요하지 않음
    // 새로운 권한 시스템에서는 profiles.account_type은 시스템 레벨 권한만 관리
    // 농장별 권한은 farm_members 테이블에서 관리됨
    console.log(
      `ℹ️ 농장 구성원 추가 완료. 사용자 ${userId}의 농장별 권한은 farm_members에서 관리됩니다.`
    );
  } catch (error) {
    console.error("구성원 추가 후 role 업데이트 오류:", error);
  }
}

export async function POST(
  request: Request,
  { params }: { params: { farmId: string } }
) {
  try {
    const { userId, role } = await request.json();
    const farmId = params.farmId;

    if (!userId || !role || !farmId) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    const supabase = createRouteHandlerClient({ cookies });

    // 현재 사용자가 농장 소유자인지 확인
    const { data: farm, error: farmError } = await supabase
      .from("farms")
      .select("owner_id")
      .eq("id", farmId)
      .single();

    if (farmError || !farm) {
      return NextResponse.json({ error: "Farm not found" }, { status: 404 });
    }

    // 현재 인증된 사용자 확인
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user || farm.owner_id !== user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    // 구성원 추가
    const { error: insertError } = await supabase.from("farm_members").insert({
      farm_id: farmId,
      user_id: userId,
      role: role,
    });

    if (insertError) {
      return NextResponse.json({ error: insertError.message }, { status: 500 });
    }

    // 사용자의 현재 role 확인 및 업데이트
    await updateUserRoleAfterMemberAdd(supabase, userId, role);

    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

// 구성원 목록 조회
export async function GET(
  request: Request,
  { params }: { params: { farmId: string } }
) {
  try {
    const farmId = params.farmId;
    const supabase = createRouteHandlerClient({ cookies });

    const { data: members, error } = await supabase
      .from("farm_members")
      .select(
        `
        *,
        users:user_id (
          id,
          email,
          name,
          avatar_url
        )
      `
      )
      .eq("farm_id", farmId);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(members);
  } catch (error) {
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
