import { useCallback, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { useSystemLog } from "../hooks/useSystemLog";
import { useDataManagement } from "../hooks/useDataManagement";
import { DataTable } from "./DataTable";
import {
  formatDateTime,
  formatPhoneNumber,
  getRoleColor,
} from "../utils/formatters";
import { PaginationParams } from "../utils/query-utils";

interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  account_type: string;
  status: string;
  is_active?: boolean;
  last_login_at: string;
  created_at: string;
}

const FILTER_OPTIONS = [
  { value: "all", label: "전체" },
  { value: "admin", label: "관리자" },
  { value: "owner", label: "농장주" },
  { value: "manager", label: "매니저" },
  { value: "viewer", label: "일반" },
];

export function UserManagement() {
  const { toast } = useToast();
  const { logUserAction, logError } = useSystemLog();
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isDetailOpen, setIsDetailOpen] = useState(false);

  const fetchUsers = useCallback(
    async (params: PaginationParams) => {
      try {
        const response = await fetch(
          `/api/admin/users?page=${params.page}&limit=${params.limit}`
        );
        if (!response.ok) throw new Error("Failed to fetch users");
        return await response.json();
      } catch (error) {
        logError("FETCH_USERS_ERROR", error);
        toast({
          title: "사용자 목록 로딩 실패",
          description: "사용자 목록을 불러오는 중 오류가 발생했습니다.",
          variant: "destructive",
        });
        return {
          data: [],
          total: 0,
          currentPage: 1,
          totalPages: 1,
        };
      }
    },
    [logError, toast]
  );

  const {
    data: users,
    isLoading,
    searchTerm,
    currentFilter,
    handleSearch,
    handleFilter,
    currentPage,
    totalPages,
    handlePageChange,
    refetch,
  } = useDataManagement<User>({
    fetchData: fetchUsers,
    searchFields: ["name", "email", "phone"],
    resourceName: "사용자",
    defaultLimit: 20,
  });

  const handleUserDetail = async (user: User) => {
    setSelectedUser(user);
    setIsDetailOpen(true);
    await logUserAction("VIEW_USER_DETAIL", user.name, user.id);
  };

  const handleStatusChange = async (userId: string, newStatus: boolean) => {
    try {
      const response = await fetch(`/api/admin/users/${userId}/status`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ is_active: newStatus }),
      });

      if (!response.ok) throw new Error("Failed to update user status");

      refetch();
      toast({
        title: "상태 변경 완료",
        description: `사용자 상태가 ${
          newStatus ? "활성" : "비활성"
        }으로 변경되었습니다.`,
      });
    } catch (error) {
      logError("UPDATE_USER_STATUS_ERROR", error);
      toast({
        title: "상태 변경 실패",
        description: "사용자 상태를 변경하는 중 오류가 발생했습니다.",
        variant: "destructive",
      });
    }
  };

  const handleExport = async () => {
    try {
      const response = await fetch("/api/admin/users/export");
      if (!response.ok) throw new Error("Failed to export users");

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `users-${formatDateTime(new Date())}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "내보내기 완료",
        description: "사용자 목록이 CSV 파일로 저장되었습니다.",
      });
    } catch (error) {
      logError("EXPORT_USERS_ERROR", error);
      toast({
        title: "내보내기 실패",
        description: "사용자 목록을 내보내는 중 오류가 발생했습니다.",
        variant: "destructive",
      });
    }
  };

  const columns = [
    {
      header: "이름",
      accessor: (user: User) => (
        <Button
          variant="link"
          className="p-0 h-auto font-normal"
          onClick={() => handleUserDetail(user)}
        >
          {user.name}
        </Button>
      ),
    },
    { header: "이메일", accessor: "email" as keyof User },
    {
      header: "연락처",
      accessor: (user: User) => formatPhoneNumber(user.phone || ""),
    },
    {
      header: "권한",
      accessor: (user: User) => (
        <Badge className={getRoleColor(user.account_type)}>
          {user.account_type}
        </Badge>
      ),
    },
    {
      header: "상태",
      accessor: (user: User) => (
        <Badge
          variant={user.is_active ? "default" : "secondary"}
          className="cursor-pointer"
          onClick={() => handleStatusChange(user.id, !user.is_active)}
        >
          {user.is_active ? "활성" : "비활성"}
        </Badge>
      ),
    },
    {
      header: "마지막 로그인",
      accessor: (user: User) =>
        user.last_login_at ? formatDateTime(user.last_login_at) : "-",
    },
    {
      header: "가입일",
      accessor: (user: User) => formatDateTime(user.created_at),
    },
  ];

  return (
    <>
      <DataTable
        data={users}
        columns={columns}
        searchTerm={searchTerm}
        onSearchChange={handleSearch}
        filterOptions={FILTER_OPTIONS}
        currentFilter={currentFilter}
        onFilterChange={handleFilter}
        onRefresh={refetch}
        onExport={handleExport}
        isLoading={isLoading}
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
      />

      <Dialog open={isDetailOpen} onOpenChange={setIsDetailOpen}>
        {selectedUser && (
          <DialogContent>
            <DialogHeader>
              <DialogTitle>사용자 상세 정보</DialogTitle>
              <DialogDescription>
                {selectedUser.name}님의 상세 정보입니다.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium">기본 정보</h4>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  <div>
                    <p className="text-sm text-muted-foreground">이메일</p>
                    <p className="text-sm">{selectedUser.email}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">연락처</p>
                    <p className="text-sm">
                      {formatPhoneNumber(selectedUser.phone || "")}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">권한</p>
                    <Badge className={getRoleColor(selectedUser.account_type)}>
                      {selectedUser.account_type}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">상태</p>
                    <Badge
                      variant={selectedUser.is_active ? "default" : "secondary"}
                    >
                      {selectedUser.is_active ? "활성" : "비활성"}
                    </Badge>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium">시스템 정보</h4>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  <div>
                    <p className="text-sm text-muted-foreground">
                      마지막 로그인
                    </p>
                    <p className="text-sm">
                      {selectedUser.last_login_at
                        ? formatDateTime(selectedUser.last_login_at)
                        : "-"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">가입일</p>
                    <p className="text-sm">
                      {formatDateTime(selectedUser.created_at)}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDetailOpen(false)}>
                닫기
              </Button>
            </DialogFooter>
          </DialogContent>
        )}
      </Dialog>
    </>
  );
}
