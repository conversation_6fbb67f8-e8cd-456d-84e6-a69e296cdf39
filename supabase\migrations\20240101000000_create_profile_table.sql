-- 농장 관리 시스템 완전 초기화 및 테이블 생성
-- 2024-12-01: 강제 테이블 생성

-- 기존 테이블 완전 삭제
DROP TABLE IF EXISTS public.visitor_entries CASCADE;
DROP TABLE IF EXISTS public.farm_members CASCADE;
DROP TABLE IF EXISTS public.farms CASCADE;
DROP TABLE IF EXISTS public.profiles CASCADE;

-- 1. profiles 테이블 생성
CREATE TABLE public.profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    phone_number TEXT,
    role TEXT DEFAULT 'owner',
    company_name TEXT,
    representative_name TEXT,
    company_address TEXT,
    manager_name TEXT,
    manager_phone TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. farms 테이블 생성
CREATE TABLE public.farms (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    owner_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    farm_name TEXT NOT NULL,
    farm_address TEXT NOT NULL,
    farm_detailed_address TEXT,
    farm_phone_number TEXT NOT NULL,
    farm_type TEXT,
    biosecurity_officer_contact TEXT,
    notes TEXT,
    notifications_enabled BOOLEAN DEFAULT true,
    manager_name TEXT,
    manager_phone TEXT,
    qr_code_token TEXT UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. farm_members 테이블 생성 (농장 구성원 관리)
CREATE TABLE IF NOT EXISTS public.farm_members (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    farm_id UUID REFERENCES public.farms(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    role TEXT CHECK (role IN ('owner', 'manager', 'viewer')) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(farm_id, user_id)
);

-- 4. visitor_entries 테이블 생성 (방문자 기록)
CREATE TABLE IF NOT EXISTS public.visitor_entries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    farm_id UUID REFERENCES public.farms(id) ON DELETE CASCADE NOT NULL,
    entry_datetime TIMESTAMP WITH TIME ZONE NOT NULL,
    full_name TEXT NOT NULL,
    phone_number TEXT NOT NULL,
    address TEXT NOT NULL,
    car_plate_number TEXT,
    visit_purpose TEXT NOT NULL,
    disinfection_check BOOLEAN DEFAULT false,
    notes TEXT,
    consent_given BOOLEAN DEFAULT false,
    session_token TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. updated_at 자동 업데이트 함수 생성
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 6. updated_at 트리거 생성
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_farms_updated_at
    BEFORE UPDATE ON public.farms
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_farm_members_updated_at
    BEFORE UPDATE ON public.farm_members
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 7. RLS (Row Level Security) 활성화
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.farms ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.farm_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.visitor_entries ENABLE ROW LEVEL SECURITY;

-- 8. profiles 테이블 RLS 정책
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Admin can view all profiles
CREATE POLICY "Admin can view all profiles" ON public.profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- 10. farms 테이블 RLS 정책
CREATE POLICY "Users can view farms they own or are members of" ON public.farms
    FOR SELECT USING (
        owner_id = auth.uid()
        OR EXISTS (
            SELECT 1 FROM public.farm_members
            WHERE farm_id = farms.id AND user_id = auth.uid()
        )
        OR EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Farm owners can update their farms" ON public.farms
    FOR UPDATE USING (owner_id = auth.uid());

CREATE POLICY "Users can insert farms" ON public.farms
    FOR INSERT WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Farm owners can delete their farms" ON public.farms
    FOR DELETE USING (owner_id = auth.uid());

-- 11. farm_members 테이블 RLS 정책
CREATE POLICY "Users can view farm members of their farms" ON public.farm_members
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.farms
            WHERE id = farm_members.farm_id
            AND (owner_id = auth.uid() OR EXISTS (
                SELECT 1 FROM public.farm_members fm
                WHERE fm.farm_id = farms.id AND fm.user_id = auth.uid()
            ))
        )
        OR EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Farm owners can manage farm members" ON public.farm_members
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.farms
            WHERE id = farm_members.farm_id AND owner_id = auth.uid()
        )
    );

-- 12. visitor_entries 테이블 RLS 정책
CREATE POLICY "Users can view visitor entries of their farms" ON public.visitor_entries
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.farms
            WHERE id = visitor_entries.farm_id
            AND (owner_id = auth.uid() OR EXISTS (
                SELECT 1 FROM public.farm_members fm
                WHERE fm.farm_id = farms.id AND fm.user_id = auth.uid()
            ))
        )
        OR EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Anyone can insert visitor entries" ON public.visitor_entries
    FOR INSERT WITH CHECK (true);

-- 12. 새 사용자 자동 프로필 생성 함수 (실제 구조에 맞게)
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (
        id,
        email,
        company_name,
        representative_name,
        company_address,
        manager_name,
        manager_phone
    )
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'company_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'representative_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'company_address', ''),
        COALESCE(NEW.raw_user_meta_data->>'manager_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'manager_phone', '')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 13. 새 사용자 생성 트리거
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_user();

-- 14. 인덱스 생성 (성능 최적화)
CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles(role);
CREATE INDEX IF NOT EXISTS idx_farms_owner_id ON public.farms(owner_id);
CREATE INDEX IF NOT EXISTS idx_farms_qr_code_token ON public.farms(qr_code_token);
CREATE INDEX IF NOT EXISTS idx_farm_members_farm_id ON public.farm_members(farm_id);
CREATE INDEX IF NOT EXISTS idx_farm_members_user_id ON public.farm_members(user_id);
CREATE INDEX IF NOT EXISTS idx_visitor_entries_farm_id ON public.visitor_entries(farm_id);
CREATE INDEX IF NOT EXISTS idx_visitor_entries_entry_datetime ON public.visitor_entries(entry_datetime);
CREATE INDEX IF NOT EXISTS idx_visitor_entries_session_token ON public.visitor_entries(session_token);