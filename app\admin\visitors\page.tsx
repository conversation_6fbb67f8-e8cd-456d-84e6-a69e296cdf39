"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/components/providers/auth-provider";
import { useFarms } from "@/hooks/use-farms";
import { getFarmTypeLabel, getFarmTypeIcon } from "@/lib/constants/farm-types";
import { supabase } from "@/lib/supabase";
import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  Users,
  Search,
  Filter,
  Download,
  Calendar,
  Building2,
  Phone,
  MapPin,
  Car,
  FileText,
  CheckCircle,
  XCircle,
  Home,
  Shield,
  RefreshCw,
} from "lucide-react";
import Link from "next/link";

/**
 * 방문자 기록 인터페이스 (농장 정보 포함)
 */
interface VisitorEntryWithFarm {
  id: string;
  farm_id: string;
  visit_datetime: string;
  visitor_name: string;
  visitor_phone: string;
  visitor_address: string;
  vehicle_number: string | null;
  visitor_purpose: string;
  disinfection_check: boolean;
  notes: string | null;
  consent_given: boolean;
  session_token: string;
  registered_by?: string;
  created_at: string;
  farms: {
    farm_name: string;
    farm_type?: string;
  };
}

/**
 * 방문자 기록 조회 페이지
 *
 * 사용자가 접근 가능한 농장의 방문자 기록을 표시하고,
 * 농장별 필터링, 검색, 기간 선택 기능을 제공합니다.
 * - Admin: 모든 농장 접근 가능
 * - 일반 사용자: 소유/관리하는 농장만 접근 가능
 */
export default function VisitorsPage() {
  const { profile } = useAuth();
  const { farms } = useFarms();

  const [visitors, setVisitors] = useState<VisitorEntryWithFarm[]>([]);
  const [loading, setLoading] = useState(true);

  // 필터링 상태
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedFarm, setSelectedFarm] = useState<string>("all");
  const [dateFilter, setDateFilter] = useState("");
  const [startDateFilter, setStartDateFilter] = useState("");
  const [endDateFilter, setEndDateFilter] = useState("");

  // CSV 다운로드 옵션 상태
  const [includeAllFarms, setIncludeAllFarms] = useState(true);
  const [csvStartDate, setCsvStartDate] = useState("");
  const [csvEndDate, setCsvEndDate] = useState("");

  /**
   * 접근 가능한 방문자 기록 로드
   */
  useEffect(() => {
    const fetchVisitors = async () => {
      try {
        console.log("=== 방문자 기록 조회 시작 ===");

        const { data: visitorsData, error } = await supabase
          .from("visitor_entries")
          .select(
            `
            *,
            farms (
              farm_name,
              farm_type
            )
          `
          )
          .order("visit_datetime", { ascending: false });

        if (error) {
          console.error("방문자 기록 조회 오류:", error);
          throw error;
        }

        console.log("조회된 방문자 기록 수:", visitorsData?.length || 0);
        setVisitors(visitorsData || []);
      } catch (error) {
        console.error("데이터 조회 오류:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchVisitors();
  }, []);

  /**
   * 검색 및 필터링된 방문자 목록
   */
  const filteredVisitors = visitors.filter((visitor) => {
    // 검색어 필터
    const matchesSearch =
      visitor.visitor_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      visitor.visitor_phone.includes(searchTerm) ||
      visitor.visitor_purpose
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      visitor.farms.farm_name.toLowerCase().includes(searchTerm.toLowerCase());

    // 농장 필터
    const matchesFarm =
      selectedFarm === "all" || visitor.farm_id === selectedFarm;

    // 단일 날짜 필터
    const matchesDate =
      !dateFilter ||
      new Date(visitor.visit_datetime).toISOString().split("T")[0] ===
        dateFilter;

    // 기간 필터
    const visitDate = new Date(visitor.entry_datetime)
      .toISOString()
      .split("T")[0];
    const matchesStartDate = !startDateFilter || visitDate >= startDateFilter;
    const matchesEndDate = !endDateFilter || visitDate <= endDateFilter;

    return (
      matchesSearch &&
      matchesFarm &&
      matchesDate &&
      matchesStartDate &&
      matchesEndDate
    );
  });

  /**
   * 기간 유효성 검사 함수
   */
  const validateDateRange = () => {
    if (!csvStartDate || !csvEndDate) return true;

    const start = new Date(csvStartDate);
    const end = new Date(csvEndDate);
    const diffYears =
      (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 365);

    if (diffYears > 5) {
      alert("기간 선택은 최대 5년까지 가능합니다.");
      return false;
    }

    if (start > end) {
      alert("시작일이 종료일보다 늦을 수 없습니다.");
      return false;
    }

    return true;
  };

  /**
   * CSV 다운로드 함수
   */
  const downloadCSV = async () => {
    try {
      // 기간 유효성 검사
      if (!validateDateRange()) {
        return;
      }

      let dataToDownload = [];
      let fileName = "";

      if (includeAllFarms) {
        // 모든 농장 데이터 (현재 필터링된 결과 사용)
        dataToDownload = [...filteredVisitors];

        // CSV용 추가 기간 필터 적용
        if (csvStartDate || csvEndDate) {
          dataToDownload = dataToDownload.filter((visitor) => {
            const visitDate = new Date(visitor.entry_datetime)
              .toISOString()
              .split("T")[0];
            const afterStart = !csvStartDate || visitDate >= csvStartDate;
            const beforeEnd = !csvEndDate || visitDate <= csvEndDate;
            return afterStart && beforeEnd;
          });
        }

        fileName = `전체농장_방문자기록_${csvStartDate || "전체"}_${
          csvEndDate || "전체"
        }_${new Date().toISOString().split("T")[0]}.csv`;
      } else {
        // 선택된 농장만
        let farmFilteredData =
          selectedFarm === "all"
            ? [...filteredVisitors]
            : filteredVisitors.filter((v) => v.farm_id === selectedFarm);

        // CSV용 추가 기간 필터 적용
        if (csvStartDate || csvEndDate) {
          farmFilteredData = farmFilteredData.filter((visitor) => {
            const visitDate = new Date(visitor.entry_datetime)
              .toISOString()
              .split("T")[0];
            const afterStart = !csvStartDate || visitDate >= csvStartDate;
            const beforeEnd = !csvEndDate || visitDate <= csvEndDate;
            return afterStart && beforeEnd;
          });
        }

        dataToDownload = farmFilteredData;
        const farmName =
          selectedFarm === "all"
            ? "전체농장"
            : farms.find((f) => f.id === selectedFarm)?.farm_name || "선택농장";
        fileName = `방문자기록_${farmName}_${csvStartDate || "전체"}_${
          csvEndDate || "전체"
        }_${new Date().toISOString().split("T")[0]}.csv`;
      }

      // CSV 헤더 설정 (항상 농장 정보 포함)
      const headers = [
        "농장명",
        "동물종류",
        "방문일시",
        "성명",
        "연락처",
        "주소",
        "차량번호",
        "방문목적",
        "소독여부",
        "비고",
      ];

      // CSV 데이터 생성
      const csvData = dataToDownload.map((visitor: any) => [
        visitor.farms?.farm_name || "알 수 없음",
        visitor.farms?.farm_type || "알 수 없음",
        new Date(visitor.visit_datetime).toLocaleString("ko-KR"),
        visitor.visitor_name,
        visitor.visitor_phone,
        visitor.visitor_address,
        visitor.vehicle_number || "",
        visitor.visitor_purpose,
        visitor.disinfection_check ? "완료" : "미완료",
        visitor.notes || "",
      ]);

      // CSV 파일 생성 및 다운로드 (한글 깨짐 해결: UTF-8 BOM 추가)
      const csvContent = [headers, ...csvData]
        .map((row) => row.map((field) => `"${field}"`).join(","))
        .join("\n");

      // UTF-8 BOM 추가로 한글 깨짐 해결
      const BOM = "\uFEFF";
      const csvWithBOM = BOM + csvContent;

      const blob = new Blob([csvWithBOM], {
        type: "text/csv;charset=utf-8;",
      });

      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", fileName);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log(`CSV 다운로드 완료: ${dataToDownload.length}건의 기록`);
    } catch (error) {
      console.error("CSV 다운로드 오류:", error);
      alert("CSV 다운로드 중 오류가 발생했습니다.");
    }
  };

  /**
   * 필터 초기화 함수
   */
  const resetFilters = () => {
    setSearchTerm("");
    setSelectedFarm("all");
    setDateFilter("");
    setStartDateFilter("");
    setEndDateFilter("");
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>방문자 기록을 불러오는 중...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:p-6 pt-2 md:pt-4">
      {/* 브레드크럼 */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/admin/dashboard" className="flex items-center gap-1">
                <Home className="h-4 w-4" />
                대시보드
              </Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>방문자 기록</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* 헤더 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <SidebarTrigger />
          <div>
            <h2 className="text-3xl font-bold tracking-tight mb-4 flex items-center gap-2">
              <Users className="h-8 w-8 text-primary" />
              방문자 기록 관리
            </h2>
            <p className="text-muted-foreground mt-2">
              {profile?.account_type === "admin"
                ? "모든 농장의 방문자 출입 기록을 조회하고 관리하세요"
                : "농장의 방문자 출입 기록을 조회하고 관리하세요"}
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                CSV 다운로드
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>CSV 다운로드 옵션</DialogTitle>
                <DialogDescription>
                  다운로드할 데이터 범위와 기간을 선택하세요.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                {/* 농장 범위 선택 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">데이터 범위</label>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeAllFarms"
                      checked={includeAllFarms}
                      onCheckedChange={(checked) =>
                        setIncludeAllFarms(checked === true)
                      }
                    />
                    <label
                      htmlFor="includeAllFarms"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      현재 필터 결과 포함 (농장:{" "}
                      {selectedFarm === "all"
                        ? profile?.account_type === "admin"
                          ? "모든 농장"
                          : "전체 농장"
                        : farms.find((f) => f.id === selectedFarm)?.farm_name}
                      )
                    </label>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {includeAllFarms
                      ? "현재 화면에 표시된 필터링 결과를 다운로드합니다."
                      : "선택된 농장의 방문자 기록만 다운로드합니다."}
                  </p>
                </div>

                {/* 기간 선택 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    기간 선택 (선택사항)
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="text-xs text-muted-foreground">
                        시작일
                      </label>
                      <Input
                        type="date"
                        value={csvStartDate}
                        onChange={(e) => setCsvStartDate(e.target.value)}
                        className="w-full"
                      />
                    </div>
                    <div>
                      <label className="text-xs text-muted-foreground">
                        종료일
                      </label>
                      <Input
                        type="date"
                        value={csvEndDate}
                        onChange={(e) => setCsvEndDate(e.target.value)}
                        className="w-full"
                      />
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    기간을 지정하지 않으면 모든 기록을 다운로드합니다. (최대 5년
                    범위)
                  </p>
                </div>

                {/* 미리보기 정보 */}
                <div className="bg-muted/50 p-3 rounded-lg">
                  <h4 className="text-sm font-medium mb-2">
                    다운로드 미리보기
                  </h4>
                  <div className="text-xs space-y-1">
                    <div>
                      • 범위:{" "}
                      {includeAllFarms
                        ? "현재 필터 결과"
                        : selectedFarm === "all"
                        ? profile?.account_type === "admin"
                          ? "모든 농장"
                          : "전체 농장"
                        : farms.find((f) => f.id === selectedFarm)?.farm_name}
                    </div>
                    <div>
                      • 기간: {csvStartDate || "전체"} ~ {csvEndDate || "전체"}
                    </div>
                    <div>• 현재 표시된 기록: {filteredVisitors.length}건</div>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIncludeAllFarms(true);
                    setCsvStartDate("");
                    setCsvEndDate("");
                  }}
                >
                  초기화
                </Button>
                <Button onClick={downloadCSV}>
                  <Download className="h-4 w-4 mr-2" />
                  다운로드
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* 검색 및 필터 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            검색 및 필터
          </CardTitle>
          <CardDescription>
            {profile?.account_type === "admin"
              ? "모든 농장의 방문자 기록을 검색하고 필터링하세요"
              : "농장의 방문자 기록을 검색하고 필터링하세요"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* 검색어 (전체 너비) */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="이름, 전화번호, 방문목적, 농장명으로 검색..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* 농장 선택 + 초기화 버튼 */}
            <div className="flex gap-2">
              <div className="flex-1">
                <Select value={selectedFarm} onValueChange={setSelectedFarm}>
                  <SelectTrigger>
                    <SelectValue placeholder="농장 선택" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">
                      {profile?.account_type === "admin"
                        ? "모든 농장"
                        : "전체 농장"}
                    </SelectItem>
                    {farms.map((farm) => (
                      <SelectItem key={farm.id} value={farm.id}>
                        <div className="flex items-center gap-2">
                          {(() => {
                            const Icon = getFarmTypeIcon(farm.farm_type);
                            return <Icon className="h-4 w-4" />;
                          })()}
                          <div>
                            <div className="font-medium">{farm.farm_name}</div>
                            <div className="text-xs text-muted-foreground">
                              {getFarmTypeLabel(farm.farm_type)}
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {(searchTerm ||
                selectedFarm !== "all" ||
                dateFilter ||
                startDateFilter ||
                endDateFilter) && (
                <Button
                  variant="outline"
                  onClick={resetFilters}
                  className="flex-shrink-0"
                >
                  <RefreshCw className="h-4 w-4 sm:mr-2" />
                  <span className="hidden sm:inline">초기화</span>
                </Button>
              )}
            </div>

            {/* 날짜 필터들 - 모바일에서 세로 배치 */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <label className="text-xs text-muted-foreground block mb-1">
                  특정 날짜
                </label>
                <Input
                  type="date"
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  className="w-full"
                />
              </div>
              <div>
                <label className="text-xs text-muted-foreground block mb-1">
                  시작일
                </label>
                <Input
                  type="date"
                  value={startDateFilter}
                  onChange={(e) => setStartDateFilter(e.target.value)}
                  className="w-full"
                />
              </div>
              <div>
                <label className="text-xs text-muted-foreground block mb-1">
                  종료일
                </label>
                <Input
                  type="date"
                  value={endDateFilter}
                  onChange={(e) => setEndDateFilter(e.target.value)}
                  className="w-full"
                />
              </div>
            </div>

            {/* 필터 상태 표시 */}
            <div className="flex flex-wrap gap-2">
              {searchTerm && (
                <Badge variant="secondary">검색: {searchTerm}</Badge>
              )}
              {selectedFarm !== "all" && (
                <Badge variant="secondary">
                  선택: {farms.find((f) => f.id === selectedFarm)?.farm_name}
                </Badge>
              )}
              {dateFilter && (
                <Badge variant="secondary">날짜: {dateFilter}</Badge>
              )}
              {startDateFilter && (
                <Badge variant="secondary">시작: {startDateFilter}</Badge>
              )}
              {endDateFilter && (
                <Badge variant="secondary">종료: {endDateFilter}</Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 통계 카드 */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">총 방문자</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{visitors.length}</div>
            <p className="text-xs text-muted-foreground">전체 기록</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {profile?.account_type === "admin" ? "총 농장" : "내 농장"}
            </CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{farms.length}</div>
            <p className="text-xs text-muted-foreground">
              {profile?.account_type === "admin"
                ? "관리 중인 농장"
                : "소유/관리 농장"}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">소독 완료</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {visitors.filter((v) => v.disinfection_check).length}
            </div>
            <p className="text-xs text-muted-foreground">
              완료율:{" "}
              {visitors.length > 0
                ? Math.round(
                    (visitors.filter((v) => v.disinfection_check).length /
                      visitors.length) *
                      100
                  )
                : 0}
              %
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">필터 결과</CardTitle>
            <Search className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredVisitors.length}</div>
            <p className="text-xs text-muted-foreground">검색 결과</p>
          </CardContent>
        </Card>
      </div>

      {/* 방문자 목록 테이블 */}
      <Card>
        <CardHeader>
          <CardTitle>방문자 목록</CardTitle>
          <CardDescription>
            {profile?.account_type === "admin"
              ? `모든 농장의 방문자 기록입니다. 총 ${filteredVisitors.length}건의 기록이 있습니다.`
              : `농장의 방문자 기록입니다. 총 ${filteredVisitors.length}건의 기록이 있습니다.`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredVisitors.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {searchTerm ||
                selectedFarm !== "all" ||
                dateFilter ||
                startDateFilter ||
                endDateFilter
                  ? "검색 조건에 맞는 방문자가 없습니다."
                  : "아직 방문자가 없습니다."}
              </p>
            </div>
          ) : (
            <>
              {/* 데스크톱 테이블 뷰 */}
              <div className="hidden md:block rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>농장</TableHead>
                      <TableHead>방문일시</TableHead>
                      <TableHead>성명</TableHead>
                      <TableHead>연락처</TableHead>
                      <TableHead>방문목적</TableHead>
                      <TableHead>차량번호</TableHead>
                      <TableHead>소독여부</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredVisitors.map((visitor) => (
                      <TableRow key={visitor.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {(() => {
                              const Icon = getFarmTypeIcon(
                                visitor.farms.farm_type
                              );
                              return (
                                <Icon className="h-4 w-4 text-muted-foreground" />
                              );
                            })()}
                            <div>
                              <div className="font-medium">
                                {visitor.farms.farm_name}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {getFarmTypeLabel(visitor.farms.farm_type)}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <div className="font-medium">
                                {new Date(
                                  visitor.visit_datetime
                                ).toLocaleDateString("ko-KR")}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {new Date(
                                  visitor.visit_datetime
                                ).toLocaleTimeString("ko-KR")}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">
                              {visitor.visitor_name}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Phone className="h-4 w-4 text-muted-foreground" />
                            <span>{visitor.visitor_phone}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4 text-muted-foreground" />
                            <span>{visitor.visitor_purpose}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          {visitor.vehicle_number ? (
                            <div className="flex items-center gap-2">
                              <Car className="h-4 w-4 text-muted-foreground" />
                              <Badge variant="outline">
                                {visitor.vehicle_number}
                              </Badge>
                            </div>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {visitor.disinfection_check ? (
                            <Badge
                              variant="default"
                              className="bg-green-100 text-green-800"
                            >
                              <CheckCircle className="h-3 w-3 mr-1" />
                              완료
                            </Badge>
                          ) : (
                            <Badge
                              variant="secondary"
                              className="bg-red-100 text-red-800"
                            >
                              <XCircle className="h-3 w-3 mr-1" />
                              미완료
                            </Badge>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* 모바일 카드 뷰 */}
              <div className="md:hidden space-y-4">
                {filteredVisitors.map((visitor) => (
                  <Card key={visitor.id} className="p-4">
                    <div className="space-y-3">
                      {/* 헤더: 농장 정보 + 소독 상태 */}
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-2 flex-1 min-w-0">
                          {(() => {
                            const Icon = getFarmTypeIcon(
                              visitor.farms.farm_type
                            );
                            return (
                              <Icon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                            );
                          })()}
                          <div className="min-w-0">
                            <div className="font-medium truncate">
                              {visitor.farms.farm_name}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {getFarmTypeLabel(visitor.farms.farm_type)}
                            </div>
                          </div>
                        </div>
                        <div className="flex-shrink-0 ml-2">
                          {visitor.disinfection_check ? (
                            <Badge
                              variant="default"
                              className="bg-green-100 text-green-800"
                            >
                              <CheckCircle className="h-3 w-3 mr-1" />
                              완료
                            </Badge>
                          ) : (
                            <Badge
                              variant="secondary"
                              className="bg-red-100 text-red-800"
                            >
                              <XCircle className="h-3 w-3 mr-1" />
                              미완료
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* 방문자 정보 */}
                      <div className="grid grid-cols-1 gap-2 text-sm">
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                          <span className="font-medium">
                            {visitor.visitor_name}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                          <span>{visitor.visitor_phone}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                          <div>
                            <span className="font-medium">
                              {new Date(
                                visitor.visit_datetime
                              ).toLocaleDateString("ko-KR")}
                            </span>
                            <span className="text-muted-foreground ml-2">
                              {new Date(
                                visitor.visit_datetime
                              ).toLocaleTimeString("ko-KR")}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                          <span>{visitor.visitor_purpose}</span>
                        </div>
                        {visitor.vehicle_number && (
                          <div className="flex items-center gap-2">
                            <Car className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                            <Badge variant="outline">
                              {visitor.vehicle_number}
                            </Badge>
                          </div>
                        )}
                      </div>

                      {/* 주소 정보 (접을 수 있도록) */}
                      {visitor.visitor_address && (
                        <div className="pt-2 border-t">
                          <div className="flex items-start gap-2 text-sm">
                            <MapPin className="h-4 w-4 text-muted-foreground flex-shrink-0 mt-0.5" />
                            <span className="text-muted-foreground text-xs leading-relaxed">
                              {visitor.visitor_address}
                            </span>
                          </div>
                        </div>
                      )}

                      {/* 비고 */}
                      {visitor.notes && (
                        <div className="pt-2 border-t">
                          <div className="bg-muted/50 p-2 rounded text-xs">
                            <span className="font-medium">비고: </span>
                            {visitor.notes}
                          </div>
                        </div>
                      )}
                    </div>
                  </Card>
                ))}
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
