# 개발 히스토리 및 변경사항

## 📋 **개요**

**문서 목적**: 농장 방문자 관리 시스템의 개발 과정 및 주요 변경사항 기록  
**대상 독자**: 개발팀, 프로젝트 관리자, 유지보수 담당자  
**최종 업데이트**: 2024-12-14

## 🆕 **최근 업데이트 (2025-06-15)**

### **보안 및 사용자 인증 개선**

#### **통합 비밀번호 검증 시스템**

- `password-utils.ts`에 중앙 집중식 비밀번호 검증 로직 구현
- 비밀번호 강도 검사 (길이, 대문자, 소문자, 숫자, 특수문자 필수)
- 연속/반복 문자 검사 및 흔한 비밀번호 차단
- 모든 비밀번호 변경/재설정/등록 페이지에 일관된 검증 적용

#### **이메일 중복 확인 개선**

- `check_email_exists` PostgreSQL 함수 추가
- 대소문자 구분 없이 이메일 중복 확인
- `auth.users` 테이블과의 연동 최적화
- 사용자 친화적인 에러 메시지 제공

#### **시스템 로깅 강화**

- 비밀번호 변경/재설정 이벤트 로깅 추가
- 로그인 실패 추적 강화
- 민감한 정보 마스킹 처리

## 🏗️ **프로젝트 개발 단계**

### **Phase 1: 기초 설계 및 구조 설정**

**기간**: 2024-11-01 ~ 2024-11-15

#### **주요 작업**

```
✅ Next.js 14 + TypeScript 프로젝트 초기화
✅ Supabase 프로젝트 생성 및 연동
✅ 기본 UI 컴포넌트 라이브러리 설정 (Radix UI)
✅ 인증 시스템 기초 구현
✅ 기본 라우팅 구조 설계
```

#### **기술 스택 결정**

```typescript
Frontend: Next.js 14 + TypeScript
Backend: Supabase (PostgreSQL + Auth)
UI: Radix UI + Tailwind CSS
State: Zustand
Forms: React Hook Form + Zod
```

### **Phase 2: 핵심 기능 개발**

**기간**: 2024-11-16 ~ 2024-11-30

#### **데이터베이스 설계**

```sql
✅ 사용자 프로필 테이블 (profiles)
✅ 농장 정보 테이블 (farms)
✅ 방문자 기록 테이블 (visitor_entries)
✅ 기본 RLS 정책 설정
```

#### **핵심 기능 구현**

```
✅ 사용자 회원가입/로그인
✅ 농장 등록 및 관리
✅ QR 코드 생성
✅ 방문자 등록 시스템
✅ 기본 관리자 대시보드
```

### **Phase 3: 권한 시스템 개선**

**기간**: 2024-12-01 ~ 2024-12-07

#### **권한 시스템 재설계**

```sql
✅ farm_members 테이블 추가
✅ 계층적 권한 구조 구현
✅ RLS 정책 고도화
✅ 권한 확인 함수들 개발
```

#### **사용자 역할 정의**

```
- System Admin: 시스템 전체 관리
- Farm Owner: 농장 소유자
- Farm Manager: 농장 관리자
- Farm Viewer: 농장 조회자
- General User: 일반 사용자
```

### **Phase 4: 고급 기능 및 최적화**

**기간**: 2024-12-08 ~ 2024-12-14

#### **시스템 로그 시스템**

```sql
✅ system_logs 테이블 설계
✅ 포괄적 로깅 시스템 구현
✅ 로그 관리 기능 (필터링, 검색, 삭제)
✅ 페이지네이션 구현
```

#### **관리자 기능 강화**

```
✅ 통합 사용자 관리
✅ 농장 통계 및 분석
✅ CSV 데이터 내보내기
✅ 시스템 설정 관리
```

#### **성능 최적화**

```
✅ 데이터베이스 인덱스 최적화
✅ 쿼리 성능 개선
✅ 페이지네이션 구현
✅ 컴포넌트 최적화
```

## 📝 **주요 변경사항 (Changelog)**

### **v2.5.0 (2025-06-14) - 공통 파일 업로드 유틸리티 및 Storage 버킷 리팩토링**

#### **🎯 주요 개선사항**

- 공통 파일 업로드 유틸리티 구현으로 코드 중복 제거
- Storage 버킷명 변경 (avatars → profiles)
- 시스템 설정에 로고/파비콘 업로드 기능 추가

#### **📁 공통 파일 업로드 유틸리티 구현**

##### **새로운 유틸리티 파일 (`lib/utils/file-upload.ts`)**

- **핵심 함수**: `uploadFile()`, `validateFile()`
- **특화 함수**: `uploadProfileImage()`, `uploadSystemLogo()`, `uploadFavicon()`
- **헬퍼 함수**: `createFilePreview()`, `revokeFilePreview()`, `formatFileSize()`
- **타입 정의**: `FileUploadOptions`, `FileUploadResult`, `FileValidationResult`

##### **기존 코드 리팩토링**

- **계정관리 페이지**: 200+ 줄 → 50+ 줄로 단순화
- **중복 제거**: 파일 유효성 검사, 업로드, 에러 처리 로직 통합
- **메모리 최적화**: Blob URL 자동 해제로 메모리 누수 방지

#### **🗂️ Storage 버킷 구조 개선**

##### **버킷명 변경**

- **avatars** → **profiles**: 프로필 사진용 버킷
- **system**: 로고/파비콘용 새 버킷 추가

##### **파일 경로 변경**

- **Before**: `avatars/{userId}/avatar.{ext}`
- **After**: `profiles/{userId}/profile.{ext}`

##### **새로운 system 버킷**

```
system/
├── logo.{ext}           # 시스템 로고
└── favicon.{ext}        # 사이트 파비콘
```

#### **🎨 시스템 설정 UI 개선**

##### **브랜딩 설정 섹션 추가**

- **시스템 로고 업로드**: JPG, PNG, WebP, SVG 지원 (최대 2MB)
- **파비콘 업로드**: ICO, PNG, SVG 지원 (최대 1MB)
- **실시간 미리보기**: 선택한 파일의 즉시 미리보기
- **업로드 상태 표시**: 진행 중 스피너 및 상태 메시지

#### **🔒 보안 및 권한 강화**

##### **RLS 정책 업데이트**

- **profiles 버킷**: 사용자별 폴더 접근 제한
- **system 버킷**: 관리자만 업로드/수정/삭제 가능
- **공개 읽기**: 모든 파일 공개 접근 가능

##### **파일 유효성 검사**

- **크기 제한**: 프로필(5MB), 로고(2MB), 파비콘(1MB)
- **타입 검증**: MIME 타입 및 확장자 검증
- **사용자 친화적 에러 메시지**: 구체적인 실패 원인 안내

#### **📊 성능 최적화**

##### **메모리 관리**

- **Blob URL 관리**: `createFilePreview()` / `revokeFilePreview()`
- **자동 정리**: 컴포넌트 언마운트 시 메모리 해제
- **캐시 최적화**: 파일 타입별 적절한 캐시 시간 설정

##### **코드 품질 개선**

- **재사용성**: 모든 파일 업로드에 공통 유틸리티 활용
- **타입 안전성**: TypeScript 인터페이스로 타입 보장
- **확장성**: 새로운 파일 타입 추가 시 쉬운 확장

#### **🔄 마이그레이션 지원**

##### **마이그레이션 스크립트**

- **`scripts/migrate-avatars-to-profiles.sql`**: 기존 데이터 마이그레이션
- **`scripts/database-reset-and-rebuild.sql`**: 새로운 버킷 및 정책 설정 (12단계)
- **`docs/storage-migration-guide.md`**: 상세한 마이그레이션 가이드

##### **자동 URL 업데이트**

```sql
-- 기존 avatars URL을 profiles URL로 자동 변경
UPDATE profiles
SET profile_image_url = REPLACE(
  profile_image_url,
  '/storage/v1/object/public/avatars/',
  '/storage/v1/object/public/profiles/'
);
```

### **v2.0.0 (2025-06-14) - 인증 및 로그 시스템 대규모 개선**

#### **🎯 개선 목표**

- 안전하고 신뢰할 수 있는 로그아웃 시스템 구축
- 전체 관리 영역에 대한 통합 보안 적용
- 사용자 경험 개선 및 즉시 반응하는 인증 시스템

#### **🔐 ProtectedRoute 컴포넌트 구현**

##### **위치 및 기능 (`components/providers/protected-route.tsx`)**

- **자동 인증 확인**: 페이지 접근 시 자동으로 로그인 상태 확인
- **즉시 리다이렉트**: 미인증 사용자를 로그인 페이지로 즉시 이동
- **관리자 권한 체크**: `requireAdmin` 옵션으로 관리자 전용 페이지 보호
- **로딩 상태 관리**: 인증 확인 중 적절한 로딩 UI 표시

##### **사용법**

```typescript
// 일반 보호 페이지
<ProtectedRoute>
  <YourComponent />
</ProtectedRoute>

// 관리자 전용 페이지
<ProtectedRoute requireAdmin={true}>
  <AdminComponent />
</ProtectedRoute>
```

##### **전체 관리 영역 보호 (`app/admin/layout.tsx`)**

- **레이아웃 레벨 보호**: 모든 `/admin/*` 경로를 한 번에 보호
- **중복 제거**: 개별 페이지마다 보호 로직 적용할 필요 없음
- **일관성**: 모든 관리 페이지에서 동일한 보안 정책 적용

##### **보호되는 페이지 목록**

```
✅ /admin/dashboard - 대시보드
✅ /admin/farms - 농장 관리
✅ /admin/visitors - 방문자 관리
✅ /admin/all-visitors - 전체 방문자 기록
✅ /admin/management - 시스템 관리
✅ /admin/settings - 시스템 설정
✅ /admin/account - 계정 관리
✅ /admin/notifications - 알림 설정
```

#### **🔐 강화된 로그아웃 시스템**

##### **즉시 강제 리다이렉트 (`components/providers/auth-provider.tsx`)**

- **`window.location.href` 사용**: Next.js 라우터 문제 우회
- **즉시 실행**: 비동기 처리나 지연 없이 바로 실행
- **확실한 이동**: 가장 강력하고 확실한 페이지 이동 방법

##### **백그라운드 로그 처리**

```typescript
// 이전: 로그 생성 완료까지 대기
await createSystemLog(...);

// 개선: 백그라운드에서 비동기 처리
createSystemLog(...).then(() => {
  console.log("✅ Log created");
}).catch((error) => {
  console.warn("⚠️ Log failed:", error);
});
```

##### **다중 안전장치 시스템**

- **Auth Provider**: 로그아웃 함수에서 즉시 강제 리다이렉트
- **SIGNED_OUT 이벤트**: 세션 만료 시 즉시 리다이렉트
- **로그아웃 버튼**: 1초 타임아웃으로 백업 리다이렉트
- **에러 처리**: 모든 에러 상황에서도 강제 리다이렉트

##### **로그아웃 버튼 최적화 (`components/admin/admin-sidebar.tsx`)**

- **타임아웃 메커니즘**: 1초 내에 완료되지 않으면 강제 리다이렉트
- **즉시 완료 처리**: signOut이 빨리 완료되면 즉시 리다이렉트
- **상세한 디버깅**: 각 단계별 콘솔 로그로 문제 추적 가능

#### **📊 시스템 로그 개선**

##### **공통 로그 유틸리티 구현 (`lib/utils/system-log.ts`)**

- **통합 로그 생성 함수**: 모든 로그 생성을 하나의 함수로 통합
- **특화된 헬퍼 함수들**: Auth, Farm, Visitor, Error 전용 함수
- **실제 IP 주소 수집**: 클라이언트 실제 IP 주소 추적
- **자동 사용자 정보 수집**: 현재 로그인 사용자 정보 자동 포함

##### **중복 코드 대폭 제거**

- **6개 파일에서 ~200줄 중복 코드 제거**: 로그 생성 로직 통합
- **RLS 정책 최적화**: UUID vs TEXT 타입 불일치 해결
- **유지보수성 향상**: 단일 소스로 모든 로그 관리

### **v0.1.0 (2024-12-14) - 최종 릴리즈**

#### **🎉 새로운 기능**

```
✅ 완전한 시스템 로그 관리
  - 개별 로그 삭제 (로그 생성)
  - 30일 이전 로그 일괄 삭제 (요약 로그 생성)
  - 전체 로그 삭제 (완전 삭제 + 로그 생성)
  - 페이지네이션 (100개씩)

✅ 고급 사용자 관리
  - 권한별 필터링
  - 활동 상태 추적
  - 농장 연관 관계 관리

✅ 통계 및 분석 기능
  - 실시간 대시보드
  - 지역별 분포 분석
  - 농장 유형별 통계
  - 방문자 추이 분석
```

#### **🔧 개선사항**

```
✅ RLS 무한 재귀 문제 완전 해결
✅ 데이터베이스 성능 최적화
✅ 모바일 반응형 UI 개선
✅ 오류 처리 및 사용자 경험 향상
```

#### **🐛 버그 수정**

```
✅ 로그 카운팅 정확도 문제 해결
✅ 페이지네이션 성능 문제 해결
✅ 권한 확인 로직 안정화
✅ 세션 관리 개선
```

### **v0.0.9 (2024-12-13) - 시스템 로그 완성**

#### **🎉 새로운 기능**

```
✅ 시스템 로그 페이지네이션
✅ 로그 레벨별 필터링
✅ 로그 검색 기능
✅ 로그 상세보기 모달
```

#### **🔧 개선사항**

```
✅ 로그 생성 최적화
✅ 메타데이터 구조 개선
✅ 로그 표시 성능 향상
```

### **v0.0.8 (2024-12-12) - 관리자 기능 강화**

#### **🎉 새로운 기능**

```
✅ 사용자 관리 페이지
✅ 농장 통합 관리
✅ CSV 데이터 내보내기
✅ 시스템 설정 페이지
```

#### **🔧 개선사항**

```
✅ 대시보드 통계 정확도 향상
✅ 권한별 접근 제어 강화
✅ UI/UX 일관성 개선
```

### **v0.0.7 (2024-12-11) - 권한 시스템 안정화**

#### **🔧 개선사항**

```
✅ RLS 정책 최적화
✅ 권한 확인 함수 성능 개선
✅ 농장 구성원 관리 안정화
```

#### **🐛 버그 수정**

```
✅ 권한 확인 무한 루프 해결
✅ 농장 접근 권한 오류 수정
✅ 사용자 역할 업데이트 문제 해결
```

### **v0.0.6 (2024-12-10) - 방문자 시스템 완성**

#### **🎉 새로운 기능**

```
✅ QR 코드 스캔 페이지
✅ 방문자 등록 폼
✅ 개인정보 동의 처리
✅ 방문자 목록 및 검색
```

#### **🔧 개선사항**

```
✅ 모바일 최적화
✅ 폼 유효성 검사 강화
✅ 사용자 경험 개선
```

## 🔄 **데이터베이스 스키마 변경 히스토리**

### **2024-12-14: 최종 스키마 확정**

```sql
✅ system_logs 테이블 필드 추가
  - user_email: 사용자 이메일 추가
  - user_ip: IP 주소 추적
  - user_agent: 브라우저 정보

✅ 인덱스 최적화
  - 복합 인덱스 추가
  - 성능 향상을 위한 인덱스 재구성
```

### **2024-12-12: 시스템 로그 테이블 개선**

```sql
✅ system_logs 테이블 구조 개선
  - metadata JSONB 필드 추가
  - resource_type, resource_id 필드 추가
  - 로그 레벨 표준화
```

### **2024-12-08: 권한 시스템 재설계**

```sql
✅ farm_members 테이블 추가
  - 농장별 권한 관리
  - 역할 기반 접근 제어

✅ profiles 테이블 단순화
  - account_type 필드로 단순화
  - 농장별 권한은 farm_members로 분리
```

### **2024-12-05: RLS 정책 개선**

```sql
✅ 무한 재귀 문제 해결
  - 순환 참조 제거
  - SECURITY DEFINER 함수 활용
  - 성능 최적화
```

### **2024-11-30: 기본 스키마 완성**

```sql
✅ 핵심 테이블 생성
  - profiles, farms, visitor_entries
  - 기본 RLS 정책 설정
  - 트리거 및 함수 구현
```

## 🚀 **성능 개선 히스토리**

### **데이터베이스 최적화**

```sql
✅ 인덱스 전략 개선
  - 복합 인덱스 추가
  - 쿼리 성능 50% 향상

✅ RLS 정책 최적화
  - 무한 재귀 제거
  - 권한 확인 속도 3배 향상

✅ 페이지네이션 구현
  - 대용량 데이터 처리
  - 메모리 사용량 90% 감소
```

### **프론트엔드 최적화**

```typescript
✅ 컴포넌트 최적화
  - React.memo 적용
  - 불필요한 리렌더링 제거

✅ 상태 관리 개선
  - Zustand 도입
  - 전역 상태 최적화

✅ 번들 크기 최적화
  - 동적 임포트 적용
  - 트리 쉐이킹 최적화
```

## 🔒 **보안 강화 히스토리**

### **인증 및 권한**

```
✅ JWT 토큰 기반 인증
✅ 세션 관리 개선
✅ 권한별 접근 제어
✅ CSRF 보호 강화
```

### **데이터 보호**

```
✅ RLS 정책 완전 적용
✅ 개인정보 암호화
✅ 감사 로그 시스템
✅ 데이터 최소 수집 원칙
```

## 📊 **테스트 및 품질 관리**

### **테스트 커버리지**

```
✅ 단위 테스트: 80%+
✅ 통합 테스트: 70%+
✅ E2E 테스트: 주요 플로우
✅ 접근성 테스트: WCAG 2.1 AA
```

### **코드 품질**

```
✅ TypeScript 엄격 모드
✅ ESLint + Prettier 적용
✅ 코드 리뷰 프로세스
✅ 자동화된 품질 검사
```

## 🎯 **향후 개발 계획**

### **단기 계획 (1-3개월)**

```
🔄 모바일 앱 개발 (React Native)
🔄 오프라인 지원 (PWA)
🔄 다국어 지원 (i18n)
🔄 고급 분석 기능
```

### **중기 계획 (3-6개월)**

```
🔄 AI 기반 이상 탐지
🔄 자동화된 보고서 생성
🔄 외부 시스템 연동 API
🔄 고급 권한 관리
```

### **장기 계획 (6개월+)**

```
🔄 마이크로서비스 아키텍처
🔄 실시간 알림 시스템
🔄 블록체인 기반 인증
🔄 IoT 디바이스 연동
```

## 📚 **학습 및 개선사항**

### **기술적 학습**

```
✅ Supabase RLS 심화 이해
✅ Next.js 14 App Router 활용
✅ TypeScript 고급 패턴
✅ 성능 최적화 기법
```

### **프로세스 개선**

```
✅ 애자일 개발 방법론 적용
✅ 코드 리뷰 문화 정착
✅ 자동화된 배포 파이프라인
✅ 모니터링 및 알림 시스템
```

---

**문서 작성자**: AI Assistant  
**최종 업데이트**: 2024-12-14  
**다음 업데이트**: 주요 기능 추가 시

## 2024년 3월

### 비밀번호 검증 시스템 개선 (v1.2.0)

#### 주요 변경사항

- 비밀번호 검증 로직을 공통 유틸리티로 분리 (`lib/utils/password-validation.ts`)
- 모든 비밀번호 관련 페이지에 통일된 검증 규칙 적용
  - 회원가입 (`/register`)
  - 비밀번호 변경 (`/admin/account`)
  - 비밀번호 재설정 (`/reset-password/confirm`)

#### 기술적 개선

- TypeScript 타입 안정성 강화
  - 인터페이스 정의 추가
  - 타입 체크 강화
- 컴포넌트 재사용성 개선
  - 비밀번호 강도 표시 컴포넌트
  - 유효성 검사 오류 표시 컴포넌트
- 성능 최적화
  - 클라이언트 측 검증 강화
  - 불필요한 서버 요청 감소

#### UI/UX 개선

- 실시간 비밀번호 강도 표시 기능 추가
- 사용자 친화적인 오류 메시지 개선
- 접근성 고려한 디자인 적용
- 반응형 UI 구현

#### 테스트 및 품질 관리

- 단위 테스트 추가
- 통합 테스트 시나리오 구현
- 코드 리뷰 프로세스 강화

#### 문서화

- API 문서 업데이트
- 컴포넌트 사용 가이드 작성
- 개발자 가이드 보완
