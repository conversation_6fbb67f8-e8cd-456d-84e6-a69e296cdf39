export interface User {
  id: string;
  email: string;
  name: string;
  account_type: "admin" | "user";
  phone?: string;
  avatar_url?: string;
  company_name?: string;
  company_description?: string;
  last_login_at?: string;
  password_changed_at?: string;
  farms?: string[];
}

// 데모 사용자 데이터
export const DEMO_USERS: User[] = [
  {
    id: "admin-1",
    email: "<EMAIL>",
    name: "시스템 관리자",
    account_type: "admin",
    company_name: "농장 방문자 관리 시스템",
  },
  {
    id: "owner-1",
    email: "<EMAIL>",
    name: "홍길동",
    account_type: "user",
    company_name: "그린팜",
    farms: ["demo-farm-1", "demo-farm-2"],
  },
  {
    id: "owner-2",
    email: "<EMAIL>",
    name: "김농부",
    account_type: "user",
    company_name: "블루팜",
    farms: ["demo-farm-3"],
  },
  {
    id: "manager-1",
    email: "<EMAIL>",
    name: "이관리",
    account_type: "user",
    company_name: "그린팜",
    farms: ["demo-farm-1"],
  },
];

export const getCurrentUser = (): User | null => {
  if (typeof window === "undefined") return null;

  const userData = localStorage.getItem("current_user");
  if (userData) {
    return JSON.parse(userData);
  }
  return null;
};

export const setCurrentUser = (user: User | null) => {
  if (typeof window === "undefined") return;

  if (user) {
    localStorage.setItem("current_user", JSON.stringify(user));
  } else {
    localStorage.removeItem("current_user");
  }
};

export const loginUser = (email: string, password: string): User | null => {
  // 데모용 간단한 로그인 (실제로는 비밀번호 검증 필요)
  const user = DEMO_USERS.find((u) => u.email === email);
  if (user) {
    setCurrentUser(user);
    return user;
  }
  return null;
};

export const logoutUser = () => {
  setCurrentUser(null);
};
