import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    // 관리자 권한 확인
    const {
      data: { user },
    } = await supabase.auth.getUser();

    const { data: profile } = await supabase
      .from("profiles")
      .select("account_type")
      .eq("id", user?.id)
      .single();

    if (profile?.account_type !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized access" },
        { status: 403 }
      );
    }

    // 오늘 날짜 설정
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // 이번 달 설정
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const firstDayOfNextMonth = new Date(
      today.getFullYear(),
      today.getMonth() + 1,
      1
    );

    // 통계 데이터 수집
    const [
      todayUsersResult,
      totalUsersResult,
      todayVisitorsResult,
      monthlyVisitorsResult,
      activeFarmsResult,
      todayLogsResult,
      errorLogsResult,
    ] = await Promise.all([
      // 오늘 가입한 사용자 수
      supabase
        .from("profiles")
        .select("id", { count: "exact" })
        .gte("created_at", today.toISOString())
        .lt("created_at", tomorrow.toISOString()),

      // 전체 사용자 수 (계정 유형별)
      supabase.from("profiles").select("account_type"),

      // 오늘의 방문자 수
      supabase
        .from("visitor_entries")
        .select("id", { count: "exact" })
        .gte("created_at", today.toISOString())
        .lt("created_at", tomorrow.toISOString()),

      // 이번 달 방문자 수
      supabase
        .from("visitor_entries")
        .select("id", { count: "exact" })
        .gte("created_at", firstDayOfMonth.toISOString())
        .lt("created_at", firstDayOfNextMonth.toISOString()),

      // 활성 농장 수
      supabase
        .from("farms")
        .select("id", { count: "exact" })
        .eq("is_active", true),

      // 오늘의 시스템 로그 수 (레벨별)
      supabase
        .from("system_logs")
        .select("level")
        .gte("created_at", today.toISOString())
        .lt("created_at", tomorrow.toISOString()),

      // 에러 로그 수 (최근 30일)
      supabase
        .from("system_logs")
        .select("id", { count: "exact" })
        .eq("level", "error")
        .gte(
          "created_at",
          new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString()
        ),
    ]);

    // 계정 유형별 사용자 수 계산
    const usersByType = totalUsersResult.data?.reduce(
      (acc: Record<string, number>, user) => {
        const type = user.account_type || "unknown";
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      },
      {}
    );

    // 오늘의 로그 레벨별 수 계산
    const logsByLevel = todayLogsResult.data?.reduce(
      (acc: Record<string, number>, log) => {
        const level = log.level || "unknown";
        acc[level] = (acc[level] || 0) + 1;
        return acc;
      },
      {}
    );

    return NextResponse.json({
      today: {
        new_users: todayUsersResult.count || 0,
        visitors: todayVisitorsResult.count || 0,
        logs: todayLogsResult.data?.length || 0,
        logs_by_level: logsByLevel || {},
      },
      monthly: {
        visitors: monthlyVisitorsResult.count || 0,
      },
      total: {
        users: totalUsersResult.data?.length || 0,
        users_by_type: usersByType || {},
        active_farms: activeFarmsResult.count || 0,
        error_logs_30d: errorLogsResult.count || 0,
      },
    });
  } catch (error) {
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
