"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Leaf, MapPin, Phone, Car, FileText, Shield, CheckCircle2 } from "lucide-react"
import { motion } from "framer-motion"

export default function DemoVisitorEntry() {
  const [formData, setFormData] = useState({
    fullName: "",
    phoneNumber: "",
    address: "",
    carPlateNumber: "",
    visitPurpose: "",
    disinfectionCheck: false,
    notes: "",
    consentGiven: false,
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.consentGiven) {
      alert("개인정보 수집에 동의해주세요.")
      return
    }

    if (!formData.fullName || !formData.phoneNumber) {
      alert("필수 항목을 모두 입력해주세요.")
      return
    }

    setIsSubmitting(true)

    // API 호출 시뮬레이션
    await new Promise((resolve) => setTimeout(resolve, 1500))

    setIsSubmitted(true)
    setIsSubmitting(false)
  }

  const nextStep = () => {
    if (currentStep === 1 && (!formData.fullName || !formData.phoneNumber)) {
      alert("이름과 전화번호는 필수 입력 항목입니다.")
      return
    }
    setCurrentStep((prev) => prev + 1)
  }

  const prevStep = () => {
    setCurrentStep((prev) => prev - 1)
  }

  if (isSubmitted) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-farm p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md"
        >
          <Card className="border-none shadow-soft-lg">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-green-100">
                <CheckCircle2 className="h-10 w-10 text-primary" />
              </div>
              <CardTitle className="text-2xl text-primary">방문 등록 완료!</CardTitle>
              <CardDescription className="text-lg">데모 농장 방문이 성공적으로 등록되었습니다.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="rounded-lg bg-accent p-4">
                <h3 className="mb-2 font-semibold text-primary">방문 정보</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">농장:</span>
                    <span className="font-medium">데모 농장</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">시간:</span>
                    <span className="font-medium">{new Date().toLocaleString("ko-KR")}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">방문자:</span>
                    <span className="font-medium">{formData.fullName}</span>
                  </div>
                </div>
              </div>

              <div className="rounded-lg bg-blue-50 p-4">
                <h3 className="mb-2 font-semibold text-blue-700">농장 연락처</h3>
                <div className="space-y-1 text-sm">
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-blue-500" />
                    <span>010-1234-5678</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-blue-500" />
                    <span>경기도 화성시 농장로 123</span>
                  </div>
                </div>
              </div>

              <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4 text-sm text-yellow-800">
                <p>방문 시 농장의 방역 수칙을 준수해 주시기 바랍니다.</p>
              </div>

              <Button onClick={() => window.location.reload()} className="w-full">
                다른 방문 등록하기
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-farm p-4">
      <div className="container mx-auto max-w-2xl">
        {/* 헤더 */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8 text-center"
        >
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
            <Leaf className="h-8 w-8 text-primary" />
          </div>
          <h1 className="mb-2 text-2xl font-bold text-primary md:text-3xl">데모 농장</h1>
          <Badge variant="outline" className="mb-4">
            <MapPin className="mr-1 h-3 w-3" />
            경기도 화성시 농장로 123
          </Badge>
          <p className="text-muted-foreground">방문자 정보를 입력해주세요</p>
        </motion.div>

        {/* 단계 표시기 */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex flex-1 flex-col items-center">
                <div
                  className={`flex h-10 w-10 items-center justify-center rounded-full border-2 ${
                    currentStep >= step
                      ? "border-primary bg-primary text-primary-foreground"
                      : "border-muted-foreground/30 bg-background text-muted-foreground"
                  }`}
                >
                  {step}
                </div>
                <span className={`mt-2 text-xs ${currentStep >= step ? "text-primary" : "text-muted-foreground"}`}>
                  {step === 1 ? "기본 정보" : step === 2 ? "추가 정보" : "확인"}
                </span>
              </div>
            ))}
          </div>
          <div className="relative mt-2">
            <div className="absolute left-0 top-0 h-1 w-full rounded bg-muted"></div>
            <div
              className="absolute left-0 top-0 h-1 rounded bg-primary transition-all duration-300"
              style={{ width: `${((currentStep - 1) / 2) * 100}%` }}
            ></div>
          </div>
        </div>

        {/* 폼 */}
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="border-none shadow-soft-lg">
            <CardHeader>
              <CardTitle>
                {currentStep === 1 ? "기본 정보 입력" : currentStep === 2 ? "추가 정보 입력" : "정보 확인 및 제출"}
              </CardTitle>
              <CardDescription>
                {currentStep === 1
                  ? "방문자 기본 정보를 입력해주세요"
                  : currentStep === 2
                    ? "추가 정보를 입력해주세요 (선택사항)"
                    : "입력한 정보를 확인하고 제출해주세요"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {currentStep === 1 && (
                  <>
                    {/* 기본 정보 */}
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="fullName" className="text-sm font-medium">
                          이름 <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="fullName"
                          value={formData.fullName}
                          onChange={(e) => handleInputChange("fullName", e.target.value)}
                          placeholder="홍길동"
                          className="h-12 input-focus"
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="phoneNumber" className="text-sm font-medium">
                          전화번호 <span className="text-red-500">*</span>
                        </Label>
                        <div className="relative">
                          <Phone className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                          <Input
                            id="phoneNumber"
                            type="tel"
                            value={formData.phoneNumber}
                            onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
                            placeholder="010-1234-5678"
                            className="h-12 pl-10 input-focus"
                            required
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="address" className="text-sm font-medium">
                          주소
                        </Label>
                        <div className="relative">
                          <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                          <Textarea
                            id="address"
                            value={formData.address}
                            onChange={(e) => handleInputChange("address", e.target.value)}
                            placeholder="서울특별시 강남구 테헤란로 123"
                            className="min-h-[80px] pl-10 input-focus"
                          />
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {currentStep === 2 && (
                  <>
                    {/* 추가 정보 */}
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="carPlateNumber" className="text-sm font-medium">
                          차량 번호
                        </Label>
                        <div className="relative">
                          <Car className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                          <Input
                            id="carPlateNumber"
                            value={formData.carPlateNumber}
                            onChange={(e) => handleInputChange("carPlateNumber", e.target.value.toUpperCase())}
                            placeholder="12가 3456"
                            className="h-12 pl-10 uppercase input-focus"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="visitPurpose" className="text-sm font-medium">
                          방문 목적
                        </Label>
                        <Input
                          id="visitPurpose"
                          value={formData.visitPurpose}
                          onChange={(e) => handleInputChange("visitPurpose", e.target.value)}
                          placeholder="배달, 점검, 미팅 등"
                          className="h-12 input-focus"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="notes" className="text-sm font-medium">
                          추가 메모
                        </Label>
                        <div className="relative">
                          <FileText className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                          <Textarea
                            id="notes"
                            value={formData.notes}
                            onChange={(e) => handleInputChange("notes", e.target.value)}
                            placeholder="추가 정보가 있으면 입력해주세요..."
                            className="min-h-[80px] pl-10 input-focus"
                          />
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {currentStep === 3 && (
                  <>
                    {/* 정보 확인 */}
                    <div className="space-y-6">
                      <div className="rounded-lg bg-accent p-4">
                        <h3 className="mb-3 font-semibold">방문자 정보</h3>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">이름:</span>
                            <span className="font-medium">{formData.fullName}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">전화번호:</span>
                            <span className="font-medium">{formData.phoneNumber}</span>
                          </div>
                          {formData.address && (
                            <div className="flex justify-between">
                              <span className="text-sm text-muted-foreground">주소:</span>
                              <span className="max-w-[60%] text-right font-medium">{formData.address}</span>
                            </div>
                          )}
                          {formData.carPlateNumber && (
                            <div className="flex justify-between">
                              <span className="text-sm text-muted-foreground">차량번호:</span>
                              <span className="font-medium">{formData.carPlateNumber}</span>
                            </div>
                          )}
                          {formData.visitPurpose && (
                            <div className="flex justify-between">
                              <span className="text-sm text-muted-foreground">방문목적:</span>
                              <span className="font-medium">{formData.visitPurpose}</span>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="disinfectionCheck"
                          checked={formData.disinfectionCheck}
                          onCheckedChange={(checked) => handleInputChange("disinfectionCheck", checked as boolean)}
                        />
                        <Label htmlFor="disinfectionCheck" className="text-sm">
                          <span className="flex items-center">
                            <Shield className="mr-1 h-4 w-4 text-primary" />
                            필요한 소독 절차를 완료했습니다
                          </span>
                        </Label>
                      </div>

                      <Separator />

                      <div className="rounded-lg border border-blue-100 bg-blue-50 p-4">
                        <div className="flex items-start space-x-2">
                          <Checkbox
                            id="consent"
                            checked={formData.consentGiven}
                            onCheckedChange={(checked) => handleInputChange("consentGiven", checked as boolean)}
                            required
                          />
                          <Label htmlFor="consent" className="text-xs text-blue-800">
                            본인은 농장 방역 및 방문자 관리 목적으로 개인정보 수집 및 처리에 동의합니다. 이 정보는
                            기밀로 유지되며 농장 운영 목적으로만 사용됩니다. <span className="text-red-500">*</span>
                          </Label>
                        </div>
                      </div>
                    </div>
                  </>
                )}

                <div className="flex justify-between space-x-4 pt-4">
                  {currentStep > 1 && (
                    <Button type="button" variant="outline" onClick={prevStep} className="flex-1">
                      이전
                    </Button>
                  )}
                  {currentStep < 3 ? (
                    <Button type="button" onClick={nextStep} className="flex-1">
                      다음
                    </Button>
                  ) : (
                    <Button type="submit" disabled={isSubmitting || !formData.consentGiven} className="flex-1">
                      {isSubmitting ? "제출 중..." : "방문 등록"}
                    </Button>
                  )}
                </div>
              </form>
            </CardContent>
          </Card>
        </motion.div>

        {/* 푸터 */}
        <div className="mt-8 text-center text-sm text-muted-foreground">
          <p>© 2023 농장 방문자 관리 시스템</p>
        </div>
      </div>
    </div>
  )
}
