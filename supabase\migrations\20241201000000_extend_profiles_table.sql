-- 계정 관리 고도화를 위한 profiles 테이블 확장
-- 2024-12-01: 핵심 개인 정보 및 회사 정보 필드 추가

-- 1. 개인 정보 관련 컬럼 추가
ALTER TABLE public.profiles
ADD COLUMN IF NOT EXISTS position TEXT,                    -- 직책
ADD COLUMN IF NOT EXISTS department TEXT,                  -- 부서
ADD COLUMN IF NOT EXISTS bio TEXT,                        -- 자기소개
ADD COLUMN IF NOT EXISTS avatar_url TEXT;                 -- 프로필 사진 URL

-- 2. 회사 정보 관련 컬럼 추가
ALTER TABLE public.profiles
ADD COLUMN IF NOT EXISTS company_name TEXT,               -- 회사명
ADD COLUMN IF NOT EXISTS company_address TEXT,            -- 회사 주소
ADD COLUMN IF NOT EXISTS business_type TEXT,              -- 업종
ADD COLUMN IF NOT EXISTS company_description TEXT,        -- 회사 소개
ADD COLUMN IF NOT EXISTS established_date DATE,           -- 설립일
ADD COLUMN IF NOT EXISTS employee_count TEXT,             -- 직원 수
ADD COLUMN IF NOT EXISTS company_website TEXT;            -- 회사 웹사이트

-- 3. 보안 관련 컬럼 추가
ALTER TABLE public.profiles
ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMPTZ,       -- 마지막 로그인 시간
ADD COLUMN IF NOT EXISTS password_changed_at TIMESTAMPTZ; -- 비밀번호 변경 시간

-- 4. 기본값 설정 (기존 사용자들을 위해)
UPDATE public.profiles
SET
  position = COALESCE(position, '사용자'),
  department = COALESCE(department, '일반'),
  bio = COALESCE(bio, ''),
  company_name = COALESCE(company_name, ''),
  company_address = COALESCE(company_address, ''),
  business_type = COALESCE(business_type, '농업'),
  company_description = COALESCE(company_description, ''),
  employee_count = COALESCE(employee_count, '1-10명'),
  company_website = COALESCE(company_website, '')
WHERE
  position IS NULL
  OR department IS NULL
  OR bio IS NULL
  OR company_name IS NULL
  OR company_address IS NULL
  OR business_type IS NULL
  OR company_description IS NULL
  OR employee_count IS NULL
  OR company_website IS NULL;

-- 5. 인덱스 추가 (성능 최적화)
CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_company_name ON public.profiles(company_name);
CREATE INDEX IF NOT EXISTS idx_profiles_last_login_at ON public.profiles(last_login_at);

-- 6. 프로필 업데이트 시 updated_at 자동 갱신 (기존 함수 활용)
-- update_updated_at_column 함수는 이미 존재하므로 트리거만 확인

-- 7. 로그인 기록 업데이트 함수 생성
CREATE OR REPLACE FUNCTION update_login_stats(user_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.profiles
  SET
    last_login_at = NOW(),
    updated_at = NOW()
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. 비밀번호 변경 기록 함수 생성
CREATE OR REPLACE FUNCTION update_password_changed(user_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE public.profiles
  SET
    password_changed_at = NOW(),
    updated_at = NOW()
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
