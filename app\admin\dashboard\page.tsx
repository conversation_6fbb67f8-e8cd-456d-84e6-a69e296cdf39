"use client";

import { SidebarTrigger } from "@/components/ui/sidebar";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/components/providers/auth-provider";
import { useFarms } from "@/hooks/use-farms";
import { useVisitorEntries } from "@/hooks/use-visitor-entries";
import {
  Building2,
  Users,
  Clock,
  TrendingUp,
  QrCode,
  Shield,
  Bell,
  Crown,
  UserCheck,
  BarChart3,
} from "lucide-react";
import { motion } from "framer-motion";
import { MobileMenuButton } from "@/components/mobile-menu-button";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb";

export default function DashboardPage() {
  const { profile } = useAuth();
  const { farms, loading: farmsLoading } = useFarms();
  const {
    entries,
    getTodayEntries,
    getThisWeekEntries,
    loading: entriesLoading,
    getAllEntries,
  } = useVisitorEntries();
  const isMobile = useIsMobile();

  // 호환성을 위한 user 별칭 생성
  const user = profile;

  // 관리자인 경우 전체 데이터 사용
  const allFarms = profile?.account_type === "admin" ? farms : farms;
  const allEntries =
    profile?.account_type === "admin" ? getAllEntries() : entries;

  const stats = {
    totalFarms: allFarms.length,
    totalVisitors: allEntries.length,
    todayVisitors:
      profile?.account_type === "admin"
        ? getAllEntries().filter((e: any) =>
            e.entry_datetime.startsWith(new Date().toISOString().split("T")[0])
          ).length
        : getTodayEntries().length,
    thisWeekVisitors:
      profile?.account_type === "admin"
        ? getAllEntries().filter((e: any) => {
            const weekAgo = new Date();
            weekAgo.setDate(weekAgo.getDate() - 7);
            return new Date(e.entry_datetime) >= weekAgo;
          }).length
        : getThisWeekEntries().length,
  };

  // 최근 활동 (최근 5개)
  const recentActivities = allEntries
    .sort(
      (a: any, b: any) =>
        new Date(b.entry_datetime).getTime() -
        new Date(a.entry_datetime).getTime()
    )
    .slice(0, 5)
    .map((entry: any) => {
      const farm = allFarms.find((f: any) => f.id === entry.farm_id);
      const entryTime = new Date(entry.entry_datetime);
      const now = new Date();
      const diffMinutes = Math.floor(
        (now.getTime() - entryTime.getTime()) / (1000 * 60)
      );

      let timeAgo = "";
      if (diffMinutes < 1) {
        timeAgo = "방금 전";
      } else if (diffMinutes < 60) {
        timeAgo = `${diffMinutes}분 전`;
      } else if (diffMinutes < 1440) {
        timeAgo = `${Math.floor(diffMinutes / 60)}시간 전`;
      } else {
        timeAgo = `${Math.floor(diffMinutes / 1440)}일 전`;
      }

      return {
        id: entry.id,
        visitor: entry.full_name,
        farm: farm?.farm_name || "알 수 없는 농장",
        time: timeAgo,
        type: "입장",
      };
    });

  // 농장별 통계 (농장주/관리자용)
  const farmStats = farms.map((farm: any) => {
    const farmEntries = allEntries.filter(
      (entry: any) => entry.farm_id === farm.id
    );
    const todayEntries = farmEntries.filter((entry: any) =>
      entry.entry_datetime.startsWith(new Date().toISOString().split("T")[0])
    );
    const weekEntries = farmEntries.filter((entry: any) => {
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return new Date(entry.entry_datetime) >= weekAgo;
    });

    return {
      farm,
      totalVisitors: farmEntries.length,
      todayVisitors: todayEntries.length,
      weekVisitors: weekEntries.length,
      disinfectionRate:
        farmEntries.length > 0
          ? Math.round(
              (farmEntries.filter((e: any) => e.disinfection_check).length /
                farmEntries.length) *
                100
            )
          : 0,
    };
  });

  if (farmsLoading || entriesLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>대시보드를 불러오는 중...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-6 pt-2 md:pt-4">
      {/* 브레드크럼 */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbPage>대시보드</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* 모바일 메뉴 버튼 (모바일에서만 표시) */}
      {isMobile && <MobileMenuButton />}

      {/* 헤더 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {/* 데스크톱에서만 메뉴 버튼 표시 */}
          {!isMobile && (
            <SidebarTrigger
              className="flex items-center gap-2 px-3 py-2 bg-primary/10 hover:bg-primary/20 rounded-lg transition-colors"
              aria-label="메뉴 열기/닫기"
            >
              <Users className="h-5 w-5" />
              <span className="text-sm font-medium">메뉴</span>
            </SidebarTrigger>
          )}

          <div>
            <div className="flex items-center space-x-2 mb-4">
              <h2 className="text-3xl font-bold tracking-tight flex items-center gap-2">
                <BarChart3 className="h-8 w-8 text-primary" />
                대시보드
              </h2>
              {user?.account_type === "admin" && (
                <Badge variant="default" className="bg-purple-600">
                  <Crown className="w-3 h-3 mr-1" />
                  시스템 관리자
                </Badge>
              )}
              {user?.account_type === "user" && (
                <Badge variant="default" className="bg-green-600">
                  <Building2 className="w-3 h-3 mr-1" />
                  일반 사용자
                </Badge>
              )}
            </div>
            <p className="text-muted-foreground mt-2">
              안녕하세요, {user?.name}님!
              {user?.account_type === "admin"
                ? " 전체 시스템 현황을 확인하세요."
                : " 오늘의 농장 현황을 확인하세요."}
            </p>
          </div>
        </div>
      </div>

      {/* 통계 카드 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Card className="card-hover">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {user?.account_type === "admin" ? "전체 농장" : "관리 농장"}
              </CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">
                {stats.totalFarms}
              </div>
              <p className="text-xs text-muted-foreground">
                {user?.account_type === "admin"
                  ? "시스템 내 모든 농장"
                  : "관리 중인 농장 수"}
              </p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card className="card-hover">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">전체 방문자</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">
                {stats.totalVisitors}
              </div>
              <p className="text-xs text-muted-foreground">누적 방문 기록</p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Card className="card-hover">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">오늘 방문자</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">
                {stats.todayVisitors}
              </div>
              <p className="text-xs text-muted-foreground">
                오늘 등록된 방문자
              </p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <Card className="card-hover">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">이번 주</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">
                {stats.thisWeekVisitors}
              </div>
              <p className="text-xs text-muted-foreground">지난 7일간 방문자</p>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* 메인 콘텐츠 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        {/* 최근 활동 */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="col-span-4"
        >
          <Card>
            <CardHeader>
              <CardTitle>최근 방문자 활동</CardTitle>
              <CardDescription>
                {user?.account_type === "admin"
                  ? "전체 시스템의 실시간 방문자 현황"
                  : "실시간으로 업데이트되는 방문자 현황"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivities.length > 0 ? (
                  recentActivities.map((activity: any, index: number) => (
                    <motion.div
                      key={activity.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: 0.6 + index * 0.1 }}
                      className="flex items-center justify-between rounded-lg border p-3"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                          <Users className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                          <p className="text-sm font-medium">
                            {activity.visitor}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {activity.farm}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-xs text-muted-foreground">
                          {activity.time}
                        </p>
                        <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                          {activity.type}
                        </span>
                      </div>
                    </motion.div>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>아직 방문자 활동이 없습니다</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* 사이드바 - 역할별 다른 내용 */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="col-span-3 space-y-4"
        >
          {/* 농장별 통계 (일반 사용자용) */}
          {user?.account_type === "user" && (
            <Card>
              <CardHeader>
                <CardTitle>농장별 현황</CardTitle>
                <CardDescription>각 농장의 방문자 통계</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {farmStats.map((stat: any, index: number) => (
                  <motion.div
                    key={stat.farm.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7 + index * 0.1 }}
                    className="rounded-lg border p-3"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-sm">
                        {stat.farm.farm_name}
                      </h4>
                      <Badge variant="outline">{stat.farm.animal_type}</Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <span className="text-muted-foreground">오늘: </span>
                        <span className="font-medium">
                          {stat.todayVisitors}명
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">이번주: </span>
                        <span className="font-medium">
                          {stat.weekVisitors}명
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">총 방문: </span>
                        <span className="font-medium">
                          {stat.totalVisitors}명
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">소독률: </span>
                        <span className="font-medium">
                          {stat.disinfectionRate}%
                        </span>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* 시스템 통계 (관리자용) */}
          {user?.account_type === "admin" && (
            <Card>
              <CardHeader>
                <CardTitle>시스템 통계</CardTitle>
                <CardDescription>전체 시스템 현황</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">총 농장주:</span>
                    <span className="font-medium">3명</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">활성 농장:</span>
                    <span className="font-medium">{allFarms.length}개</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">월 평균 방문:</span>
                    <span className="font-medium">
                      {Math.round(allEntries.length / 30)}명/일
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">전체 소독률:</span>
                    <span className="font-medium">
                      {allEntries.length > 0
                        ? Math.round(
                            (allEntries.filter((e: any) => e.disinfection_check)
                              .length /
                              allEntries.length) *
                              100
                          )
                        : 0}
                      %
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 빠른 작업 */}
          <Card>
            <CardHeader>
              <CardTitle>빠른 작업</CardTitle>
              <CardDescription>자주 사용하는 기능들</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid gap-3">
                <div className="flex items-center space-x-3 rounded-lg border p-3 hover:bg-accent cursor-pointer transition-colors">
                  <QrCode className="h-5 w-5 text-primary" />
                  <div>
                    <p className="text-sm font-medium">QR 코드 생성</p>
                    <p className="text-xs text-muted-foreground">
                      새 농장 QR 코드
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-3 rounded-lg border p-3 hover:bg-accent cursor-pointer transition-colors">
                  <Shield className="h-5 w-5 text-primary" />
                  <div>
                    <p className="text-sm font-medium">방역 체크</p>
                    <p className="text-xs text-muted-foreground">
                      방역 현황 확인
                    </p>
                  </div>
                </div>
                {user?.account_type === "admin" && (
                  <div className="flex items-center space-x-3 rounded-lg border p-3 hover:bg-accent cursor-pointer transition-colors">
                    <BarChart3 className="h-5 w-5 text-primary" />
                    <div>
                      <p className="text-sm font-medium">시스템 분석</p>
                      <p className="text-xs text-muted-foreground">
                        전체 통계 분석
                      </p>
                    </div>
                  </div>
                )}
                <div className="flex items-center space-x-3 rounded-lg border p-3 hover:bg-accent cursor-pointer transition-colors">
                  <Bell className="h-5 w-5 text-primary" />
                  <div>
                    <p className="text-sm font-medium">알림 설정</p>
                    <p className="text-xs text-muted-foreground">알림 관리</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 시스템 상태 */}
          <Card>
            <CardHeader>
              <CardTitle>시스템 상태</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">QR 코드 시스템</span>
                  <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                    정상
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">알림 서비스</span>
                  <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                    정상
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">로컬 저장소</span>
                  <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                    정상
                  </span>
                </div>
                {user?.account_type === "admin" && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm">데모 모드</span>
                    <span className="inline-flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">
                      활성
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* 모바일에서 하단 여백 추가 (메뉴 버튼 가리지 않도록) */}
      {isMobile && <div className="h-20" />}
    </div>
  );
}
