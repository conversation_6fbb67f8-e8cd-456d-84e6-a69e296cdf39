import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { User, Camera } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { uploadImage, deleteImage } from "@/lib/utils/storage";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";

interface ProfileImageUploadProps {
  profileId: string;
  currentImageUrl?: string | null;
  onImageUpdate: (newImageUrl: string) => void;
}

export function ProfileImageUpload({
  profileId,
  currentImageUrl,
  onImageUpdate,
}: ProfileImageUploadProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [profileImageFile, setProfileImageFile] = useState<File | null>(null);
  const [profileImagePreview, setProfileImagePreview] = useState<string | null>(
    null
  );

  const handleProfileImageChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setProfileImageFile(file);

    // 이미지 미리보기 생성
    const reader = new FileReader();
    reader.onload = (e) => {
      setProfileImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  const handleProfileImageUpload = async () => {
    if (!profileImageFile) {
      toast({
        description: "업로드할 이미지를 선택해주세요.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    sessionStorage.setItem("profile_image_uploading", "true");

    try {
      const fileExt = profileImageFile.type.split("/")[1];
      const fileName = `${profileId}/profile.${fileExt}`;

      // 기존 이미지가 있다면 삭제
      if (currentImageUrl) {
        await deleteImage("profiles", currentImageUrl.split("/").pop() || "");
      }

      // 새 이미지 업로드
      const { publicUrl } = await uploadImage({
        file: profileImageFile,
        bucket: "profiles",
        path: fileName,
        maxSizeMB: 5,
        allowedFileTypes: ["image/jpeg", "image/png", "image/webp"],
      });

      // 프로필에 profile_image_url 업데이트
      const supabase = createClientComponentClient();
      const { error: updateError } = await supabase
        .from("profiles")
        .update({
          profile_image_url: publicUrl,
        })
        .eq("id", profileId);

      if (updateError) throw updateError;

      toast({
        title: "업로드 완료",
        description: "프로필 이미지가 업데이트되었습니다.",
      });

      // 상태 초기화 및 부모 컴포넌트 업데이트
      setProfileImageFile(null);
      setProfileImagePreview(null);
      onImageUpdate(publicUrl);
    } catch (error) {
      console.error("Profile image upload error:", error);
      toast({
        title: "업로드 실패",
        description:
          error instanceof Error
            ? error.message
            : "이미지 업로드에 실패했습니다.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      sessionStorage.removeItem("profile_image_uploading");
    }
  };

  return (
    <div className="flex items-center space-x-4">
      <div className="relative">
        <div className="w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center overflow-hidden">
          {profileImagePreview || currentImageUrl ? (
            <img
              src={profileImagePreview || currentImageUrl}
              alt="프로필 이미지"
              className="w-full h-full object-cover"
              onError={(e) => {
                console.error("Image load error:", e);
              }}
            />
          ) : (
            <User className="w-10 h-10 text-primary" />
          )}
        </div>
        <input
          type="file"
          accept="image/*"
          onChange={handleProfileImageChange}
          className="hidden"
          id="profile-image-upload"
        />
        <Button
          size="sm"
          className="absolute -bottom-2 -right-2 rounded-full w-8 h-8 p-0"
          onClick={() =>
            document.getElementById("profile-image-upload")?.click()
          }
          disabled={loading}
        >
          <Camera className="w-4 h-4" />
        </Button>
      </div>
      <div className="flex-1">
        <h4 className="text-sm font-medium">프로필 이미지</h4>
        <p className="text-sm text-muted-foreground">
          JPG, PNG 파일을 업로드하세요 (최대 5MB)
        </p>

        {profileImageFile && (
          <div className="mt-2 flex items-center gap-2 bg-yellow-50 p-2 rounded border">
            <p className="text-xs text-green-600">
              선택된 파일: {profileImageFile.name}
            </p>
            <Button
              size="sm"
              onClick={handleProfileImageUpload}
              disabled={loading}
              className="h-6 px-2 text-xs"
            >
              {loading ? (
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"></div>
                  업로드 중...
                </div>
              ) : (
                "업로드"
              )}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
