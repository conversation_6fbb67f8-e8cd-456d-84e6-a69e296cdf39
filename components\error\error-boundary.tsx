"use client";

import React, { Component, ReactNode } from "react";
import { AdminError } from "./admin-error";
import { createErrorLog } from "@/lib/utils/system-log";

interface Props {
  children: ReactNode;
  fallback?: (error: Error, reset: () => void) => ReactNode;
  title?: string;
  description?: string;
  showNavigation?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("ErrorBoundary caught an error:", error, errorInfo);
    
    // 에러 로그 생성
    createErrorLog(
      "REACT_ERROR_BOUNDARY",
      error,
      "React Error Boundary에서 에러 포착",
      undefined,
      `컴포넌트 렌더링 중 오류: ${error.message}`
    ).catch((logError) => {
      console.error("Failed to log error boundary error:", logError);
    });
  }

  reset = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError && this.state.error) {
      // 커스텀 fallback이 제공된 경우
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.reset);
      }

      // 기본 AdminError 컴포넌트 사용
      return (
        <AdminError
          error={this.state.error}
          reset={this.reset}
          title={this.props.title}
          description={this.props.description}
          showNavigation={this.props.showNavigation}
        />
      );
    }

    return this.props.children;
  }
}

// Hook 버전의 Error Boundary (React 18+)
export function withErrorBoundary<T extends object>(
  Component: React.ComponentType<T>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: T) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}
