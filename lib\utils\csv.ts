/**
 * CSV 내보내기 유틸리티
 * 
 * 여러 페이지에서 사용되는 공통 CSV 내보내기 로직을 모아둔 유틸리티입니다.
 */

import { getFileNameDate } from "./date";

/**
 * CSV 내보내기 옵션
 */
export interface CSVExportOptions {
  /** 파일명 (확장자 제외) */
  filename?: string;
  /** UTF-8 BOM 추가 여부 (한글 깨짐 방지) */
  includeBOM?: boolean;
  /** 날짜를 파일명에 포함할지 여부 */
  includeDate?: boolean;
  /** 커스텀 구분자 (기본값: 쉼표) */
  delimiter?: string;
}

/**
 * CSV 데이터를 파일로 다운로드
 * @param headers CSV 헤더 배열
 * @param data CSV 데이터 배열 (각 행은 문자열 배열)
 * @param options 내보내기 옵션
 */
export const downloadCSV = (
  headers: string[],
  data: string[][],
  options: CSVExportOptions = {}
): void => {
  const {
    filename = "data",
    includeBOM = true,
    includeDate = true,
    delimiter = ",",
  } = options;

  try {
    // CSV 내용 생성
    const csvContent = [headers, ...data]
      .map((row) => 
        row.map((field) => `"${String(field).replace(/"/g, '""')}"`).join(delimiter)
      )
      .join("\n");

    // UTF-8 BOM 추가 (한글 깨짐 방지)
    const BOM = "\uFEFF";
    const csvWithBOM = includeBOM ? BOM + csvContent : csvContent;

    // Blob 생성
    const blob = new Blob([csvWithBOM], {
      type: "text/csv;charset=utf-8;",
    });

    // 파일명 생성
    const finalFilename = includeDate 
      ? `${filename}_${getFileNameDate()}.csv`
      : `${filename}.csv`;

    // 다운로드 실행
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", finalFilename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    console.log(`CSV 다운로드 완료: ${finalFilename} (${data.length}건의 기록)`);
  } catch (error) {
    console.error("CSV 다운로드 오류:", error);
    throw new Error("CSV 다운로드 중 오류가 발생했습니다.");
  }
};

/**
 * 방문자 데이터 CSV 내보내기
 * @param visitors 방문자 데이터 배열
 * @param options 내보내기 옵션
 */
export const exportVisitorsCSV = (
  visitors: Array<{
    farms?: { farm_name?: string; farm_type?: string };
    visit_datetime?: string;
    entry_datetime?: string;
    visitor_name?: string;
    full_name?: string;
    visitor_phone?: string;
    phone_number?: string;
    visitor_address?: string;
    address?: string;
    vehicle_number?: string;
    car_plate_number?: string;
    visitor_purpose?: string;
    visit_purpose?: string;
    disinfection_check?: boolean;
    notes?: string;
  }>,
  options: CSVExportOptions & { includeAllFarms?: boolean } = {}
): void => {
  const { includeAllFarms = false, ...csvOptions } = options;

  // CSV 헤더 설정
  const headers = includeAllFarms
    ? [
        "농장명",
        "농장유형", 
        "방문일시",
        "성명",
        "연락처",
        "주소",
        "차량번호",
        "방문목적",
        "소독여부",
        "비고",
      ]
    : [
        "방문일시",
        "성명",
        "연락처",
        "주소",
        "차량번호",
        "방문목적",
        "소독여부",
        "비고",
      ];

  // CSV 데이터 생성
  const csvData = visitors.map((visitor) => {
    const baseData = [
      new Date(visitor.visit_datetime || visitor.entry_datetime || "").toLocaleString("ko-KR"),
      visitor.visitor_name || visitor.full_name || "",
      visitor.visitor_phone || visitor.phone_number || "",
      visitor.visitor_address || visitor.address || "",
      visitor.vehicle_number || visitor.car_plate_number || "",
      visitor.visitor_purpose || visitor.visit_purpose || "",
      visitor.disinfection_check ? "완료" : "미완료",
      visitor.notes || "",
    ];

    if (includeAllFarms) {
      return [
        visitor.farms?.farm_name || "알 수 없음",
        visitor.farms?.farm_type || "알 수 없음",
        ...baseData,
      ];
    }

    return baseData;
  });

  downloadCSV(headers, csvData, {
    filename: "방문자기록",
    ...csvOptions,
  });
};

/**
 * 농장 데이터 CSV 내보내기
 * @param farms 농장 데이터 배열
 * @param options 내보내기 옵션
 */
export const exportFarmsCSV = (
  farms: Array<{
    farm_name?: string;
    description?: string;
    is_active?: boolean;
    farm_address?: string;
    farm_type?: string;
    owner_name?: string;
    owner_phone?: string;
    owner_email?: string;
    created_at?: string;
    updated_at?: string;
  }>,
  options: CSVExportOptions & {
    includeBasic?: boolean;
    includeLocation?: boolean;
    includeOwner?: boolean;
    includeStats?: boolean;
  } = {}
): void => {
  const {
    includeBasic = true,
    includeLocation = true,
    includeOwner = true,
    includeStats = false,
    ...csvOptions
  } = options;

  // 동적 헤더 생성
  const headers: string[] = [];
  if (includeBasic) {
    headers.push("농장명", "설명", "상태");
  }
  if (includeLocation) {
    headers.push("주소", "지역", "농장유형");
  }
  if (includeOwner) {
    headers.push("소유자명", "연락처", "이메일");
  }
  if (includeStats) {
    headers.push("등록일", "수정일", "방문자수");
  }

  // CSV 데이터 생성
  const csvData = farms.map((farm) => {
    const row: string[] = [];

    if (includeBasic) {
      row.push(
        farm.farm_name || "농장명 없음",
        farm.description || "설명 없음",
        farm.is_active ? "활성" : "비활성"
      );
    }

    if (includeLocation) {
      const addressParts = farm.farm_address ? farm.farm_address.split(" ") : [];
      row.push(
        farm.farm_address || "주소 없음",
        addressParts[0] || "지역 정보 없음",
        farm.farm_type || "농장 유형 없음"
      );
    }

    if (includeOwner) {
      row.push(
        farm.owner_name || "소유자 정보 없음",
        farm.owner_phone || "연락처 없음",
        farm.owner_email || "이메일 없음"
      );
    }

    if (includeStats) {
      row.push(
        farm.created_at ? new Date(farm.created_at).toLocaleString("ko-KR") : "등록일 없음",
        farm.updated_at ? new Date(farm.updated_at).toLocaleString("ko-KR") : "업데이트 없음",
        "0" // 실제로는 방문자 테이블에서 조회해야 함
      );
    }

    return row;
  });

  downloadCSV(headers, csvData, {
    filename: "농장목록",
    ...csvOptions,
  });
};

/**
 * 사용자 데이터 CSV 내보내기
 * @param users 사용자 데이터 배열
 * @param options 내보내기 옵션
 */
export const exportUsersCSV = (
  users: Array<{
    name?: string;
    email?: string;
    phone?: string;
    account_type?: string;
    is_active?: boolean;
    last_login_at?: string;
    created_at?: string;
  }>,
  options: CSVExportOptions & {
    includeBasic?: boolean;
    includeActivity?: boolean;
    includeContact?: boolean;
  } = {}
): void => {
  const {
    includeBasic = true,
    includeActivity = true,
    includeContact = false,
    ...csvOptions
  } = options;

  // 동적 헤더 생성
  const headers: string[] = [];
  if (includeBasic) {
    headers.push("이름", "이메일", "계정유형", "상태");
  }
  if (includeActivity) {
    headers.push("마지막로그인", "가입일");
  }
  if (includeContact) {
    headers.push("연락처");
  }

  // CSV 데이터 생성
  const csvData = users.map((user) => {
    const row: string[] = [];

    if (includeBasic) {
      row.push(
        user.name || "이름 없음",
        user.email || "이메일 없음",
        user.account_type === "admin" ? "관리자" : "일반사용자",
        user.is_active ? "활성" : "비활성"
      );
    }

    if (includeActivity) {
      row.push(
        user.last_login_at ? new Date(user.last_login_at).toLocaleString("ko-KR") : "로그인 기록 없음",
        user.created_at ? new Date(user.created_at).toLocaleString("ko-KR") : "가입일 없음"
      );
    }

    if (includeContact) {
      row.push(user.phone || "연락처 없음");
    }

    return row;
  });

  downloadCSV(headers, csvData, {
    filename: "사용자목록",
    ...csvOptions,
  });
};

/**
 * 시스템 로그 CSV 내보내기
 * @param logs 시스템 로그 데이터 배열
 * @param options 내보내기 옵션
 */
export const exportSystemLogsCSV = (
  logs: Array<{
    created_at?: string;
    user_name?: string;
    action?: string;
    details?: string;
    level?: string;
    ip_address?: string;
    user_agent?: string;
  }>,
  options: CSVExportOptions & {
    includeBasic?: boolean;
    includeDetails?: boolean;
    includeUser?: boolean;
    includeSystem?: boolean;
  } = {}
): void => {
  const {
    includeBasic = true,
    includeDetails = true,
    includeUser = true,
    includeSystem = false,
    ...csvOptions
  } = options;

  // 동적 헤더 생성
  const headers: string[] = [];
  if (includeBasic) {
    headers.push("일시", "액션", "레벨");
  }
  if (includeDetails) {
    headers.push("상세내용");
  }
  if (includeUser) {
    headers.push("사용자");
  }
  if (includeSystem) {
    headers.push("IP주소", "사용자에이전트");
  }

  // CSV 데이터 생성
  const csvData = logs.map((log) => {
    const row: string[] = [];

    if (includeBasic) {
      row.push(
        log.created_at ? new Date(log.created_at).toLocaleString("ko-KR") : "시간 없음",
        log.action || "액션 없음",
        log.level || "info"
      );
    }

    if (includeDetails) {
      row.push(log.details || "상세 정보 없음");
    }

    if (includeUser) {
      row.push(log.user_name || "사용자 정보 없음");
    }

    if (includeSystem) {
      row.push(
        log.ip_address || "IP 정보 없음",
        log.user_agent || "에이전트 정보 없음"
      );
    }

    return row;
  });

  downloadCSV(headers, csvData, {
    filename: "시스템로그",
    ...csvOptions,
  });
};
