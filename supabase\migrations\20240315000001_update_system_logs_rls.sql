-- Drop existing policies if any
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON "public"."system_logs";
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON "public"."system_logs";

-- Enable RLS
ALTER TABLE "public"."system_logs" ENABLE ROW LEVEL SECURITY;

-- <PERSON>reate read policy
CREATE POLICY "Enable read access for authenticated users"
ON "public"."system_logs"
FOR SELECT
TO authenticated
USING (
  auth.uid() = user_id OR 
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.account_type = 'admin'
  )
);

-- <PERSON>reate insert policy
CREATE POLICY "Enable insert for authenticated users"
ON "public"."system_logs"
FOR INSERT
TO authenticated
WITH CHECK (
  -- Allow users to create logs for themselves
  auth.uid() = user_id OR
  -- Allow users to create logs when user_id is null (system logs)
  user_id IS NULL OR
  -- Allow admins to create logs for any user
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.account_type = 'admin'
  )
);

-- Grant permissions
GRANT SELECT, INSERT ON "public"."system_logs" TO authenticated; 