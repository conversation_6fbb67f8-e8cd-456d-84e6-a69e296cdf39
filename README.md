# 농장 방문자 관리 시스템 (Farm Visitor Management System)

## 프로젝트 개요

이 프로젝트는 농장의 방문자 관리를 위한 웹 기반 시스템입니다. 방문자 등록, 관리, 통계 등의 기능을 제공합니다.

## 기술 스택

- **프레임워크**: Next.js 14
- **언어**: TypeScript
- **스타일링**: Tailwind CSS
- **UI 컴포넌트**: shadcn/ui
- **애니메이션**: Framer Motion
- **상태 관리**: React Hooks

## 주요 기능

### 1. 회원 관리

- 회원가입
  - 이메일 기반 회원가입
  - 비밀번호 복잡성 검증
  - 전화번호 형식 검증
- 로그인/로그아웃
- 프로필 관리

### 2. 방문자 관리

- 방문자 등록
- 방문 일정 관리
- 방문 기록 조회

### 3. 통계 및 보고서

- 방문자 통계
- 기간별 보고서

## 프로젝트 구조

```
app/
├── components/     # 재사용 가능한 컴포넌트
├── lib/           # 유틸리티 함수 및 설정
├── register/      # 회원가입 페이지
├── login/         # 로그인 페이지
└── dashboard/     # 대시보드 페이지
```

## 시작하기

### 필수 조건

- Node.js 18.0.0 이상
- npm 또는 yarn

### 설치

```bash
# 저장소 클론
git clone [repository-url]

# 의존성 설치
npm install
# 또는
yarn install

# 개발 서버 실행
npm run dev
# 또는
yarn dev
```

### 환경 설정

`.env.local` 파일을 생성하고 다음 환경 변수를 설정하세요:

```env
NEXT_PUBLIC_API_URL=your_api_url
```

## 개발 가이드라인

### 코드 스타일

- TypeScript strict 모드 사용
- ESLint와 Prettier를 통한 코드 포맷팅
- 컴포넌트는 함수형 컴포넌트 사용

### 커밋 메시지 규칙

- feat: 새로운 기능
- fix: 버그 수정
- docs: 문서 수정
- style: 코드 포맷팅
- refactor: 코드 리팩토링
- test: 테스트 코드
- chore: 빌드 업무 수정

## API 문서

API 문서는 [API.md](./docs/API.md)에서 확인할 수 있습니다.

## 라이선스

이 프로젝트는 MIT 라이선스를 따릅니다.
