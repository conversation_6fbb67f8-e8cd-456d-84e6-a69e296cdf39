import {
  Mountain,
  PiggyBank,
  Bird,
  Rabbit,
  Milk,
  Wheat,
  TreePine,
  Fish,
  Flower2,
  Apple,
  Grape,
  Carrot,
  Sprout,
  HelpCircle,
} from "lucide-react";

// 농장 유형 정의
export const FARM_TYPES = {
  cattle: "cattle",
  pigs: "pigs",
  poultry: "poultry",
  sheep: "sheep",
  goats: "goats",
  dairy: "dairy",
  agriculture: "agriculture",
  aquaculture: "aquaculture",
  horticulture: "horticulture",
  fruit: "fruit",
  vegetable: "vegetable",
  grain: "grain",
  forestry: "forestry",
  mixed: "mixed",
  other: "other",
} as const;

export type FarmType = keyof typeof FARM_TYPES;

// 농장 유형별 한국어 라벨
export const FARM_TYPE_LABELS: Record<FarmType, string> = {
  cattle: "소 농장 (한우/육우)",
  pigs: "돼지 농장",
  poultry: "가금류 농장 (닭/오리)",
  sheep: "양 농장",
  goats: "염소 농장",
  dairy: "낙농업 (젖소)",
  agriculture: "일반 농업",
  aquaculture: "수산업/양식업",
  horticulture: "원예업",
  fruit: "과수원",
  vegetable: "채소 농장",
  grain: "곡물 농장",
  forestry: "임업",
  mixed: "복합 농장",
  other: "기타",
};

// 농장 유형별 아이콘
export const FARM_TYPE_ICONS: Record<FarmType, any> = {
  cattle: Mountain, // 소 농장 (목장을 산으로 표현)
  pigs: PiggyBank, // 돼지 농장
  poultry: Bird, // 가금류 농장
  sheep: Rabbit, // 양 농장
  goats: Rabbit, // 염소 농장
  dairy: Milk, // 낙농업
  agriculture: Wheat, // 일반 농업
  aquaculture: Fish, // 수산업
  horticulture: Flower2, // 원예업
  fruit: Apple, // 과수원
  vegetable: Carrot, // 채소 농장
  grain: Sprout, // 곡물 농장
  forestry: TreePine, // 임업
  mixed: Grape, // 복합 농장
  other: HelpCircle, // 기타
};

// 농장 유형별 색상 (배지용)
export const FARM_TYPE_COLORS: Record<FarmType, string> = {
  cattle: "bg-red-100 text-red-800 border-red-200",
  pigs: "bg-pink-100 text-pink-800 border-pink-200",
  poultry: "bg-yellow-100 text-yellow-800 border-yellow-200",
  sheep: "bg-gray-100 text-gray-800 border-gray-200",
  goats: "bg-orange-100 text-orange-800 border-orange-200",
  dairy: "bg-blue-100 text-blue-800 border-blue-200",
  agriculture: "bg-green-100 text-green-800 border-green-200",
  aquaculture: "bg-cyan-100 text-cyan-800 border-cyan-200",
  horticulture: "bg-purple-100 text-purple-800 border-purple-200",
  fruit: "bg-red-100 text-red-800 border-red-200",
  vegetable: "bg-green-100 text-green-800 border-green-200",
  grain: "bg-amber-100 text-amber-800 border-amber-200",
  forestry: "bg-emerald-100 text-emerald-800 border-emerald-200",
  mixed: "bg-indigo-100 text-indigo-800 border-indigo-200",
  other: "bg-slate-100 text-slate-800 border-slate-200",
};

// 농장 유형 배열 (Select 옵션용)
export const FARM_TYPE_OPTIONS = Object.entries(FARM_TYPE_LABELS).map(
  ([value, label]) => ({
    value: value as FarmType,
    label,
    icon: FARM_TYPE_ICONS[value as FarmType],
    color: FARM_TYPE_COLORS[value as FarmType],
  })
);

// 농장 유형 라벨 가져오기 함수
export const getFarmTypeLabel = (type: string | undefined): string => {
  if (!type) return "기타";
  return FARM_TYPE_LABELS[type as FarmType] || "기타";
};

// 농장 유형 아이콘 가져오기 함수
export const getFarmTypeIcon = (type: string | undefined) => {
  if (!type) return FARM_TYPE_ICONS.other;
  return FARM_TYPE_ICONS[type as FarmType] || FARM_TYPE_ICONS.other;
};

// 농장 유형 색상 가져오기 함수
export const getFarmTypeColor = (type: string | undefined): string => {
  if (!type) return FARM_TYPE_COLORS.other;
  return FARM_TYPE_COLORS[type as FarmType] || FARM_TYPE_COLORS.other;
};
