-- 데이터베이스 완전 재구축 스크립트
-- 2024-12-14: 개선된 Role 시스템 적용
-- 2024-12-14: RLS 무한 재귀 문제 해결 (순환 참조 제거)
-- 2025-06-14: Supabase Storage 버킷 및 정책 설정 추가 (12단계)

-- ============================================
-- 1단계: 기존 테이블 및 관련 객체 삭제
-- ============================================

-- 트리거 삭제
DROP TRIGGER IF EXISTS trigger_update_roles_after_farm_delete ON public.farms;
DROP TRIGGER IF EXISTS trigger_update_role_after_member_change ON public.farm_members;
DROP TRIGGER IF EXISTS trigger_update_role_after_farm_create ON public.farms;
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- 함수 삭제
DROP FUNCTION IF EXISTS public.update_roles_after_farm_delete();
DROP FUNCTION IF EXISTS public.update_role_after_member_change();
DROP FUNCTION IF EXISTS public.update_role_after_farm_create();
DROP FUNCTION IF EXISTS public.recalculate_user_roles();
DROP FUNCTION IF EXISTS public.calculate_user_role(UUID);
DROP FUNCTION IF EXISTS public.handle_new_user();
DROP FUNCTION IF EXISTS public.update_login_stats(UUID);
DROP FUNCTION IF EXISTS public.update_password_changed(UUID);
DROP FUNCTION IF EXISTS public.check_email_exists(text);

-- 뷰 삭제
DROP VIEW IF EXISTS user_farm_permissions;
DROP VIEW IF EXISTS role_statistics;

-- RLS 정책 삭제
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can view accessible farms" ON public.farms;
DROP POLICY IF EXISTS "Users can manage own farms" ON public.farms;
DROP POLICY IF EXISTS "Admins can manage all farms" ON public.farms;
DROP POLICY IF EXISTS "Users can view farm members" ON public.farm_members;
DROP POLICY IF EXISTS "Farm owners can manage members" ON public.farm_members;
DROP POLICY IF EXISTS "Users can view accessible visitors" ON public.visitor_entries;
DROP POLICY IF EXISTS "Users can manage farm visitors" ON public.visitor_entries;
DROP POLICY IF EXISTS "Admins can view all visitors" ON public.visitor_entries;
DROP POLICY IF EXISTS "Users can view own logs" ON public.system_logs;
DROP POLICY IF EXISTS "Admins can view all logs" ON public.system_logs;

-- 테이블 삭제 (의존성 순서대로)
DROP TABLE IF EXISTS public.system_logs CASCADE;
DROP TABLE IF EXISTS public.visitor_entries CASCADE;
DROP TABLE IF EXISTS public.farm_members CASCADE;
DROP TABLE IF EXISTS public.farms CASCADE;
DROP TABLE IF EXISTS public.profiles CASCADE;

-- ============================================
-- 2.5단계: 유틸리티 함수 생성
-- ============================================

-- 이메일 중복 확인 함수
CREATE OR REPLACE FUNCTION public.check_email_exists(p_email text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_count integer;
BEGIN
  -- auth.users 테이블에서 이메일 확인 (대소문자 구분 없이)
  SELECT count(*) INTO user_count
  FROM auth.users
  WHERE email ILIKE p_email;
  
  -- 결과 반환 (1개 이상이면 true, 아니면 false)
  RETURN user_count > 0;
END;
$$;

-- ============================================
-- 3단계: 새로운 테이블 생성
-- ============================================

-- 2.1 사용자 프로필 테이블 (개선된 구조)
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL DEFAULT '',
    phone TEXT,
    
    -- 시스템 레벨 권한 (단순화)
    account_type TEXT NOT NULL DEFAULT 'user' CHECK (account_type IN ('admin', 'user')),
    
    -- 회사/개인 정보
    company_name TEXT,
    company_address TEXT,
    business_type TEXT,
    company_description TEXT,
    establishment_date DATE,
    employee_count INTEGER,
    company_website TEXT,
    
    -- 개인 정보
    position TEXT,
    department TEXT,
    bio TEXT,
    profile_image_url TEXT,
    
    -- 시스템 정보
    last_login_at TIMESTAMP WITH TIME ZONE,
    password_changed_at TIMESTAMP WITH TIME ZONE,
    login_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    
    -- 타임스탬프
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2.2 농장 테이블
CREATE TABLE public.farms (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    farm_name TEXT NOT NULL,
    description TEXT,
    
    -- 주소 정보
    farm_address TEXT NOT NULL,
    farm_detailed_address TEXT,
    
    -- 농장 정보
    farm_type TEXT, -- 농장 유형 (축산, 농업 등)
    
    -- 소유자 정보
    owner_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    
    -- 연락처 정보
    manager_phone TEXT,
    manager_name TEXT,
    
    -- 상태 정보
    is_active BOOLEAN DEFAULT true,
    
    -- 타임스탬프
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2.3 농장 구성원 테이블 (농장별 권한 관리)
CREATE TABLE public.farm_members (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    farm_id UUID NOT NULL REFERENCES public.farms(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,

    -- 농장 내 역할 (농장별 권한)
    role TEXT NOT NULL DEFAULT 'viewer' CHECK (role IN ('owner', 'manager', 'viewer')),

    -- 구성원 정보
    position TEXT, -- 직책
    responsibilities TEXT, -- 담당 업무

    -- 상태 정보
    is_active BOOLEAN DEFAULT true,

    -- 타임스탬프
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- 유니크 제약조건 (한 사용자는 한 농장에 한 번만 속할 수 있음)
    UNIQUE(farm_id, user_id)
);

-- 2.4 방문자 기록 테이블
CREATE TABLE public.visitor_entries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    farm_id UUID NOT NULL REFERENCES public.farms(id) ON DELETE CASCADE,
    
    -- 방문일시
    visit_datetime TIMESTAMP WITH TIME ZONE NOT NULL,

    -- 방문자 정보
    visitor_name TEXT NOT NULL,
    visitor_phone TEXT NOT NULL,
    visitor_address TEXT NOT NULL,
    visitor_purpose TEXT,
    
    -- 소독 여부    
    disinfection_check BOOLEAN DEFAULT false,

    -- 차량 정보
    vehicle_number TEXT,
    
    -- 추가 정보
    notes TEXT,
    
    -- 등록자 정보
    registered_by UUID REFERENCES public.profiles(id),

    -- 세션 토큰
    session_token TEXT NOT NULL DEFAULT gen_random_uuid()::text,

    -- 개인정보 동의
    consent_given BOOLEAN DEFAULT false,

    -- 타임스탬프
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2.5 시스템 로그 테이블
CREATE TABLE public.system_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- 로그 기본 정보
    level TEXT NOT NULL DEFAULT 'info' CHECK (level IN ('error', 'warn', 'info', 'debug')),
    action TEXT NOT NULL,
    message TEXT NOT NULL,
    
    -- 사용자 정보
    user_id UUID REFERENCES public.profiles(id),
    user_email TEXT,
    user_ip TEXT,
    user_agent TEXT,
     
    -- 관련 리소스 정보
    resource_type TEXT, -- 'farm', 'user', 'visitor', 'system'
    resource_id UUID,
    
    -- 추가 데이터 (JSON)
    metadata JSONB,
    
    -- 타임스탬프
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================
-- 3단계: 인덱스 생성
-- ============================================

-- profiles 테이블 인덱스
CREATE INDEX idx_profiles_email ON public.profiles(email);
CREATE INDEX idx_profiles_account_type ON public.profiles(account_type);
CREATE INDEX idx_profiles_created_at ON public.profiles(created_at);

-- farms 테이블 인덱스
CREATE INDEX idx_farms_owner_id ON public.farms(owner_id);
CREATE INDEX idx_farms_created_at ON public.farms(created_at);
CREATE INDEX idx_farms_is_active ON public.farms(is_active);

-- farm_members 테이블 인덱스
CREATE INDEX idx_farm_members_farm_id ON public.farm_members(farm_id);
CREATE INDEX idx_farm_members_user_id ON public.farm_members(user_id);
CREATE INDEX idx_farm_members_role ON public.farm_members(role);

-- visitor_entries 테이블 인덱스
CREATE INDEX idx_visitor_entries_farm_id ON public.visitor_entries(farm_id);
CREATE INDEX idx_visitor_entries_visit_datetime ON public.visitor_entries(visit_datetime);
CREATE INDEX idx_visitor_entries_visitor_phone ON public.visitor_entries(visitor_phone);
CREATE INDEX idx_visitor_entries_created_at ON public.visitor_entries(created_at);

-- system_logs 테이블 인덱스
CREATE INDEX idx_system_logs_user_id ON public.system_logs(user_id);
CREATE INDEX idx_system_logs_level ON public.system_logs(level);
CREATE INDEX idx_system_logs_action ON public.system_logs(action);
CREATE INDEX idx_system_logs_resource_type ON public.system_logs(resource_type);
CREATE INDEX idx_system_logs_created_at ON public.system_logs(created_at);

-- ============================================
-- 4단계: RLS (Row Level Security) 설정
-- ============================================

-- RLS 활성화
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.farms ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.farm_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.visitor_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_logs ENABLE ROW LEVEL SECURITY;

-- ============================================
-- 5단계: 권한 확인 함수들
-- ============================================

-- 5.1 시스템 관리자 확인 함수
CREATE OR REPLACE FUNCTION public.is_system_admin(input_user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.profiles
        WHERE id = input_user_id AND account_type = 'admin'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5.2 농장별 권한 확인 함수
CREATE OR REPLACE FUNCTION public.get_farm_role(input_user_id UUID, input_farm_id UUID)
RETURNS TEXT AS $$
DECLARE
    farm_role TEXT;
BEGIN
    -- 농장 소유자인지 확인
    SELECT 'owner' INTO farm_role
    FROM public.farms
    WHERE id = input_farm_id AND owner_id = input_user_id;

    IF farm_role IS NOT NULL THEN
        RETURN farm_role;
    END IF;

    -- 농장 구성원인지 확인
    SELECT role INTO farm_role
    FROM public.farm_members
    WHERE farm_id = input_farm_id AND user_id = input_user_id AND is_active = true;

    RETURN COALESCE(farm_role, 'none');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5.3 농장 관리 권한 확인 함수
CREATE OR REPLACE FUNCTION public.can_manage_farm(input_user_id UUID, input_farm_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    farm_role TEXT;
BEGIN
    -- 시스템 관리자는 모든 농장 관리 가능
    IF public.is_system_admin(input_user_id) THEN
        RETURN true;
    END IF;

    -- 농장별 권한 확인
    SELECT public.get_farm_role(input_user_id, input_farm_id) INTO farm_role;

    RETURN farm_role IN ('owner', 'manager');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5.4 농장 조회 권한 확인 함수
CREATE OR REPLACE FUNCTION public.can_view_farm(input_user_id UUID, input_farm_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    farm_role TEXT;
BEGIN
    -- 시스템 관리자는 모든 농장 조회 가능
    IF public.is_system_admin(input_user_id) THEN
        RETURN true;
    END IF;

    -- 농장별 권한 확인
    SELECT public.get_farm_role(input_user_id, input_farm_id) INTO farm_role;

    RETURN farm_role IN ('owner', 'manager', 'viewer');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5.5 사용자가 접근 가능한 농장 목록 함수
CREATE OR REPLACE FUNCTION public.get_user_accessible_farms(input_user_id UUID)
RETURNS TABLE(
    farm_id UUID,
    farm_name TEXT,
    farm_role TEXT,
    can_manage BOOLEAN,
    can_view BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        f.id as farm_id,
        f.farm_name,
        CASE
            WHEN f.owner_id = input_user_id THEN 'owner'
            WHEN fm.role IS NOT NULL THEN fm.role
            ELSE 'none'
        END as farm_role,
        CASE
            WHEN public.is_system_admin(input_user_id) THEN true
            WHEN f.owner_id = input_user_id THEN true
            WHEN fm.role = 'manager' THEN true
            ELSE false
        END as can_manage,
        CASE
            WHEN public.is_system_admin(input_user_id) THEN true
            WHEN f.owner_id = input_user_id THEN true
            WHEN fm.role IN ('manager', 'viewer') THEN true
            ELSE false
        END as can_view
    FROM public.farms f
    LEFT JOIN public.farm_members fm ON fm.farm_id = f.id AND fm.user_id = input_user_id AND fm.is_active = true
    WHERE
        f.is_active = true AND (
            public.is_system_admin(input_user_id) OR  -- 시스템 관리자는 모든 농장
            f.owner_id = input_user_id OR             -- 소유한 농장
            fm.user_id = input_user_id                -- 구성원인 농장
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5.6 사용자 상태 확인 함수
CREATE OR REPLACE FUNCTION public.get_user_status(input_user_id UUID)
RETURNS TABLE(
    user_id UUID,
    account_type TEXT,
    has_farms BOOLEAN,
    owned_farms_count INTEGER,
    member_farms_count INTEGER,
    user_category TEXT
) AS $$
DECLARE
    owned_count INTEGER;
    member_count INTEGER;
    acc_type TEXT;
BEGIN
    -- 사용자의 account_type 조회
    SELECT p.account_type INTO acc_type
    FROM public.profiles p
    WHERE p.id = input_user_id;

    -- 소유한 농장 수 조회
    SELECT COUNT(*) INTO owned_count
    FROM public.farms f
    WHERE f.owner_id = input_user_id AND f.is_active = true;

    -- 구성원으로 속한 농장 수 조회
    SELECT COUNT(*) INTO member_count
    FROM public.farm_members fm
    WHERE fm.user_id = input_user_id AND fm.is_active = true;

    RETURN QUERY
    SELECT
        input_user_id as user_id,
        COALESCE(acc_type, 'user') as account_type,
        (owned_count > 0 OR member_count > 0) as has_farms,
        owned_count as owned_farms_count,
        member_count as member_farms_count,
        CASE
            WHEN acc_type = 'admin' THEN 'system_admin'
            WHEN owned_count > 0 THEN 'farm_owner'
            WHEN member_count > 0 THEN 'farm_member'
            ELSE 'general_user'
        END as user_category;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================
-- 6단계: RLS 정책 생성 (무한 재귀 방지)
-- ============================================
--
-- 주요 개선사항:
-- 1. farms ↔ farm_members 순환 참조 제거
-- 2. EXISTS 대신 IN 절 사용으로 안전성 향상
-- 3. SECURITY DEFINER 함수로 관리자 권한 확인 최적화
--

-- 6.1 profiles 테이블 정책
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles" ON public.profiles
    FOR ALL USING (public.is_system_admin(auth.uid()));

-- 6.2 farms 테이블 정책 (순환 참조 제거)
CREATE POLICY "Users can view own farms" ON public.farms
    FOR SELECT USING (
        public.is_system_admin(auth.uid()) OR
        owner_id = auth.uid()
    );

CREATE POLICY "Users can manage own farms" ON public.farms
    FOR ALL USING (
        public.is_system_admin(auth.uid()) OR
        owner_id = auth.uid()
    );

-- 6.3 farm_members 테이블 정책 (farms 참조 최소화)
CREATE POLICY "Users can view farm members" ON public.farm_members
    FOR SELECT USING (
        public.is_system_admin(auth.uid()) OR
        user_id = auth.uid() OR
        farm_id IN (
            SELECT id FROM public.farms WHERE owner_id = auth.uid()
        )
    );

CREATE POLICY "Farm owners can manage members" ON public.farm_members
    FOR ALL USING (
        public.is_system_admin(auth.uid()) OR
        farm_id IN (
            SELECT id FROM public.farms WHERE owner_id = auth.uid()
        )
    );

-- 6.4 visitor_entries 테이블 정책 (단순화)
CREATE POLICY "Users can view farm visitors" ON public.visitor_entries
    FOR SELECT USING (
        public.is_system_admin(auth.uid()) OR
        farm_id IN (
            SELECT id FROM public.farms WHERE owner_id = auth.uid()
        ) OR
        farm_id IN (
            SELECT farm_id FROM public.farm_members
            WHERE user_id = auth.uid() AND is_active = true
        )
    );

CREATE POLICY "Users can manage farm visitors" ON public.visitor_entries
    FOR ALL USING (
        public.is_system_admin(auth.uid()) OR
        farm_id IN (
            SELECT id FROM public.farms WHERE owner_id = auth.uid()
        ) OR
        farm_id IN (
            SELECT farm_id FROM public.farm_members
            WHERE user_id = auth.uid() AND is_active = true AND role = 'manager'
        )
    );

-- 6.5 방문자 등록 정책 (공개 접근)
CREATE POLICY "Anyone can register visitors" ON public.visitor_entries
    FOR INSERT WITH CHECK (true);

-- 6.6 system_logs 테이블 정책 (UUID 타입 준수)
-- 1. 사용자는 자신의 로그만 조회 가능
CREATE POLICY "Users can view own logs" ON public.system_logs
    FOR SELECT USING (
        user_id = auth.uid()
    );

-- 2. 관리자는 모든 로그 조회 가능
CREATE POLICY "Admins can view all logs" ON public.system_logs
    FOR SELECT USING (
        public.is_system_admin(auth.uid())
    );

-- 3. 시스템 로그는 항상 생성 허용 (로그인, 시스템 이벤트 등)
CREATE POLICY "Allow all system log creation" ON public.system_logs
    FOR INSERT WITH CHECK (true);

-- 4. 관리자는 모든 로그 작업 가능
CREATE POLICY "Admins can manage all logs" ON public.system_logs
    FOR ALL USING (
        public.is_system_admin(auth.uid())
    );

-- ============================================
-- 7단계: 자동화 트리거 및 함수
-- ============================================

-- 7.1 새 사용자 프로필 생성 함수
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (
        id,
        email,
        name,
        phone,
        account_type
    )
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'name', ''),
        COALESCE(NEW.raw_user_meta_data->>'phone', ''),
        'user' -- 기본값: 일반 사용자
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7.2 새 사용자 트리거 생성
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 7.3 updated_at 자동 업데이트 함수
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 7.4 updated_at 트리거들
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_farms_updated_at
    BEFORE UPDATE ON public.farms
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_farm_members_updated_at
    BEFORE UPDATE ON public.farm_members
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_visitor_entries_updated_at
    BEFORE UPDATE ON public.visitor_entries
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- 7.5 시스템 로그 자동 생성 함수
CREATE OR REPLACE FUNCTION public.log_farm_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO public.system_logs (
            level, action, message, user_id, resource_type, resource_id, metadata
        ) VALUES (
            'info', 'farm_created',
            '농장이 생성되었습니다: ' || NEW.farm_name,
            NEW.owner_id, 'farm', NEW.id,
            jsonb_build_object('farm_name', NEW.farm_name)
        );
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.system_logs (
            level, action, message, user_id, resource_type, resource_id, metadata
        ) VALUES (
            'info', 'farm_updated',
            '농장 정보가 수정되었습니다: ' || NEW.farm_name,
            auth.uid(), 'farm', NEW.id,
            jsonb_build_object('farm_name', NEW.farm_name)
        );
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO public.system_logs (
            level, action, message, user_id, resource_type, resource_id, metadata
        ) VALUES (
            'info', 'farm_deleted',
            '농장이 삭제되었습니다: ' || OLD.farm_name,
            auth.uid(), 'farm', OLD.id,
            jsonb_build_object('farm_name', OLD.farm_name)
        );
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7.6 농장 변경 로그 트리거
CREATE TRIGGER log_farm_changes_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.farms
    FOR EACH ROW EXECUTE FUNCTION public.log_farm_changes();

-- 7.7 로그인 시간 업데이트 함수 (기존 호환성)
CREATE OR REPLACE FUNCTION public.update_login_stats(input_user_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.profiles
    SET
        last_login_at = NOW(),
        updated_at = NOW()
    WHERE id = input_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7.8 비밀번호 변경 시간 업데이트 함수 (기존 호환성)
CREATE OR REPLACE FUNCTION public.update_password_changed(input_user_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.profiles
    SET
        password_changed_at = NOW(),
        updated_at = NOW()
    WHERE id = input_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================
-- 8단계: 유용한 뷰 생성
-- ============================================

-- 8.1 사용자-농장 권한 종합 뷰
CREATE OR REPLACE VIEW user_farm_permissions AS
SELECT
    p.id as user_id,
    p.email,
    p.name,
    p.account_type,
    f.id as farm_id,
    f.farm_name,
    CASE
        WHEN f.owner_id = p.id THEN 'owner'
        WHEN fm.role IS NOT NULL THEN fm.role
        ELSE NULL
    END as farm_role,
    CASE
        WHEN f.owner_id = p.id THEN true
        WHEN fm.role = 'manager' THEN true
        ELSE false
    END as can_manage_farm,
    CASE
        WHEN f.owner_id = p.id THEN true
        WHEN fm.role IN ('manager', 'viewer') THEN true
        ELSE false
    END as can_view_farm,
    f.created_at as farm_created_at,
    fm.created_at as member_joined_at
FROM public.profiles p
LEFT JOIN public.farms f ON f.owner_id = p.id OR EXISTS (
    SELECT 1 FROM public.farm_members fm2
    WHERE fm2.user_id = p.id AND fm2.farm_id = f.id AND fm2.is_active = true
)
LEFT JOIN public.farm_members fm ON fm.user_id = p.id AND fm.farm_id = f.id AND fm.is_active = true
WHERE f.id IS NOT NULL AND f.is_active = true;

-- 8.2 권한별 사용자 통계 뷰
CREATE OR REPLACE VIEW role_statistics AS
SELECT
    'System Admin' as role_type,
    COUNT(*) as user_count,
    'admin' as role_code
FROM public.profiles
WHERE account_type = 'admin' AND is_active = true

UNION ALL

SELECT
    'Farm Owner' as role_type,
    COUNT(DISTINCT owner_id) as user_count,
    'owner' as role_code
FROM public.farms
WHERE is_active = true

UNION ALL

SELECT
    'Farm Owner (Member)' as role_type,
    COUNT(DISTINCT user_id) as user_count,
    'owner' as role_code
FROM public.farm_members
WHERE role = 'owner' AND is_active = true

UNION ALL

SELECT
    'Farm Manager' as role_type,
    COUNT(DISTINCT user_id) as user_count,
    'manager' as role_code
FROM public.farm_members
WHERE role = 'manager' AND is_active = true

UNION ALL

SELECT
    'Farm Viewer' as role_type,
    COUNT(DISTINCT user_id) as user_count,
    'viewer' as role_code
FROM public.farm_members
WHERE role = 'viewer' AND is_active = true

UNION ALL

SELECT
    'General User' as role_type,
    COUNT(*) as user_count,
    'general' as role_code
FROM public.profiles p
WHERE account_type = 'user' AND is_active = true
AND NOT EXISTS (SELECT 1 FROM public.farms WHERE owner_id = p.id AND is_active = true)
AND NOT EXISTS (SELECT 1 FROM public.farm_members WHERE user_id = p.id AND is_active = true);

-- 8.3 농장별 통계 뷰
CREATE OR REPLACE VIEW farm_statistics AS
SELECT
    f.id as farm_id,
    f.farm_name,
    f.owner_id,
    p.name as owner_name,
    COUNT(DISTINCT fm.user_id) as member_count,
    COUNT(DISTINCT CASE WHEN fm.role = 'manager' THEN fm.user_id END) as manager_count,
    COUNT(DISTINCT CASE WHEN fm.role = 'viewer' THEN fm.user_id END) as viewer_count,
    COUNT(DISTINCT ve.id) as total_visitors,
    COUNT(DISTINCT CASE WHEN ve.visit_datetime >= CURRENT_DATE - INTERVAL '30 days' THEN ve.id END) as recent_visitors,
    f.created_at,
    f.updated_at
FROM public.farms f
LEFT JOIN public.profiles p ON p.id = f.owner_id
LEFT JOIN public.farm_members fm ON fm.farm_id = f.id AND fm.is_active = true
LEFT JOIN public.visitor_entries ve ON ve.farm_id = f.id
WHERE f.is_active = true
GROUP BY f.id, f.farm_name, f.owner_id, p.name, f.created_at, f.updated_at;

-- ============================================
-- 9단계: 초기 데이터 및 설정
-- ============================================

-- 9.1 시스템 관리자 계정 생성 (선택사항)
-- 실제 운영 시에는 별도로 생성하거나 이 부분을 주석 처리하세요

-- 개발/테스트용 관리자 계정 (필요시 주석 해제)
/*
INSERT INTO public.profiles (
    id,
    email,
    name,
    account_type,
    phone,
    company_name
) VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    '시스템 관리자',
    'admin',
    '010-0000-0000',
    '농장 방문자 관리 시스템'
) ON CONFLICT (email) DO NOTHING;
*/

-- 운영용 관리자 계정 생성 함수
CREATE OR REPLACE FUNCTION public.create_admin_account(
    admin_email TEXT,
    admin_name TEXT,
    admin_phone TEXT DEFAULT NULL,
    admin_company TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    new_admin_id UUID;
BEGIN
    -- 이미 존재하는 이메일인지 확인
    IF EXISTS (SELECT 1 FROM public.profiles WHERE email = admin_email) THEN
        RAISE EXCEPTION '이미 존재하는 이메일입니다: %', admin_email;
    END IF;

    -- 새 관리자 계정 생성
    INSERT INTO public.profiles (
        id,
        email,
        name,
        account_type,
        phone,
        company_name,
        created_at,
        updated_at
    ) VALUES (
        gen_random_uuid(),
        admin_email,
        admin_name,
        'admin',
        admin_phone,
        admin_company,
        NOW(),
        NOW()
    ) RETURNING id INTO new_admin_id;

    -- 시스템 로그 기록
    INSERT INTO public.system_logs (
        level,
        action,
        message,
        user_id,
        resource_type,
        resource_id,
        metadata
    ) VALUES (
        'info',
        'admin_created',
        '새로운 시스템 관리자가 생성되었습니다: ' || admin_name,
        new_admin_id,
        'user',
        new_admin_id,
        jsonb_build_object(
            'email', admin_email,
            'name', admin_name,
            'account_type', 'admin'
        )
    );

    RETURN new_admin_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================
-- 10단계: 권한 부여
-- ============================================

-- 인증된 사용자에게 테이블 접근 권한 부여
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- 익명 사용자에게 제한적 권한 부여 (필요한 경우)
GRANT USAGE ON SCHEMA public TO anon;
GRANT SELECT ON public.profiles TO anon;

-- ============================================
-- 11단계: 확인 쿼리들
-- ============================================

-- 테이블 생성 확인
SELECT
    schemaname,
    tablename,
    tableowner,
    hasindexes,
    hasrules,
    hastriggers
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY tablename;

-- 함수 생성 확인
SELECT
    routine_name,
    routine_type,
    data_type
FROM information_schema.routines
WHERE routine_schema = 'public'
AND routine_name LIKE '%farm%' OR routine_name LIKE '%user%'
ORDER BY routine_name;

-- RLS 정책 확인
SELECT
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- =====================================================
-- 12단계: Supabase Storage 버킷 및 정책 설정
-- =====================================================

-- Storage 버킷 생성
-- profiles 버킷 (프로필 사진용)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'profiles',
  'profiles',
  true,
  5242880, -- 5MB
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
) ON CONFLICT (id) DO NOTHING;

-- system 버킷 (로고, 파비콘용)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'system',
  'system',
  true,
  2097152, -- 2MB
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml', 'image/x-icon', 'image/vnd.microsoft.icon']
) ON CONFLICT (id) DO NOTHING;

-- Storage 정책 설정

-- profiles 버킷 정책
-- 프로필 사진 조회 (모든 사용자)
CREATE POLICY "Profile images are publicly accessible" ON storage.objects
FOR SELECT USING (bucket_id = 'profiles');

-- 프로필 사진 업로드 (인증된 사용자만, 자신의 폴더에만)
CREATE POLICY "Users can upload their own profile image" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'profiles'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

-- 프로필 사진 업데이트 (인증된 사용자만, 자신의 파일만)
CREATE POLICY "Users can update their own profile image" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'profiles'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

-- 프로필 사진 삭제 (인증된 사용자만, 자신의 파일만)
CREATE POLICY "Users can delete their own profile image" ON storage.objects
FOR DELETE USING (
  bucket_id = 'profiles'
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

-- system 버킷 정책
-- 시스템 파일 조회 (모든 사용자)
CREATE POLICY "System files are publicly accessible" ON storage.objects
FOR SELECT USING (bucket_id = 'system');

-- 시스템 파일 업로드 (관리자만)
CREATE POLICY "Only admins can upload system files" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'system'
  AND auth.role() = 'authenticated'
  AND EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND account_type = 'admin'
  )
);

-- 시스템 파일 업데이트 (관리자만)
CREATE POLICY "Only admins can update system files" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'system'
  AND auth.role() = 'authenticated'
  AND EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND account_type = 'admin'
  )
);

-- 시스템 파일 삭제 (관리자만)
CREATE POLICY "Only admins can delete system files" ON storage.objects
FOR DELETE USING (
  bucket_id = 'system'
  AND auth.role() = 'authenticated'
  AND EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid()
    AND account_type = 'admin'
  )
);

-- =====================================================
-- 13단계: 최종 확인 쿼리 (Storage 포함)
-- =====================================================

-- Storage 버킷 확인
SELECT
  id,
  name,
  public,
  file_size_limit,
  allowed_mime_types,
  created_at
FROM storage.buckets
WHERE id IN ('profiles', 'system')
ORDER BY id;

-- Storage 정책 확인
SELECT
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies
WHERE schemaname = 'storage'
AND tablename = 'objects'
AND (policyname LIKE '%profile%' OR policyname LIKE '%system%')
ORDER BY policyname;
