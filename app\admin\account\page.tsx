"use client";

import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/components/providers/auth-provider";
import {
  User,
  Building2,
  Shield,
  Save,
  Camera,
  Monitor,
  Smartphone,
  Clock,
  CheckCircle2,
  History,
} from "lucide-react";
import { motion } from "framer-motion";
import { supabase } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";
import { AddressSearch } from "@/components/address-search";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import Link from "next/link";
import { Home } from "lucide-react";
import Image from "next/image";
import { uploadImage, deleteImage } from "@/lib/utils/storage";
import { ProfileImageUpload } from "./components/profile-image-upload";
import { memo } from "react";
import {
  validatePassword,
  validatePasswordMatch,
  getPasswordStrength,
  getPasswordStrengthLabel,
} from "@/lib/utils/password-validation";
import { cn } from "@/lib/utils";

// 프로필 정보 컴포넌트
const ProfileInfo = memo(function ProfileInfo({
  profileData,
  setProfileData,
  loading,
  onSave,
}: {
  profileData: {
    name: string;
    email: string;
    phone: string;
    position: string;
    department: string;
    bio: string;
  };
  setProfileData: React.Dispatch<React.SetStateAction<typeof profileData>>;
  loading: boolean;
  onSave: () => void;
}) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="name">이름</Label>
          <Input
            id="name"
            value={profileData.name}
            onChange={(e) =>
              setProfileData((prev) => ({
                ...prev,
                name: e.target.value,
              }))
            }
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="email">이메일</Label>
          <Input
            id="email"
            type="email"
            value={profileData.email}
            disabled
            className="bg-muted"
          />
          <p className="text-xs text-muted-foreground">
            이메일은 변경할 수 없습니다
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="phone">전화번호</Label>
          <Input
            id="phone"
            type="tel"
            value={profileData.phone}
            onChange={(e) =>
              setProfileData((prev) => ({
                ...prev,
                phone: e.target.value,
              }))
            }
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="position">직책</Label>
          <Select
            value={profileData.position}
            onValueChange={(value) =>
              setProfileData((prev) => ({ ...prev, position: value }))
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="대표">대표</SelectItem>
              <SelectItem value="관리자">관리자</SelectItem>
              <SelectItem value="직원">직원</SelectItem>
              <SelectItem value="방역담당자">방역담당자</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="bio">자기소개</Label>
        <Textarea
          id="bio"
          value={profileData.bio}
          onChange={(e) =>
            setProfileData((prev) => ({
              ...prev,
              bio: e.target.value,
            }))
          }
          placeholder="간단한 자기소개를 입력하세요"
        />
      </div>

      <div className="flex justify-end">
        <Button onClick={onSave} disabled={loading}>
          <Save className="mr-2 h-4 w-4" />
          {loading ? "저장 중..." : "프로필 저장"}
        </Button>
      </div>
    </div>
  );
});

// 회사 정보 컴포넌트
const CompanyInfo = memo(function CompanyInfo({
  companyData,
  setCompanyData,
  loading,
  onSave,
}: {
  companyData: {
    company_name: string;
    company_address: string;
    business_type: string;
    company_description: string;
    establishment_date: string;
    employee_count: number | null;
    company_website: string;
  };
  setCompanyData: React.Dispatch<React.SetStateAction<typeof companyData>>;
  loading: boolean;
  onSave: () => void;
}) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="company_name">회사명</Label>
          <Input
            id="company_name"
            value={companyData.company_name}
            onChange={(e) =>
              setCompanyData((prev) => ({
                ...prev,
                company_name: e.target.value,
              }))
            }
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="business_type">업종</Label>
          <Select
            value={companyData.business_type}
            onValueChange={(value) =>
              setCompanyData((prev) => ({
                ...prev,
                business_type: value,
              }))
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="축산업">축산업</SelectItem>
              <SelectItem value="농업">농업</SelectItem>
              <SelectItem value="원예업">원예업</SelectItem>
              <SelectItem value="수산업">수산업</SelectItem>
              <SelectItem value="기타">기타</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="company_address">회사 주소</Label>
        <div className="space-y-2">
          <AddressSearch
            onSelect={(address, detailedAddress) => {
              setCompanyData((prev) => ({
                ...prev,
                company_address: detailedAddress
                  ? `${address} ${detailedAddress}`
                  : address,
              }));
            }}
            defaultDetailedAddress=""
          />
          {companyData.company_address && (
            <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
              <div className="text-sm">
                <div className="font-medium text-gray-700">선택된 주소:</div>
                <div className="text-gray-600 mt-1">
                  {companyData.company_address}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="establishment_date">설립일</Label>
          <Input
            id="establishment_date"
            type="date"
            value={companyData.establishment_date}
            onChange={(e) =>
              setCompanyData((prev) => ({
                ...prev,
                establishment_date: e.target.value,
              }))
            }
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="employee_count">직원 수</Label>
          <Select
            value={companyData.employee_count?.toString() || ""}
            onValueChange={(value) =>
              setCompanyData((prev) => ({
                ...prev,
                employee_count: parseInt(value, 10) || null,
              }))
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="직원 수를 선택하세요" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">1-10명</SelectItem>
              <SelectItem value="50">11-50명</SelectItem>
              <SelectItem value="100">51-100명</SelectItem>
              <SelectItem value="500">101-500명</SelectItem>
              <SelectItem value="1000">500명 이상</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="company_website">웹사이트</Label>
        <Input
          id="company_website"
          type="url"
          value={companyData.company_website}
          onChange={(e) =>
            setCompanyData((prev) => ({
              ...prev,
              company_website: e.target.value,
            }))
          }
          placeholder="https://example.com"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="company_description">회사 소개</Label>
        <Textarea
          id="company_description"
          value={companyData.company_description}
          onChange={(e) =>
            setCompanyData((prev) => ({
              ...prev,
              company_description: e.target.value,
            }))
          }
          placeholder="회사 및 농장에 대한 간단한 소개를 입력하세요"
        />
      </div>

      <div className="flex justify-end">
        <Button onClick={onSave} disabled={loading}>
          <Save className="mr-2 h-4 w-4" />
          {loading ? "저장 중..." : "회사 정보 저장"}
        </Button>
      </div>
    </div>
  );
});

// 보안 설정 컴포넌트
const SecuritySettings = memo(function SecuritySettings({
  passwordData,
  setPasswordData,
  loading,
  onSave,
}: {
  passwordData: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  };
  setPasswordData: React.Dispatch<React.SetStateAction<typeof passwordData>>;
  loading: boolean;
  onSave: () => void;
}) {
  const [passwordStrength, setPasswordStrength] = useState(0);

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setPasswordData((prev) => ({ ...prev, newPassword }));
    setPasswordStrength(getPasswordStrength(newPassword));
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="currentPassword">현재 비밀번호</Label>
        <Input
          id="currentPassword"
          type="password"
          value={passwordData.currentPassword}
          onChange={(e) =>
            setPasswordData((prev) => ({
              ...prev,
              currentPassword: e.target.value,
            }))
          }
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="newPassword">새 비밀번호</Label>
        <Input
          id="newPassword"
          type="password"
          value={passwordData.newPassword}
          onChange={handlePasswordChange}
        />
        {passwordData.newPassword && (
          <div className="mt-2">
            <div className="text-sm text-muted-foreground">
              비밀번호 강도: {getPasswordStrengthLabel(passwordStrength)}
            </div>
            <div className="h-1 mt-1 bg-muted rounded-full overflow-hidden">
              <div
                className={cn("h-full transition-all duration-300", {
                  "w-1/5 bg-destructive": passwordStrength === 0,
                  "w-2/5 bg-orange-500": passwordStrength === 1,
                  "w-3/5 bg-yellow-500": passwordStrength === 2,
                  "w-4/5 bg-lime-500": passwordStrength === 3,
                  "w-full bg-green-500": passwordStrength === 4,
                })}
              />
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              8자 이상, 대소문자, 숫자, 특수문자를 포함해야 합니다.
            </p>
          </div>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="confirmPassword">새 비밀번호 확인</Label>
        <Input
          id="confirmPassword"
          type="password"
          value={passwordData.confirmPassword}
          onChange={(e) =>
            setPasswordData((prev) => ({
              ...prev,
              confirmPassword: e.target.value,
            }))
          }
        />
      </div>

      <div className="flex justify-end">
        <Button onClick={onSave} disabled={loading}>
          <Shield className="mr-2 h-4 w-4" />
          {loading ? "변경 중..." : "비밀번호 변경"}
        </Button>
      </div>
    </div>
  );
});

export default function AccountPage() {
  const { profile } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  const [profileData, setProfileData] = useState({
    name: "",
    email: "",
    phone: "",
    position: "",
    department: "",
    bio: "",
  });

  const [companyData, setCompanyData] = useState({
    company_name: "",
    company_address: "",
    business_type: "",
    company_description: "",
    establishment_date: "",
    employee_count: null as number | null,
    company_website: "",
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [loginActivity, setLoginActivity] = useState<any[]>([]);

  // 로그인 활동 데이터 로드
  const loadLoginActivity = useCallback(async () => {
    if (!profile?.id) return;

    try {
      // 최신 프로필 데이터 가져오기
      const { data: latestProfile, error } = await supabase
        .from("profiles")
        .select("last_login_at, password_changed_at")
        .eq("id", profile.id)
        .single();

      if (error) throw error;

      const currentTime = new Date();
      const lastLogin = latestProfile.last_login_at
        ? new Date(latestProfile.last_login_at)
        : new Date(currentTime.getTime() - 2 * 60 * 60 * 1000); // 2시간 전

      // 현재 브라우저 정보 감지
      const userAgent =
        typeof window !== "undefined" ? window.navigator.userAgent : "";
      let currentDevice = "Unknown Browser";
      let currentIcon = Monitor;

      if (userAgent.includes("Chrome")) {
        currentDevice = "Chrome on Windows";
        currentIcon = Monitor;
      } else if (userAgent.includes("Safari")) {
        currentDevice = "Safari on macOS";
        currentIcon = Monitor;
      } else if (
        userAgent.includes("Mobile") ||
        userAgent.includes("Android") ||
        userAgent.includes("iPhone")
      ) {
        currentDevice = userAgent.includes("iPhone")
          ? "Safari on iPhone"
          : "Chrome on Android";
        currentIcon = Smartphone;
      }

      setLoginActivity([
        {
          id: 1,
          device: currentDevice,
          location: "서울, 대한민국",
          time: "지금",
          isCurrent: true,
          icon: currentIcon,
        },
        {
          id: 2,
          device: "Safari on iPhone",
          location: "서울, 대한민국",
          time: formatTimeAgo(lastLogin),
          isCurrent: false,
          icon: Smartphone,
        },
      ]);
    } catch (error) {
      console.error("Failed to load login activity:", error);
    }
  }, [profile?.id]);

  // 시간 포맷 함수
  const formatTimeAgo = useCallback((date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60)
    );

    if (diffInMinutes < 60) {
      return `${diffInMinutes}분 전`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}시간 전`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)}일 전`;
    }
  }, []);

  // 프로필 데이터 로드
  useEffect(() => {
    if (profile) {
      setProfileData({
        name: profile.name || "",
        email: profile.email || "",
        phone: profile.phone || "",
        position: profile.position || "",
        department: profile.department || "",
        bio: profile.bio || "",
      });

      setCompanyData({
        company_name: profile.company_name || "",
        company_address: profile.company_address || "",
        business_type: profile.business_type || "",
        company_description: profile.company_description || "",
        establishment_date: profile.establishment_date || "",
        employee_count: profile.employee_count
          ? parseInt(profile.employee_count.toString(), 10)
          : null,
        company_website: profile.company_website || "",
      });

      loadLoginActivity();
    }
  }, [profile, loadLoginActivity]);

  // 프로필 저장
  const handleProfileSave = useCallback(async () => {
    if (!profile?.id) return;

    setLoading(true);
    try {
      const { error } = await supabase
        .from("profiles")
        .update({
          name: profileData.name,
          phone: profileData.phone,
          position: profileData.position,
          department: profileData.department,
          bio: profileData.bio,
        })
        .eq("id", profile.id);

      if (error) throw error;

      toast({
        title: "프로필이 저장되었습니다.",
        description: "개인 정보가 성공적으로 업데이트되었습니다.",
      });
    } catch (error) {
      console.error("Profile save error:", error);
      toast({
        title: "저장 실패",
        description: "프로필 저장 중 오류가 발생했습니다.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [profile?.id, profileData, toast]);

  // 회사 정보 저장
  const handleCompanySave = useCallback(async () => {
    if (!profile?.id) return;

    setLoading(true);
    try {
      const { error } = await supabase
        .from("profiles")
        .update({
          company_name: companyData.company_name,
          company_address: companyData.company_address,
          business_type: companyData.business_type,
          company_description: companyData.company_description,
          establishment_date: companyData.establishment_date || null,
          employee_count: companyData.employee_count,
          company_website: companyData.company_website,
        })
        .eq("id", profile.id);

      if (error) throw error;

      toast({
        title: "저장 완료",
        description: "회사 정보가 업데이트되었습니다.",
      });
    } catch (error) {
      console.error("Failed to update company info:", error);
      toast({
        title: "오류",
        description: "회사 정보 업데이트에 실패했습니다.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [profile?.id, companyData, toast]);

  // 비밀번호 변경
  const handlePasswordChange = useCallback(async () => {
    if (!passwordData.currentPassword) {
      toast({
        title: "현재 비밀번호 필요",
        description: "현재 비밀번호를 입력해주세요.",
        variant: "destructive",
      });
      return;
    }

    // 비밀번호 유효성 검사
    const passwordValidation = validatePassword(passwordData.newPassword);
    if (!passwordValidation.isValid) {
      toast({
        title: "비밀번호 오류",
        description: passwordValidation.errors[0],
        variant: "destructive",
      });
      return;
    }

    // 비밀번호 일치 검사
    const passwordMatchValidation = validatePasswordMatch(
      passwordData.newPassword,
      passwordData.confirmPassword
    );
    if (!passwordMatchValidation.isValid) {
      toast({
        title: "비밀번호 불일치",
        description: passwordMatchValidation.errors[0],
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      // 1. 현재 비밀번호 검증
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: profile?.email || "",
        password: passwordData.currentPassword,
      });

      if (signInError) {
        toast({
          title: "현재 비밀번호 오류",
          description: "현재 비밀번호가 올바르지 않습니다.",
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      // 2. 새 비밀번호로 업데이트
      const { error: updateError } = await supabase.auth.updateUser({
        password: passwordData.newPassword,
      });

      if (updateError) throw updateError;

      // 3. 비밀번호 변경 시간 업데이트
      if (profile?.id) {
        const { error: updateTimeError } = await supabase
          .from("profiles")
          .update({
            password_changed_at: new Date().toISOString(),
          })
          .eq("id", profile.id);

        if (updateTimeError) {
          console.error(
            "Failed to update password change time:",
            updateTimeError
          );
        }
      }

      // 4. 성공 메시지 및 상태 초기화
      toast({
        title: "비밀번호가 변경되었습니다.",
        description: "새 비밀번호로 성공적으로 변경되었습니다.",
      });

      setPasswordData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });

      // 5. 시스템 로그 생성
      if (profile?.id) {
        await supabase.rpc("create_system_log", {
          p_event_type: "PASSWORD_CHANGED",
          p_message: "비밀번호가 변경되었습니다.",
          p_severity: "info",
          p_user_id: profile.id,
          p_entity_type: "user",
          p_entity_id: profile.id,
          p_metadata: {
            user_id: profile.id,
            user_email: profile.email,
            timestamp: new Date().toISOString(),
          },
        });
      }
    } catch (error) {
      console.error("Password change error:", error);
      toast({
        title: "비밀번호 변경 실패",
        description: "비밀번호 변경 중 오류가 발생했습니다.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [passwordData, profile?.id, profile?.email, toast]);

  const handleProfileImageUpdate = useCallback(
    (newImageUrl: string) => {
      if (profile) {
        setProfileData((prev) => ({
          ...prev,
          profile_image_url: newImageUrl,
        }));
      }
    },
    [profile]
  );

  return (
    <div className="flex-1 space-y-4 p-4 md:p-6 pt-2 md:pt-4">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/admin/dashboard" className="flex items-center gap-1">
                <Home className="h-4 w-4" />
                대시보드
              </Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>계정 관리</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <SidebarTrigger />
          <div>
            <h2 className="text-3xl font-bold tracking-tight mb-4 flex items-center gap-2">
              <User className="h-8 w-8 text-primary" />
              계정 관리
            </h2>
            <p className="text-muted-foreground mt-2">
              개인 정보 및 회사 정보를 관리하세요
            </p>
          </div>
        </div>
      </div>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="profile">개인 정보</TabsTrigger>
          <TabsTrigger value="company">회사 정보</TabsTrigger>
          <TabsTrigger value="security">보안 설정</TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 mb-4">
                  <User className="h-5 w-5" />
                  개인 정보
                </CardTitle>
                <CardDescription>개인 프로필 정보를 관리합니다</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {profile && (
                  <ProfileImageUpload
                    profileId={profile.id}
                    currentImageUrl={profile.profile_image_url}
                    onImageUpdate={handleProfileImageUpdate}
                  />
                )}
                <Separator />
                <ProfileInfo
                  profileData={profileData}
                  setProfileData={setProfileData}
                  loading={loading}
                  onSave={handleProfileSave}
                />
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>

        <TabsContent value="company" className="space-y-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  회사 정보
                </CardTitle>
                <CardDescription>
                  회사 및 농장 정보를 관리합니다
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CompanyInfo
                  companyData={companyData}
                  setCompanyData={setCompanyData}
                  loading={loading}
                  onSave={handleCompanySave}
                />
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  비밀번호 변경
                </CardTitle>
                <CardDescription>
                  계정 보안을 위해 정기적으로 비밀번호를 변경하세요
                </CardDescription>
              </CardHeader>
              <CardContent>
                <SecuritySettings
                  passwordData={passwordData}
                  setPasswordData={setPasswordData}
                  loading={loading}
                  onSave={handlePasswordChange}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <History className="h-5 w-5" />
                  로그인 기록
                </CardTitle>
                <CardDescription>최근 로그인 활동을 확인하세요</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {loginActivity.map((activity) => (
                    <div
                      key={activity.id}
                      className={`flex items-center justify-between p-3 rounded-lg ${
                        activity.isCurrent
                          ? "bg-primary/10 border border-primary/20"
                          : "bg-muted"
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <activity.icon className="h-5 w-5 text-primary" />
                        <div>
                          <p className="font-medium">{activity.device}</p>
                          <p className="text-sm text-muted-foreground">
                            {activity.location}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {activity.isCurrent && (
                          <span className="flex items-center gap-1 text-xs font-medium text-green-600">
                            <CheckCircle2 className="h-3 w-3" />
                            현재 세션
                          </span>
                        )}
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">
                          {activity.time}
                        </span>
                      </div>
                    </div>
                  ))}

                  {profile?.last_login_at && (
                    <div className="mt-4 p-3 bg-muted/50 rounded-lg">
                      <p className="text-xs text-muted-foreground">
                        마지막 로그인:{" "}
                        {new Date(profile.last_login_at).toLocaleString(
                          "ko-KR"
                        )}
                      </p>
                      {profile.password_changed_at && (
                        <p className="text-xs text-muted-foreground mt-1">
                          비밀번호 변경:{" "}
                          {new Date(profile.password_changed_at).toLocaleString(
                            "ko-KR"
                          )}
                        </p>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
