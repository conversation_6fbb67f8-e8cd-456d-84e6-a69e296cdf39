import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

// 농장 삭제 후 사용자 role 업데이트 함수
async function updateUserRolesAfterFarmDeletion(supabase: any, farmId: string) {
  try {
    console.log(`🔄 농장 삭제 전 사용자 role 업데이트 시작: ${farmId}`);

    // 1. 삭제될 농장의 모든 구성원 조회 (소유자 포함)
    const { data: farmMembers, error: membersError } = await supabase
      .from("farm_members")
      .select("user_id, role")
      .eq("farm_id", farmId);

    if (membersError) {
      console.error("농장 구성원 조회 오류:", membersError);
      return;
    }

    // 2. 농장 소유자 정보도 조회
    const { data: farm, error: farmError } = await supabase
      .from("farms")
      .select("owner_id")
      .eq("id", farmId)
      .single();

    if (farmError) {
      console.error("농장 정보 조회 오류:", farmError);
      return;
    }

    // 3. 모든 관련 사용자 ID 수집 (소유자 + 구성원)
    const allUserIds = new Set([
      farm.owner_id,
      ...(farmMembers?.map((m) => m.user_id) || []),
    ]);

    console.log(`📋 영향받는 사용자 수: ${allUserIds.size}`);

    // 4. 각 사용자별로 다른 농장 관계 확인 및 role 업데이트
    for (const userId of allUserIds) {
      await updateUserRole(supabase, userId, farmId);
    }

    console.log("✅ 사용자 role 업데이트 완료");
  } catch (error) {
    console.error("사용자 role 업데이트 중 오류:", error);
  }
}

// 개별 사용자의 role 업데이트 함수
async function updateUserRole(
  supabase: any,
  userId: string,
  deletingFarmId: string
) {
  try {
    console.log(`👤 사용자 role 업데이트 시작: ${userId}`);

    // 1. 사용자가 소유한 다른 농장 확인
    const { data: ownedFarms, error: ownedError } = await supabase
      .from("farms")
      .select("id")
      .eq("owner_id", userId)
      .neq("id", deletingFarmId);

    if (ownedError) {
      console.error("소유 농장 조회 오류:", ownedError);
      return;
    }

    // 2. 사용자가 구성원으로 속한 다른 농장 확인
    const { data: memberFarms, error: memberError } = await supabase
      .from("farm_members")
      .select("farm_id, role")
      .eq("user_id", userId)
      .neq("farm_id", deletingFarmId);

    if (memberError) {
      console.error("구성원 농장 조회 오류:", memberError);
      return;
    }

    // 3. 새로운 role 결정
    let newRole = "viewer"; // 기본값

    if (ownedFarms && ownedFarms.length > 0) {
      // 다른 농장을 소유하고 있으면 owner
      newRole = "owner";
    } else if (memberFarms && memberFarms.length > 0) {
      // 다른 농장의 구성원이면 해당 농장에서의 최고 권한 role
      const roles = memberFarms.map((m) => m.role);
      if (roles.includes("manager")) {
        newRole = "manager";
      } else if (roles.includes("viewer")) {
        newRole = "viewer";
      }
    }

    // 4. 농장 삭제 시 profiles.role 업데이트는 더 이상 필요하지 않음
    // 새로운 권한 시스템에서는 profiles.account_type은 시스템 레벨 권한만 관리
    // 농장별 권한은 farm_members 테이블에서 관리됨
    console.log(
      `ℹ️ 농장 삭제 완료. 사용자 ${userId}의 농장별 권한은 farm_members에서 관리됩니다.`
    );
  } catch (error) {
    console.error("개별 사용자 role 업데이트 오류:", error);
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: { farmId: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const {
      data: { session },
      error: authError,
    } = await supabase.auth.getSession();

    if (authError || !session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const farmData = await request.json();

    // Verify ownership
    const { data: existingFarm, error: farmCheckError } = await supabase
      .from("farms")
      .select("owner_id")
      .eq("id", params.farmId)
      .single();

    if (farmCheckError || !existingFarm) {
      return NextResponse.json({ error: "Farm not found" }, { status: 404 });
    }

    if (existingFarm.owner_id !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    // Update farm
    const { data: farm, error: updateError } = await supabase
      .from("farms")
      .update(farmData)
      .eq("id", params.farmId)
      .select()
      .single();

    if (updateError) {
      throw updateError;
    }

    return NextResponse.json({ farm });
  } catch (error) {
    console.error("Error updating farm:", error);
    return NextResponse.json(
      { error: "Failed to update farm" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { farmId: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const {
      data: { session },
      error: authError,
    } = await supabase.auth.getSession();

    if (authError || !session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Verify ownership
    const { data: existingFarm, error: farmCheckError } = await supabase
      .from("farms")
      .select("owner_id")
      .eq("id", params.farmId)
      .single();

    if (farmCheckError || !existingFarm) {
      return NextResponse.json({ error: "Farm not found" }, { status: 404 });
    }

    if (existingFarm.owner_id !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    // 1. 농장 삭제 전에 관련 사용자들의 role 업데이트 처리
    await updateUserRolesAfterFarmDeletion(supabase, params.farmId);

    // 2. 농장 삭제 (CASCADE로 farm_members도 자동 삭제됨)
    const { error: deleteError } = await supabase
      .from("farms")
      .delete()
      .eq("id", params.farmId);

    if (deleteError) {
      throw deleteError;
    }

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error("Error deleting farm:", error);
    return NextResponse.json(
      { error: "Failed to delete farm" },
      { status: 500 }
    );
  }
}
