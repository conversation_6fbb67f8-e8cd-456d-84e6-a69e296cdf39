import * as z from "zod";
import { FARM_TYPES } from "@/lib/constants/farm-types";

export const farmFormSchema = z.object({
  farm_name: z
    .string()
    .min(2, { message: "농장명은 최소 2자 이상이어야 합니다." })
    .max(50, { message: "농장명은 최대 50자까지 가능합니다." }),
  farm_address: z
    .string()
    .min(5, { message: "주소를 입력해주세요." })
    .max(200, { message: "주소가 너무 깁니다." }),
  farm_detailed_address: z
    .string()
    .max(100, { message: "상세 주소는 최대 100자까지 가능합니다." })
    .optional()
    .nullable()
    .default(null),
  farm_type: z.enum(Object.values(FARM_TYPES) as [string, ...string[]], {
    required_error: "농장 유형을 선택해주세요.",
  }),
  description: z
    .string()
    .max(500, { message: "설명은 최대 500자까지 가능합니다." })
    .optional()
    .nullable()
    .default(null),
  manager_name: z
    .string()
    .min(2, { message: "관리자명은 최소 2자 이상이어야 합니다." })
    .max(50, { message: "관리자명은 최대 50자까지 가능합니다." }),
  manager_phone: z
    .string()
    .min(10, { message: "연락처를 정확히 입력해주세요." })
    .max(20, { message: "연락처는 최대 20자까지 가능합니다." })
    .regex(
      /^01([0|1|6|7|8|9])-?([0-9]{3,4})-?([0-9]{4})$/,
      "올바른 휴대폰 번호를 입력해주세요. (예: 010-1234-5678)"
    ),
});

export type FarmFormValues = z.infer<typeof farmFormSchema>;
