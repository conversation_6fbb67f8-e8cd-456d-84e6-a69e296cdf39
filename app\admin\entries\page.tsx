"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useFarms } from "@/hooks/use-farms";
import { useVisitorEntries } from "@/hooks/use-visitor-entries";
import {
  Download,
  Search,
  Filter,
  Calendar,
  Users,
  Clock,
  MapPin,
  Phone,
  Car,
} from "lucide-react";
import { motion } from "framer-motion";

export default function EntriesPage() {
  const { farms, loading: farmsLoading } = useFarms();
  const { entries, loading: entriesLoading } = useVisitorEntries();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedFarm, setSelectedFarm] = useState<string>("all");
  const [dateFilter, setDateFilter] = useState<string>("");
  const [purposeFilter, setPurposeFilter] = useState<string>("all");

  // 농장 정보와 함께 방문자 데이터 조합
  const entriesWithFarm = entries.map((entry) => {
    console.log("test : " + entry);
    const farm = farms.find((f) => f.id === entry.farm_id);

    return {
      ...entry,
      farm_name: farm?.farm_name || "알 수 없는 농장",
      farm_address: farm?.farm_address || "",
    };
  });

  // 필터링된 방문자 기록
  const filteredEntries = entriesWithFarm.filter((entry) => {
    const matchesSearch =
      entry.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      entry.phone_number.includes(searchTerm) ||
      entry.car_plate_number
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      entry.farm_name.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFarm =
      selectedFarm === "all" || entry.farm_id === selectedFarm;

    const matchesDate =
      !dateFilter || entry.entry_datetime.startsWith(dateFilter);

    const matchesPurpose =
      purposeFilter === "all" || entry.visit_purpose === purposeFilter;

    return matchesSearch && matchesFarm && matchesDate && matchesPurpose;
  });

  // CSV 내보내기
  const exportToCSV = () => {
    const headers = [
      "날짜 및 시간",
      "농장명",
      "방문자명",
      "전화번호",
      "주소",
      "차량번호",
      "방문목적",
      "소독여부",
      "메모",
    ];

    const csvData = filteredEntries.map((entry) => [
      new Date(entry.entry_datetime).toLocaleString("ko-KR"),
      entry.farm_name,
      entry.full_name,
      entry.phone_number,
      entry.address,
      entry.car_plate_number || "",
      entry.visit_purpose,
      entry.disinfection_check ? "완료" : "미완료",
      entry.notes || "",
    ]);

    const csvContent = [headers, ...csvData]
      .map((row) => row.map((field) => `"${field}"`).join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `방문자기록-${new Date().toISOString().split("T")[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);

    alert("방문자 기록이 CSV 파일로 내보내졌습니다.");
  };

  // 방문 목적 목록 추출
  const visitPurposes = Array.from(
    new Set(entries.map((entry) => entry.visit_purpose).filter(Boolean))
  );

  if (farmsLoading || entriesLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>방문자 기록을 불러오는 중...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <SidebarTrigger />
          <div>
            <h2 className="text-3xl font-bold tracking-tight">방문자 기록</h2>
            <p className="text-muted-foreground">
              모든 농장의 방문자 기록을 확인하고 관리하세요
            </p>
          </div>
        </div>
        <Button onClick={exportToCSV} disabled={filteredEntries.length === 0}>
          <Download className="mr-2 h-4 w-4" />
          CSV 내보내기
        </Button>
      </div>

      {/* 통계 카드 */}
      <div className="grid gap-4 md:grid-cols-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">전체 방문자</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">
                {entries.length}
              </div>
              <p className="text-xs text-muted-foreground">총 방문 기록</p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">오늘 방문자</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">
                {
                  entries.filter((e) =>
                    e.entry_datetime.startsWith(
                      new Date().toISOString().split("T")[0]
                    )
                  ).length
                }
              </div>
              <p className="text-xs text-muted-foreground">
                오늘 등록된 방문자
              </p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">소독 완료율</CardTitle>
              <Badge className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">
                {entries.length > 0
                  ? Math.round(
                      (entries.filter((e) => e.disinfection_check).length /
                        entries.length) *
                        100
                    )
                  : 0}
                %
              </div>
              <p className="text-xs text-muted-foreground">방역 수칙 준수율</p>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                필터링된 결과
              </CardTitle>
              <Filter className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">
                {filteredEntries.length}
              </div>
              <p className="text-xs text-muted-foreground">현재 필터 조건</p>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* 필터 섹션 */}
      <Card>
        <CardHeader>
          <CardTitle>검색 및 필터</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="방문자명, 전화번호, 차량번호, 농장명 검색..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={selectedFarm} onValueChange={setSelectedFarm}>
              <SelectTrigger>
                <SelectValue placeholder="농장 선택" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">모든 농장</SelectItem>
                {farms.map((farm) => (
                  <SelectItem key={farm.id} value={farm.id}>
                    {farm.farm_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={purposeFilter} onValueChange={setPurposeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="방문 목적" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">모든 목적</SelectItem>
                {visitPurposes.map((purpose) => (
                  <SelectItem key={purpose} value={purpose}>
                    {purpose}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="date"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                className="pl-10"
              />
            </div>

            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm("");
                setSelectedFarm("all");
                setPurposeFilter("all");
                setDateFilter("");
              }}
            >
              <Filter className="mr-2 h-4 w-4" />
              필터 초기화
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 방문자 기록 테이블 */}
      <Card>
        <CardHeader>
          <CardTitle>방문자 기록 ({filteredEntries.length}건)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>날짜 및 시간</TableHead>
                  <TableHead>농장</TableHead>
                  <TableHead>방문자 정보</TableHead>
                  <TableHead>연락처</TableHead>
                  <TableHead>차량</TableHead>
                  <TableHead>방문 목적</TableHead>
                  <TableHead>소독 여부</TableHead>
                  <TableHead>메모</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredEntries.map((entry, index) => (
                  <motion.tr
                    key={entry.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="hover:bg-muted/50"
                  >
                    <TableCell className="font-medium">
                      <div className="flex flex-col">
                        <span>
                          {new Date(entry.entry_datetime).toLocaleDateString(
                            "ko-KR"
                          )}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {new Date(entry.entry_datetime).toLocaleTimeString(
                            "ko-KR",
                            {
                              hour: "2-digit",
                              minute: "2-digit",
                            }
                          )}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">{entry.farm_name}</span>
                        <span className="text-xs text-muted-foreground flex items-center">
                          <MapPin className="h-3 w-3 mr-1" />
                          {entry.farm_address.split(" ").slice(0, 2).join(" ")}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">{entry.full_name}</span>
                        <span className="text-xs text-muted-foreground">
                          {entry.address}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Phone className="h-3 w-3 mr-1 text-muted-foreground" />
                        <span className="text-sm">{entry.phone_number}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {entry.car_plate_number ? (
                        <div className="flex items-center">
                          <Car className="h-3 w-3 mr-1 text-muted-foreground" />
                          <Badge variant="outline">
                            {entry.car_plate_number}
                          </Badge>
                        </div>
                      ) : (
                        <span className="text-muted-foreground text-sm">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">{entry.visit_purpose}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          entry.disinfection_check ? "default" : "destructive"
                        }
                      >
                        {entry.disinfection_check ? "완료" : "미완료"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {entry.notes ? (
                        <span
                          className="text-sm text-muted-foreground max-w-[150px] truncate block"
                          title={entry.notes}
                        >
                          {entry.notes}
                        </span>
                      ) : (
                        <span className="text-muted-foreground text-sm">-</span>
                      )}
                    </TableCell>
                  </motion.tr>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredEntries.length === 0 && (
            <div className="text-center py-12">
              <Users className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
              <h3 className="text-lg font-semibold mb-2">
                검색 결과가 없습니다
              </h3>
              <p className="text-muted-foreground">
                검색 조건을 변경하거나 필터를 초기화해보세요.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
