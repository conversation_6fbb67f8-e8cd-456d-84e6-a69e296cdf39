# 🔧 브라우저 캐시 문제 해결 가이드

## 📋 문제 상황
프로필 이미지를 새로 업로드했는데도 이전 이미지가 계속 표시되는 경우

## 🎯 해결 방법

### 1. **자동 해결 (애플리케이션 레벨)**

#### ✅ 이미 구현된 기능들:
- **타임스탬프 캐시 무효화**: 모든 이미지 URL에 `?t=timestamp` 추가
- **브라우저 캐시 정리**: Service Worker 캐시 자동 삭제
- **스토리지 정리**: 로컬/세션 스토리지의 이미지 관련 데이터 제거
- **강제 리렌더링**: React key 속성을 통한 컴포넌트 강제 업데이트

### 2. **수동 해결 (사용자 레벨)**

#### 🔄 브라우저 새로고침
```
Ctrl + F5 (Windows) / Cmd + Shift + R (Mac)
```
- 하드 리프레시로 모든 캐시 무시

#### 🧹 브라우저 캐시 삭제
1. **Chrome/Edge**:
   - F12 → Network 탭 → "Disable cache" 체크
   - 또는 설정 → 개인정보 및 보안 → 인터넷 사용 기록 삭제

2. **Firefox**:
   - F12 → Network 탭 → 설정 아이콘 → "Disable cache" 체크

3. **Safari**:
   - 개발자 → 캐시 비우기

#### 🔍 개발자 도구 활용
1. F12로 개발자 도구 열기
2. Network 탭에서 "Disable cache" 활성화
3. 페이지 새로고침

### 3. **개발자 디버깅**

#### 📊 콘솔 로그 확인
```javascript
// 이미지 로드 상태 확인
🖼️ Profile image URL changed: [URL]
✅ Profile image preview set to: [URL with timestamp]
🖼️ Profile image loaded: [URL]

// 캐시 정리 확인
🧹 Image cache cleared from storage
```

#### 🔍 네트워크 탭 확인
- 이미지 요청이 캐시에서 오는지 서버에서 오는지 확인
- Status가 "200" (서버에서 새로 받음) vs "304" (캐시 사용)

#### 📱 Application 탭 확인
- Storage → Local Storage / Session Storage에서 이미지 관련 데이터 확인
- Cache Storage에서 이미지 캐시 상태 확인

### 4. **예방 조치**

#### ⚙️ 개발 환경 설정
```javascript
// Next.js에서 이미지 캐시 비활성화
module.exports = {
  images: {
    unoptimized: true,
  },
  async headers() {
    return [
      {
        source: '/api/storage/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
        ],
      },
    ];
  },
};
```

#### 🔧 Supabase Storage 설정
```sql
-- Storage 정책에 캐시 제어 헤더 추가
ALTER TABLE storage.objects 
ADD COLUMN cache_control TEXT DEFAULT 'no-cache';
```

### 5. **문제 진단 체크리스트**

#### ✅ 확인 사항:
- [ ] 브라우저 개발자 도구에서 "Disable cache" 활성화
- [ ] 콘솔에서 이미지 URL에 타임스탬프가 포함되어 있는지 확인
- [ ] Network 탭에서 이미지 요청이 실제로 서버에서 오는지 확인
- [ ] 다른 브라우저나 시크릿 모드에서도 같은 문제가 발생하는지 확인
- [ ] 데이터베이스에서 profile_image_url이 올바르게 업데이트되었는지 확인

#### 🚨 긴급 해결:
```javascript
// 브라우저 콘솔에서 실행
// 모든 이미지 강제 새로고침
document.querySelectorAll('img').forEach(img => {
  const src = img.src;
  img.src = '';
  img.src = src + '?t=' + Date.now();
});

// 모든 캐시 삭제
if ('caches' in window) {
  caches.keys().then(names => {
    names.forEach(name => caches.delete(name));
  });
}
```

## 🎉 최종 확인

이미지가 정상적으로 업데이트되면:
- 새로운 이미지가 즉시 표시됨
- 콘솔에 "🖼️ Profile image loaded" 메시지 출력
- Network 탭에서 새로운 이미지 요청 확인 가능
